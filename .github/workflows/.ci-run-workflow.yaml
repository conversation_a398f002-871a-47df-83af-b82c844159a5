name: Select and Run Workflow

on:
  workflow_dispatch:
    inputs:
      target_workflow:
        description: 'Select workflow to run'
        required: true
        type: choice
        options:
          - ci-lms-api-docker-image.yml
          - ci-lms-client-docker-image.yml
          - ci-lms-worker-docker-image.yml
          - ci-lms-task-worker-docker-image.yml
          - ci-lms-scheduler-worker-docker-image.yml
          - ci-lms-admin-api-docker-image.yml
          - ci-lms-admin-client-docker-image.yml
          - ci-notification-docker-image.yml
      branch:
        description: 'Branch to run on'
        required: true
        type: choice
        options:
          - con-ed-dev
          - con-ed-release
          - con-ed-master

jobs:
  trigger-workflow:
    runs-on: skl-runner
    permissions:
      actions: write
      contents: read
    
    steps:
      - name: Trigger selected workflow
        uses: actions/github-script@v7
        with:
          github-token: ${{ secrets.GITHUB_TOKEN }}
          script: |
            const workflow_id = '${{ github.event.inputs.target_workflow }}';
            const branch = '${{ github.event.inputs.branch }}';
            
            console.log(`Triggering workflow: ${workflow_id} on ${branch}`);
            
            await github.rest.actions.createWorkflowDispatch({
              owner: context.repo.owner,
              repo: context.repo.repo,
              workflow_id: workflow_id,
              ref: branch,
              inputs: {
                triggered_by: 'workflow-selector',
                original_ref: branch
              }
            });

      - name: Check workflow status
        uses: actions/github-script@v7
        with:
          github-token: ${{ secrets.GITHUB_TOKEN }}
          script: |
            const delay = ms => new Promise(resolve => setTimeout(resolve, ms));
            
            // Wait for workflow to start (max 30 seconds)
            for (let i = 0; i < 6; i++) {
              const runs = await github.rest.actions.listWorkflowRuns({
                owner: context.repo.owner,
                repo: context.repo.repo,
                workflow_id: '${{ github.event.inputs.target_workflow }}',
                branch: '${{ github.event.inputs.branch }}',
                per_page: 1
              });
              
              if (runs.data.total_count > 0) {
                const run = runs.data.workflow_runs[0];
                console.log(`Workflow started: ${run.html_url}`);
                break;
              }
              
              await delay(5000); // Wait 5 seconds before checking again
            }