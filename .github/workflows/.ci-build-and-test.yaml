name: LMS Monorepo CI Build & Test
on:
  pull_request:
    types: [opened, reopened, synchronize, ready_for_review]
    branches:
      - con-ed-release
      - con-ed-master
      - con-ed-dev

jobs:
  set-up:
    if: github.event.pull_request.draft == false
    runs-on: skl-runner
    steps:
      - name: Checkout source code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - uses: dorny/paths-filter@v2
        id: changes-dependencies
        with:
          filters: |
            dependency:
              - yarn.lock
              - package.json
              - packages/cpd/api/package.json
              - packages/cpd/client/package.json
              - packages/cpd/worker/package.json
              - packages/notification/package.json
              - packages/lms-admin/api/package.json
              - packages/lms-admin/client/package.json

      - name: Set up pre script
        uses: ./.github/packages/actions/setupPreScript

      - name: Set up node js and package dependencies
        uses: ./.github/packages/actions/setUpNodePackageAndDependencies
        with:
          skip-install-cached: 'true'

  pre-build-frontend:
    if: github.event.pull_request.draft == false
    runs-on: skl-runner
    needs: [set-up]
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - uses: dorny/paths-filter@v2
        id: changes-src-client
        with:
          filters: |
            src:
              - packages/cpd/client/src/**
              - shared/**

      - name: Set up node js and package dependencies
        if: steps.changes-src-client.outputs.src == 'true'
        uses: ./.github/packages/actions/setUpNodePackageAndDependencies

      - name: Pre Build Shared Package
        if: steps.changes-src-client.outputs.src == 'true'
        run: pnpm run build:iso-shared

      - name: Pre Build LMS Model
        if: steps.changes-src-client.outputs.src == 'true'
        run: pnpm run build:iso-lms-model

      - name: Run tests
        if: steps.changes-src-client.outputs.src == 'true'
        run: pnpm run pre-test:cpd-client

      - name: Build check
        if: steps.changes-src-client.outputs.src == 'true'
        run: pnpm run build-check:client

      - name: Run transform-tokens AntD Design Token
        if: steps.changes-src-client.outputs.src == 'true'
        run: LICENSE_KEY=${{ secrets.DESIGN_TOKEN_KEY }} LICENSE_EMAIL=${{ secrets.DESIGN_TOKEN_EMAIL }} pnpm run build-design-token

      - name: SonarQube Analysis
        if: steps.changes-src-client.outputs.src == 'true'
        uses: sonarsource/sonarqube-scan-action@master
        with:
          projectBaseDir: packages/cpd/client

  pre-build-backend:
    if: github.event.pull_request.draft == false
    runs-on: skl-runner
    needs: [set-up]
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - uses: dorny/paths-filter@v2
        id: changes-src-api
        with:
          filters: |
            src:
              - packages/cpd/api/src/**

      - name: Set up node js and package dependencies
        if: steps.changes-src-api.outputs.src == 'true'
        uses: ./.github/packages/actions/setUpNodePackageAndDependencies

      - name: Pre Build Shared Package
        if: steps.changes-src-api.outputs.src == 'true'
        shell: bash
        run: pnpm run build:iso-shared

      - name: Pre Build LMS Model
        if: steps.changes-src-api.outputs.src == 'true'
        run: pnpm run build:iso-lms-model

      - name: Run tests
        if: steps.changes-src-api.outputs.src == 'true'
        shell: bash
        run: pnpm run test:cpd-api

      - name: Build check
        if: steps.changes-src-api.outputs.src == 'true'
        shell: bash
        run: pnpm run build-check:api

      - name: SonarQube Analysis
        if: steps.changes-src-api.outputs.src == 'true'
        uses: sonarsource/sonarqube-scan-action@master
        with:
          projectBaseDir: packages/cpd/api

  pre-build-worker:
    if: github.event.pull_request.draft == false
    runs-on: skl-runner
    needs: [set-up]
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - uses: dorny/paths-filter@v2
        id: changes-src-worker
        with:
          filters: |
            src:
              - packages/cpd/worker/src/**

      - name: Set up node js and package dependencies
        if: steps.changes-src-worker.outputs.src == 'true'
        uses: ./.github/packages/actions/setUpNodePackageAndDependencies

      - name: Pre Build Shared Package
        if: steps.changes-src-worker.outputs.src == 'true'
        run: pnpm run build:iso-shared

      - name: Pre Build LMS Model
        if: steps.changes-src-worker.outputs.src == 'true'
        run: pnpm run build:iso-lms-model

      - name: Run tests
        if: steps.changes-src-worker.outputs.src == 'true'
        run: pnpm run test:cpd-worker

      - name: Build check
        if: steps.changes-src-worker.outputs.src == 'true'
        run: pnpm run build:cpd-worker

      - name: SonarQube Analysis
        if: steps.changes-src-worker.outputs.src == 'true'
        uses: sonarsource/sonarqube-scan-action@master
        with:
          projectBaseDir: packages/cpd/worker

  pre-build-notification:
    if: github.event.pull_request.draft == false
    runs-on: skl-runner
    needs: [set-up]
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - uses: dorny/paths-filter@v2
        id: changes-src-notification
        with:
          filters: |
            src:
              - packages/notification/src/**

      - name: Set up node js and package dependencies
        if: steps.changes-src-notification.outputs.src == 'true'
        uses: ./.github/packages/actions/setUpNodePackageAndDependencies

      - name: Pre Build Shared Constants Package
        if: steps.changes-src-notification.outputs.src == 'true'
        run: pnpm run build:iso-constants

      - name: Run tests
        if: steps.changes-src-notification.outputs.src == 'true'
        run: pnpm run test:notification

      - name: Build check
        if: steps.changes-src-notification.outputs.src == 'true'
        run: pnpm run build-check:notification

      - name: SonarQube Analysis
        if: steps.changes-src-notification.outputs.src == 'true'
        uses: sonarsource/sonarqube-scan-action@master
        with:
          projectBaseDir: packages/notification

  pre-build-lms-admin-client:
    if: github.event.pull_request.draft == false
    runs-on: skl-runner
    needs: [set-up]
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - uses: dorny/paths-filter@v2
        id: changes-src-lms-admin-client
        with:
          filters: |
            src:
              - packages/lms-admin/client/src/**
              - shared/**

      - name: Set up node js and package dependencies
        if: steps.changes-src-lms-admin-client.outputs.src == 'true'
        uses: ./.github/packages/actions/setUpNodePackageAndDependencies

      - name: Pre Build Shared Package
        if: steps.changes-src-lms-admin-client.outputs.src == 'true'
        run: pnpm run build:iso-shared

      - name: Pre Build LMS Model
        if: steps.changes-src-lms-admin-client.outputs.src == 'true'
        run: pnpm run build:iso-lms-model

      - name: Run tests
        if: steps.changes-src-lms-admin-client.outputs.src == 'true'
        run: pnpm run pre-test:lms-admin-client

      - name: Build check
        if: steps.changes-src-lms-admin-client.outputs.src == 'true'
        run: pnpm run build-check:lms-admin-client

      - name: Run transform-tokens
        if: steps.changes-src-lms-admin-client.outputs.src == 'true'
        run: LICENSE_KEY=${{ secrets.DESIGN_TOKEN_KEY }} LICENSE_EMAIL=${{ secrets.DESIGN_TOKEN_EMAIL }} pnpm run build-design-token:lms-admin-client

      - name: SonarQube Analysis
        if: steps.changes-src-lms-admin-client.outputs.src == 'true'
        uses: sonarsource/sonarqube-scan-action@master
        with:
          projectBaseDir: packages/lms-admin/client

  pre-build-lms-admin-api:
    if: github.event.pull_request.draft == false
    runs-on: skl-runner
    needs: [set-up]
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - uses: dorny/paths-filter@v2
        id: changes-src-lms-admin-api
        with:
          filters: |
            src:
              - packages/lms-admin/api/src/**

      - name: Set up node js and package dependencies
        if: steps.changes-src-lms-admin-api.outputs.src == 'true'
        uses: ./.github/packages/actions/setUpNodePackageAndDependencies

      - name: Pre Build Shared Package
        if: steps.changes-src-lms-admin-api.outputs.src == 'true'
        shell: bash
        run: pnpm run build:iso-shared

      - name: Pre Build LMS Model
        if: steps.changes-src-lms-admin-api.outputs.src == 'true'
        run: pnpm run build:iso-lms-model

      - name: Run tests
        if: steps.changes-src-lms-admin-api.outputs.src == 'true'
        shell: bash
        run: pnpm run test:lms-admin-api

      - name: Build check
        if: steps.changes-src-lms-admin-api.outputs.src == 'true'
        shell: bash
        run: pnpm run build-check:lms-admin-api

      - name: SonarQube Analysis
        if: steps.changes-src-lms-admin-api.outputs.src == 'true'
        uses: sonarsource/sonarqube-scan-action@master
        with:
          projectBaseDir: packages/lms-admin/api

  pre-build-task-worker:
    if: github.event.pull_request.draft == false
    runs-on: skl-runner
    needs: [set-up]
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - uses: dorny/paths-filter@v2
        id: changes-src-task-worker
        with:
          filters: |
            src:
              - packages/cpd/task-worker/src/**

      - name: Set up node js and package dependencies
        if: steps.changes-src-task-worker.outputs.src == 'true'
        uses: ./.github/packages/actions/setUpNodePackageAndDependencies

      - name: Pre Build Shared Package
        if: steps.changes-src-task-worker.outputs.src == 'true'
        run: pnpm run build:iso-shared

      - name: Pre Build LMS Model
        if: steps.changes-src-task-worker.outputs.src == 'true'
        run: pnpm run build:iso-lms-model

      - name: Run tests
        if: steps.changes-src-task-worker.outputs.src == 'true'
        run: pnpm run test:task-worker

      - name: Build check
        if: steps.changes-src-task-worker.outputs.src == 'true'
        run: pnpm run build-check:lms-task-worker

      - name: SonarQube Analysis
        if: steps.changes-src-task-worker.outputs.src == 'true'
        uses: sonarsource/sonarqube-scan-action@master
        with:
          projectBaseDir: packages/cpd/task-worker

  pre-build-scheduler-worker:
    if: github.event.pull_request.draft == false
    runs-on: skl-runner
    needs: [set-up]
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - uses: dorny/paths-filter@v2
        id: changes-src-scheduler-worker
        with:
          filters: |
            src:
              - packages/cpd/scheduler-worker/src/**

      - name: Set up node js and package dependencies
        if: steps.changes-src-scheduler-worker.outputs.src == 'true'
        uses: ./.github/packages/actions/setUpNodePackageAndDependencies

      - name: Pre Build Shared Package
        if: steps.changes-src-scheduler-worker.outputs.src == 'true'
        run: pnpm run build:iso-shared

      - name: Pre Build LMS Model
        if: steps.changes-src-scheduler-worker.outputs.src == 'true'
        run: pnpm run build:iso-lms-model

      - name: Run tests
        if: steps.changes-src-scheduler-worker.outputs.src == 'true'
        run: pnpm run test:scheduler-worker

      - name: Build check
        if: steps.changes-src-scheduler-worker.outputs.src == 'true'
        run: pnpm run build-check:lms-scheduler-worker

      - name: SonarQube Analysis
        if: steps.changes-src-scheduler-worker.outputs.src == 'true'
        uses: sonarsource/sonarqube-scan-action@master
        with:
          projectBaseDir: packages/cpd/scheduler-worker

  pre-build-lms-ecommerce-api:
    if: github.event.pull_request.draft == false
    runs-on: skl-runner
    needs: [set-up]
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - uses: dorny/paths-filter@v2
        id: changes-src-lms-ecommerce-api
        with:
          filters: |
            src:
              - packages/lms-ecommerce/api/**

      - name: Set up node js and package dependencies
        if: steps.changes-src-lms-ecommerce-api.outputs.src == 'true'
        uses: ./.github/packages/actions/setUpNodePackageAndDependencies

      - name: Run Test
        if: steps.changes-src-lms-ecommerce-api.outputs.src == 'true'
        run: pnpm run test:lms-ecommerce-api

      - name: Run Build Check
        if: steps.changes-src-lms-ecommerce-api.outputs.src == 'true'
        run: pnpm run build-check:lms-ecommerce-api

  pre-build-lms-ecommerce-client:
    if: github.event.pull_request.draft == false
    runs-on: skl-runner
    needs: [set-up]
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - uses: dorny/paths-filter@v2
        id: changes-src-lms-ecommerce-client
        with:
          filters: |
            src:
              - packages/lms-ecommerce/web/**

      - name: Set up node js and package dependencies
        if: steps.changes-src-lms-ecommerce-client.outputs.src == 'true'
        uses: ./.github/packages/actions/setUpNodePackageAndDependencies

      - name: Run Test
        if: steps.changes-src-lms-ecommerce-client.outputs.src == 'true'
        run: pnpm run test:lms-ecommerce-client

      - name: Run Build Check
        if: steps.changes-src-lms-ecommerce-client.outputs.src == 'true'
        run: pnpm run build-check:lms-ecommerce-client
