name: Cloud build Task Worker to image registry

on:
  pull_request:
    types: [closed]
    branches:
      - con-ed-dev
      - con-ed-release
      - con-ed-master
    paths:
      - 'docker/Dockerfile.task-worker'
      - 'packages/cpd/task-worker/**'
      - '.github/workflows/ci-lms-task-worker-docker-image.yaml'
      - 'shared/**'

env:
  APP_NAME: skilllane-lms-task-worker
  APP_ENV: ''
  PROJECT_ID: /skilllane-platform
  REGISTRY: asia-southeast1-docker.pkg.dev
  DOCKER_IMAGE: /lms/task-worker
  DOCKER_IMAGE_TAG: 'latest'
  RUN_SA_KEY: RUN_SA_KEY

jobs:
  setup-build-deploy:
    if: github.event.pull_request.merged == true
    name: Setup, Build, and Deploy
    runs-on: skl-runner

    # Add "id-token" with the intended permissions.
    permissions:
      contents: 'read'
      id-token: 'write'

    steps:
      - name: Checkout
        uses: actions/checkout@v3
        with:
          fetch-depth: 0

      - id: 'auth'
        uses: 'google-github-actions/auth@v2'
        with:
          credentials_json: '${{ secrets[env.RUN_SA_KEY] }}'

      - name: Set up Cloud SDK
        uses: google-github-actions/setup-gcloud@v2

      - name: 'Docker auth'
        run: |-
          gcloud auth configure-docker ${{ env.REGISTRY }} --quiet

      - name: Set Docker image tag
        run: |
          if [[ ${{ github.ref }} == 'refs/heads/con-ed-dev' ]]; then
            echo "Setting environment variables for develop branch"
            echo "DOCKER_IMAGE_TAG=dev" >> $GITHUB_ENV
            echo "APP_ENV=lms-dev" >> $GITHUB_ENV
          fi

          if [[ ${{ github.ref }} == 'refs/heads/con-ed-release' ]]; then
            echo "Setting environment variables for release branch"
            echo "DOCKER_IMAGE_TAG=staging" >> $GITHUB_ENV
            echo "APP_ENV=lms-staging" >> $GITHUB_ENV
          fi

          if [[ ${{ github.ref }} == 'refs/heads/con-ed-master' ]]; then
            echo "Setting environment variables for master branch"
            echo "DOCKER_IMAGE_TAG=pre-prod" >> $GITHUB_ENV
            echo "APP_ENV=lms-pre-prod" >> $GITHUB_ENV
          fi

      - name: Build and push image
        uses: docker/build-push-action@v5
        with:
          context: .
          file: './docker/Dockerfile.task-worker'
          push: true
          tags: '${{ env.REGISTRY }}${{ env.PROJECT_ID }}${{ env.DOCKER_IMAGE }}:${{ env.DOCKER_IMAGE_TAG }}'
          build-args: |
            ENV_VARIABLE=${{ env.VAULT }}

      - name: Deploy
        uses: peter-evans/repository-dispatch@v2
        with:
          token: ${{ secrets.PAT }}
          repository: SkillLane/skilllane-non-prod-infra
          event-type: deployment_update
          client-payload: '{"APP_NAME": "${{ env.APP_NAME }}", "APP_ENV": "${{ env.APP_ENV }}", "BUILD_NUMBER": "${{ github.run_number }}"}'

      - name: SonarQube Analysis
        uses: sonarsource/sonarqube-scan-action@master
        with:
          projectBaseDir: packages/cpd/task-worker
          args: |
            -Dsonar.qualitygate.wait=false
