name: Build Tag Production LMS Worker

on:
  push:
    tags:
      - 'v*.*.*-lms'
    paths:
      - 'docker/Dockerfile.worker'
      - 'packages/cpd/worker/**'
      - '*'

env:
  PROJECT_ID: /skilllane-coned-platform
  REGISTRY: asia-southeast1-docker.pkg.dev
  DOCKER_IMAGE: /lms/worker
  DOCKER_IMAGE_TAG: ''
  GCP_SA_KEY: LMS_GCP_SA_KEY

jobs:
  setup-build-deploy:
    name: Setup, Build & Push Image
    runs-on: skl-runner

    steps:
      - name: Checkout
        uses: actions/checkout@v3

      - id: 'auth'
        uses: 'google-github-actions/auth@v2'
        with:
          credentials_json: '${{ secrets[env.GCP_SA_KEY] }}'

      - name: Set up Cloud SDK
        uses: google-github-actions/setup-gcloud@v2

      - name: "Docker auth"
        run: |-
          gcloud auth configure-docker ${{ env.REGISTRY }} --quiet

      - name: Set Docker image tag
        run: |
          echo "DOCKER_IMAGE_TAG=${{ github.ref_name }}" >> $GITHUB_ENV

      - name: Build and push image
        uses: docker/build-push-action@v5
        with:
          context: .
          file: './docker/Dockerfile.worker'
          push: true
          tags: '${{ env.REGISTRY }}${{ env.PROJECT_ID }}${{ env.DOCKER_IMAGE }}:${{ env.DOCKER_IMAGE_TAG }}'
          build-args: |
            ENV_VARIABLE=${{ env.VAULT }}
