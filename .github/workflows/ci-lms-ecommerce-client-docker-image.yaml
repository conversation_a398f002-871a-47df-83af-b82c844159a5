name: Cloud build LMS Ecommerce Client to image registry

on:
  pull_request:
    types: [closed]
    branches:
      # Open When Client go to dev and staging
      # - con-ed-dev
      # - con-ed-release
      - con-ed-master
    paths:
      - "docker/Dockerfile.lms-ecommerce-client"
      - "packages/lms-ecommerce/client/**"
      - ".github/workflows/ci-lms-ecommerce-client-docker-image.yaml"
      - "*"

env:
  APP_NAME: skilllane-lms-ecommerce-web
  APP_ENV: ""
  PROJECT_ID: /skilllane-platform
  REGISTRY: asia-southeast1-docker.pkg.dev
  DOCKER_IMAGE: /lms/ecommerce-web
  DOCKER_IMAGE_TAG: "latest"
  RUN_SA_KEY: RUN_SA_KEY

  VAULT_URL_NONE_PROD: SKL_VAULT_URL
  VAULT_TOKEN_NON_PROD: SKL_VAULT_TOKEN
  JQ_JSON_NONE_PROD: .data.data

  SECRET_PATH_DEV: secret/data/lms-dev/skilllane-lms-ecommerce-web
  SECRET_PATH_STAGING: secret/data/lms-staging/skilllane-lms-ecommerce-web
  SECRET_PATH_PRE_PROD: secret/data/lms-pre-prod/skilllane-lms-ecommerce-web

jobs:
  setup-build-deploy:
    if: github.event.pull_request.merged == true
    name: Setup, Build, and Deploy
    runs-on: skl-runner

    steps:
      - name: Set environment variables
        run: |
          echo "VAULT_URL=${{ env.VAULT_URL_NONE_PROD}}" >> $GITHUB_ENV
          echo "VAULT_TOKEN=${{ env.VAULT_TOKEN_NON_PROD}}" >> $GITHUB_ENV
          echo "JQ_JSON=${{ env.JQ_JSON_NONE_PROD}}" >> $GITHUB_ENV

          if [[ ${{ github.ref }} == 'refs/heads/con-ed-dev' ]]; then
            echo "Setting environment variables for develop branch"
            echo "BUILD_ENVIRONMENT=con-ed-dev" >> $GITHUB_ENV
            echo "VAULT_SECRET_PATH=${{ env.SECRET_PATH_DEV}}" >> $GITHUB_ENV
            echo "APP_ENV=lms-dev" >> $GITHUB_ENV
          fi

          if [[ ${{ github.ref }} == 'refs/heads/con-ed-release' ]]; then
            echo "Setting environment variables for release branch"
            echo "BUILD_ENVIRONMENT=con-ed-release" >> $GITHUB_ENV
            echo "VAULT_SECRET_PATH=${{ env.SECRET_PATH_STAGING}}" >> $GITHUB_ENV
            echo "APP_ENV=lms-staging" >> $GITHUB_ENV
          fi

          if [[ ${{ github.ref }} == 'refs/heads/con-ed-master' ]]; then
            echo "Setting environment variables for master branch"
            echo "BUILD_ENVIRONMENT=con-ed-master" >> $GITHUB_ENV
            echo "VAULT_SECRET_PATH=${{ env.SECRET_PATH_PRE_PROD}}" >> $GITHUB_ENV
            echo "APP_ENV=lms-pre-prod" >> $GITHUB_ENV
          fi

      - name: Checkout
        uses: actions/checkout@v3
        with:
          fetch-depth: 0

      - id: "auth"
        uses: "google-github-actions/auth@v2"
        with:
          credentials_json: "${{ secrets[env.RUN_SA_KEY] }}"

      - name: "Set up Cloud SDK"
        uses: "google-github-actions/setup-gcloud@v2"

      - name: Install curl, jq
        run: |
          sudo apt-get update
          sudo apt-get install curl -y
          sudo apt-get install jq -y

      - name: Retrieve secret from Vault
        id: fetch-secret-vault
        uses: ./.github/packages/actions/fetchVaultEnv
        with:
          vault-token: ${{ secrets[env.VAULT_TOKEN] }}
          vault-url: ${{ secrets[env.VAULT_URL] }}
          vault-path: ${{ env.VAULT_SECRET_PATH }}

      - name: "Docker auth"
        run: |-
          gcloud auth configure-docker ${{ env.REGISTRY }} --quiet

      - name: Set Docker image tag
        run: |
          if [[ ${{ github.ref }} == 'refs/heads/con-ed-dev' ]]; then
            echo "DOCKER_IMAGE_TAG=dev" >> $GITHUB_ENV
          fi
          if [[ ${{ github.ref }} == 'refs/heads/con-ed-release' ]]; then
            echo "DOCKER_IMAGE_TAG=staging" >> $GITHUB_ENV
          fi
          if [[ ${{ github.ref }} == 'refs/heads/con-ed-master' ]]; then
            echo "DOCKER_IMAGE_TAG=pre-prod" >> $GITHUB_ENV
          fi

      - name: Build and push image
        uses: docker/build-push-action@v5
        with:
          context: .
          file: "./docker/Dockerfile.lms-ecommerce-client"
          push: true
          tags: "${{ env.REGISTRY }}${{ env.PROJECT_ID }}${{ env.DOCKER_IMAGE }}:${{ env.DOCKER_IMAGE_TAG }}"
          build-args: |
            ENV_VARIABLE=${{ steps.fetch-secret-vault.outputs.env-base64 }}
            APP_TAG_VERSION=${{ env.DOCKER_IMAGE_TAG }}

      - name: Deploy
        uses: peter-evans/repository-dispatch@v2
        with:
          token: ${{ secrets.PAT }}
          repository: SkillLane/skilllane-non-prod-infra
          event-type: deployment_update
          client-payload: '{"APP_NAME": "${{ env.APP_NAME }}", "APP_ENV": "${{ env.APP_ENV }}", "BUILD_NUMBER": "${{ github.run_number }}"}'
