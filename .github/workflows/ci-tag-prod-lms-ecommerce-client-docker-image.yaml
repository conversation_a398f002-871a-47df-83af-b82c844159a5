name: Build Tag Production LMS Client Landing Page

on:
  push:
    tags:
      - "v*.*.*-lms-ecommerce"
    paths:
      - "docker/Dockerfile.lms-ecommerce-client"
      - "packages/lms-ecommerce/client/**"
      - ".github/workflows/ci-tag-prod-lms-ecommerce-client-docker-image.yaml"
      - "*"

env:
  PROJECT_ID: /skilllane-coned-platform
  REGISTRY: asia-southeast1-docker.pkg.dev
  DOCKER_IMAGE: /lms/ecommerce-web
  DOCKER_IMAGE_TAG: ""
  BUILD_ENVIRONMENT: prod
  GIT_COMMIT_HASH: ${{ github.sha }}
  GCP_SA_KEY: LMS_GCP_SA_KEY

  VAULT_URL_PROD: https://vault-prod.skilllane.net
  VAULT_TOKEN_PROD: VAULT_PROD_TOKEN
  JQ_JSON_PROD: .data.data

  SECRET_PATH_PROD: secret/data/lms/prod/lms-ecommerce-web

jobs:
  setup-build-deploy:
    name: Setup, Build & Push Image
    runs-on: skl-runner

    steps:
      - name: Set environment variables
        run: |
          echo "BUILD_ENVIRONMENT=${{ env.BUILD_ENVIRONMENT }}" >> $GITHUB_ENV
          echo "VAULT_URL=${{ env.VAULT_URL_PROD}}" >> $GITHUB_ENV
          echo "VAULT_SECRET_PATH=${{ env.SECRET_PATH_PROD}}" >> $GITHUB_ENV
          echo "VAULT_TOKEN=${{ env.VAULT_TOKEN_PROD}}" >> $GITHUB_ENV
          echo "JQ_JSON=${{ env.JQ_JSON_PROD}}" >> $GITHUB_ENV

      - name: Checkout
        uses: actions/checkout@v3

      - id: "auth"
        uses: "google-github-actions/auth@v2"
        with:
          credentials_json: "${{ secrets[env.GCP_SA_KEY] }}"

      - name: "Set up Cloud SDK"
        uses: "google-github-actions/setup-gcloud@v2"

      - name: Install curl, jq
        run: |
          sudo apt-get update
          sudo apt-get install curl -y
          sudo apt-get install jq -y

      - name: Retrieve secret from Vault
        id: fetch-secret-vault
        uses: ./.github/packages/actions/fetchVaultEnv
        with:
          vault-token: ${{ secrets[env.VAULT_TOKEN] }}
          vault-url: ${{ env.VAULT_URL }}
          vault-path: ${{ env.VAULT_SECRET_PATH }}

      - name: "Docker auth"
        run: |-
          gcloud auth configure-docker ${{ env.REGISTRY }} --quiet

      - name: Set Docker image tag
        run: |
          echo "DOCKER_IMAGE_TAG=${{ github.ref_name }}" >> $GITHUB_ENV

      - name: Build and push image
        uses: docker/build-push-action@v5
        with:
          context: .
          file: "./docker/Dockerfile.lms-ecommerce-client"
          push: true
          tags: "${{ env.REGISTRY }}${{ env.PROJECT_ID }}${{ env.DOCKER_IMAGE }}:${{ env.DOCKER_IMAGE_TAG }}"
          build-args: |
            ENV_VARIABLE=${{ steps.fetch-secret-vault.outputs.env-base64 }}
            APP_TAG_VERSION=${{ env.DOCKER_IMAGE_TAG }}
