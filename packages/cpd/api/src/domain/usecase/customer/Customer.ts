import { GenericID } from '@iso/constants/commonTypes';
import { DateFormat } from '@iso/helpers/dateUtils';
import { DBCollectionEnum } from '@iso/lms/enums/dbCollection.enum';
import { EnrollmentStatusEnum } from '@iso/lms/enums/enrollment.enum';
import { BulkOpTypeEnum } from '@iso/lms/enums/job.enum';
import { QuizTestTypeEnum } from '@iso/lms/enums/quiz.enum';
import { CourseVersion } from '@iso/lms/models/courseVersion.model';
import * as fileUpload from 'express-fileupload';
import { inject, injectable } from 'inversify';
import { orderBy, union } from 'lodash';

import { Code } from '@core/commons/code';
import { CoreDIToken } from '@core/commons/di/coreDIToken';
import { Exception } from '@core/commons/exception/Exception';
import {
  BuildExportEnrollmentReportParams,
  BuildRelationEnrollmentReportParams,
  BusinessType,
  CustomerParams,
  EnrollmentReportExportExcelParams,
  ExportEnrollmentReportParams,
  UpdateCustomerParams,
} from '@entities/constants/customer';
import {
  CustomerPartnerParams,
  CustomerPartnerReportParams,
  FilterGetCustomerPartnerList,
} from '@entities/constants/customerPartner';
import {
  CourseDIToken,
  CreditDIToken,
  OrganizationDIToken,
  ProductSKUDIToken,
  UserDIToken,
} from '@entities/constants/di';
import { LearningStatusTypeEnum } from '@entities/constants/enrollment';
import {
  EnrollmentAttachmentReportParams,
  FILE_TYPE_ENROLLMENT_ATTACHMENT,
} from '@entities/constants/enrollmentAttachment';
import { EnrollmentCertificateReportHistoryParams } from '@entities/constants/enrollmentCertificate';
import { DOMAIN } from '@entities/constants/error';
import { EnrollmentReportColumns } from '@entities/constants/excel';
import { organizationDomain } from '@entities/constants/organization';
import { PreEnrollmentTransactionReportParams } from '@entities/constants/preEnrollmentTransaction';
import { ProductSKUReportHistoryParams } from '@entities/constants/productSKU';
import { QuizAnswerReportParams } from '@entities/constants/quizAnswer';
import { TYPES } from '@entities/constants/types';
import { UserReportHistoryParams } from '@entities/constants/user';
import { ICustomerUseCase } from '@entities/interface/customerUseCase.interface';
import {
  EnrollmentAttachmentRepositoryInterface,
  ICourseRepository,
  ICustomerPartnerRepository,
  ICustomerRepository,
  ICustomerSaleOrderItemRepository,
  IEnrollmentCertificateRepository,
  IOrganizationRepository,
  IPreEnrollmentTransactionRepository,
  IProductSKURepository,
  IQuizAnswerRepository,
  IRoundRepository,
  UserRepositoryInterface,
} from '@entities/interface/repository';
import { Customer } from '@entities/models/customerModel';
import { Storage } from '@entities/services';
import { date } from '@entities/services/dateUtils';
import {
  buildCertificateUrl,
  buildEvaluateResult,
  buildLearningStatus,
  buildTestScore,
} from '@entities/services/enrollmentServices';
import { buildExcel, injectionRiskSyntaxProtector } from '@entities/services/excelService';
import { AmqpAdapter } from '@infrastructure/adapter/message/amqpAdapter';
import { CustomerDataMapper, CustomerPartnerDataMapper } from '@infrastructure/dataMapper';
import { getLearningProgressPercentText } from '@usecase/enrollment/utils';

@injectable()
export class CustomerUseCase implements ICustomerUseCase {
  constructor(
    @inject(TYPES.CustomerRepository)
    private readonly customerRepository: ICustomerRepository,
    @inject(UserDIToken.UserRepository)
    private readonly userRepository: UserRepositoryInterface,
    @inject(CreditDIToken.CustomerSaleOrderItemRepository)
    private readonly customerSaleOrderItemRepository: ICustomerSaleOrderItemRepository,
    @inject(TYPES.CustomerPartnerRepository)
    private readonly customerPartnerRepository: ICustomerPartnerRepository,
    @inject(TYPES.PreEnrollmentTransactionRepository)
    private readonly preEnrollmentTransactionRepository: IPreEnrollmentTransactionRepository,
    @inject(TYPES.RoundRepository)
    private readonly roundRepository: IRoundRepository,
    @inject(TYPES.EnrollmentAttachmentRepository)
    private readonly enrollmentAttachmentRepository: EnrollmentAttachmentRepositoryInterface,
    @inject(TYPES.EnrollmentCertificateRepository)
    private readonly enrollmentCertificateRepository: IEnrollmentCertificateRepository,
    @inject(TYPES.QuizAnswerRepository)
    private readonly quizAnswerRepository: IQuizAnswerRepository,
    @inject(OrganizationDIToken.OrganizationRepository)
    private readonly organizationRepository: IOrganizationRepository,
    @inject(ProductSKUDIToken.ProductSKURepository)
    private readonly productSKURepository: IProductSKURepository,
    @inject(CourseDIToken.CourseRepository)
    private readonly courseRepository: ICourseRepository,
    @inject(CoreDIToken.AmqpWorkerAdapter)
    private readonly amqpWorkerAdapter: AmqpAdapter,
    @inject(TYPES.CustomerDataMapper)
    private readonly customerDataMapper: CustomerDataMapper,
    @inject(TYPES.CustomerPartnerDataMapper)
    private readonly customerPartnerDataMapper: CustomerPartnerDataMapper,
  ) {}

  async all(): Promise<CustomerParams[]> {
    const data = await this.customerRepository.find({});
    const result = this.customerDataMapper.toDTOs(data);
    return result;
  }

  async getByCustomerCode(customerCode: string): Promise<any> {
    const data = await this.customerRepository.aggregate([
      { $match: { customerCode, deletedAt: null } },
      {
        $lookup: {
          from: DBCollectionEnum.USERS,
          localField: 'customerCode',
          foreignField: 'customerCodes',
          as: 'users',
        },
      },
      { $addFields: { totalUsers: { $size: '$users' } } },
      {
        $project: {
          id: 1,
          customerName: 1,
          customerCode: 1,
          createdAt: 1,
          logoImageUrl: '$certificateConfig.logoImageUrl',
          isDynamicCertificate: '$certificateConfig.isDynamicCertificate',
          textDynamicCertificate: '$certificateConfig.textDynamicCertificate',
          totalUsers: 1,
        },
      },
    ]);

    if (!data || data.length === 0) {
      throw Exception.new({
        code: Code.ENTITY_NOT_FOUND_ERROR,
        message: `customer ${customerCode} not found`,
        domain: DOMAIN.CUSTOMER,
      });
    }

    const organization = await this.organizationRepository.findOne({ domain: { $in: organizationDomain.CPD } });
    if (!organization) {
      throw Exception.new({
        code: Code.ENTITY_NOT_FOUND_ERROR,
        domain: DOMAIN.ORGANIZATION,
      });
    }

    return { ...data[0], certificateDefaultConfig: organization.certificateConfig };
  }

  async create(payload: CustomerParams): Promise<CustomerParams> {
    const { customerCode } = payload;
    const customer = await this.customerRepository.findOne({ customerCode });
    if (customer) {
      throw Exception.new({
        code: Code.CONFLICT_ERROR,
        data: { customerCode },
        domain: DOMAIN.CUSTOMER,
      });
    }

    const document = Customer.create(payload);
    await this.customerRepository.save(document);

    const routingKey = '';
    const message = customerCode;
    await this.amqpWorkerAdapter.publishToTopic('new_customer', routingKey, message, {
      headers: { operationType: BulkOpTypeEnum.UPDATE_CUSTOMER },
    });

    return this.customerDataMapper.toDTO(document);
  }

  async update(
    customerCode: string,
    payload: UpdateCustomerParams,
    files?: fileUpload.FileArray | null,
  ): Promise<void> {
    const customer = await this.customerRepository.findOne({ customerCode });
    if (!customer) {
      throw Exception.new({
        code: Code.ENTITY_NOT_FOUND_ERROR,
        data: { customerCode },
        domain: DOMAIN.CUSTOMER,
      });
    }

    customer.customerName = payload.customerName;
    customer.certificateConfig.isDynamicCertificate = payload.isDynamicCertificate || false;
    customer.certificateConfig.textDynamicCertificate = payload.textDynamicCertificate || '';

    if (files && files.uploadedFile) {
      const logoImageUrl = await this.updateLogoImageToS3(customer.customerCode, files);
      customer.certificateConfig.logoImageUrl = logoImageUrl;
    }

    await this.customerRepository.save(customer);
  }

  private async updateLogoImageToS3(customerCode: string, files: fileUpload.FileArray): Promise<string> {
    const { uploadedFile } = files as any;
    const targetKey = `images/customer/logo/${customerCode}.png`;
    await Storage.uploadImageToS3(uploadedFile.data, targetKey).catch((err) => {
      throw Exception.new({
        code: Code.BAD_REQUEST_ERROR,
        message: 'The image can not be uploaded to the cloud storage',
        data: { err },
        domain: DOMAIN.CUSTOMER,
      });
    });
    return targetKey;
  }

  async createOrUpdate(payload: CustomerParams): Promise<void> {
    const { customerCode } = payload;
    const customer = await this.customerRepository.findOne({ customerCode });
    if (!customer) {
      await this.create(payload);
    } else {
      customer.customerName = payload.customerName;
      customer.deletedAt = null;
      await this.customerRepository.save(customer);
    }
  }

  async deleteCustomerAccount(customerCode: string): Promise<any> {
    const customer = await this.customerRepository.findOne({ customerCode }, { includeDeleted: true });
    if (!customer) {
      throw Exception.new({
        code: Code.ENTITY_NOT_FOUND_ERROR,
        message: 'customer account not found',
        domain: DOMAIN.CUSTOMER,
      });
    }

    if (customer.deletedAt) {
      throw Exception.new({
        code: Code.CONFLICT_ERROR,
        message: 'customer account already deleted',
        domain: DOMAIN.CUSTOMER,
      });
    }

    const purchaseOrder = await this.customerSaleOrderItemRepository.findOne({
      customerCode: customer.customerCode,
      deletedAt: null,
    });

    if (purchaseOrder) {
      throw Exception.new({
        code: Code.CONFLICT_ERROR,
        message: 'customer account have sale order',
        domain: DOMAIN.CUSTOMER,
      });
    }

    await this.customerRepository.deleteOne({ id: customer.id });
    await this.userRepository.updateMany(
      { customerCodes: { $in: [customerCode] } },
      { $pull: { customerCodes: customerCode } },
    );
  }

  async getCustomerPartnerListByCustomerCode(filter: FilterGetCustomerPartnerList): Promise<CustomerPartnerParams[]> {
    const { customerCode, isEnabled } = filter;

    let aggregateParams: Record<string, unknown>[] = [
      {
        $match: {
          customerCode,
          deletedAt: null,
        },
      },
    ];

    if (isEnabled !== undefined) {
      aggregateParams.push({
        $lookup: {
          from: DBCollectionEnum.CUSTOMER_PARTNERS,
          let: { id: '$id' },
          pipeline: [
            {
              $match: {
                $expr: {
                  $and: [
                    {
                      $eq: ['$customerId', '$$id'],
                    },
                    {
                      $eq: ['$isEnabled', isEnabled],
                    },
                  ],
                },
              },
            },
          ],
          as: 'customerPartners',
        },
      });
    } else {
      aggregateParams.push({
        $lookup: {
          from: DBCollectionEnum.CUSTOMER_PARTNERS,
          let: { id: '$id' },
          pipeline: [
            {
              $match: {
                $expr: {
                  $eq: ['$customerId', '$$id'],
                },
              },
            },
          ],
          as: 'customerPartners',
        },
      });
    }

    aggregateParams = aggregateParams.concat([
      {
        $unwind: '$customerPartners',
      },
      {
        $replaceRoot: { newRoot: '$customerPartners' },
      },
    ]);

    const res = await this.customerRepository.aggregate(aggregateParams);

    return this.customerPartnerDataMapper.toDTOs(res);
  }

  private getCustomerCreditReportFilter() {
    const startYearDate = date().startOf('year').toDate();
    const startNextYearDate = date().startOf('year').add(1, 'years').toDate();
    const endYearDate = date().endOf('year').toDate();
    const filterDateThisYear = {
      $or: [
        {
          $and: [
            {
              $gte: ['$purchase-orders.startedAt', startYearDate],
            },
            {
              $lt: ['$purchase-orders.startedAt', endYearDate],
            },
          ],
        },
        {
          $and: [
            {
              $gte: ['$purchase-orders.expiredAt', startYearDate],
            },
            {
              $lt: ['$purchase-orders.expiredAt', endYearDate],
            },
          ],
        },
      ],
    };
    const aggregate: any = [
      {
        $lookup: {
          from: DBCollectionEnum.CUSTOMER_SALE_ORDER_ITEMS,
          let: { customerCode: '$customerCode' },
          pipeline: [
            {
              $match: {
                $and: [
                  { $expr: { $eq: ['$customerCode', '$$customerCode'] } },
                  { $expr: { $eq: ['$deletedAt', null] } },
                ],
              },
            },
          ],
          as: 'purchase-orders',
        },
      },
      { $unwind: { path: '$purchase-orders', preserveNullAndEmptyArrays: true } },
      {
        $project: {
          customerName: 1,
          customerCode: 1,
          createdAt: 1,
          startedAt: '$purchase-orders.startedAt',
          expiredAt: '$purchase-orders.expiredAt',
          totalPoint: {
            $cond: [filterDateThisYear, '$purchase-orders.point', 0],
          },
          usedPoint: {
            $cond: [
              filterDateThisYear,
              {
                $subtract: ['$purchase-orders.point', '$purchase-orders.remainPoint'],
              },
              0,
            ],
          },
          availablePoint: {
            $cond: [
              {
                $and: [
                  {
                    $lt: ['$purchase-orders.startedAt', new Date()],
                  },
                  {
                    $gte: ['$purchase-orders.expiredAt', new Date()],
                  },
                  filterDateThisYear,
                ],
              },
              '$purchase-orders.remainPoint',
              0,
            ],
          },
          notStartedPoint: {
            $cond: [
              {
                $and: [
                  {
                    $gte: ['$purchase-orders.startedAt', new Date()],
                  },
                  {
                    $lt: ['$purchase-orders.startedAt', endYearDate],
                  },
                ],
              },
              '$purchase-orders.point',
              0,
            ],
          },
          notStartedNextYearPoint: {
            $cond: [
              {
                $gte: ['$purchase-orders.startedAt', startNextYearDate],
              },
              '$purchase-orders.point',
              0,
            ],
          },
          expiredPoint: {
            $cond: [
              {
                $and: [
                  {
                    $lt: ['$purchase-orders.expiredAt', new Date()],
                  },
                  {
                    $gte: ['$purchase-orders.expiredAt', startYearDate],
                  },
                ],
              },
              '$purchase-orders.remainPoint',
              0,
            ],
          },
        },
      },
      {
        $group: {
          _id: { customerCode: '$customerCode', customerName: '$customerName' },
          totalPoint: { $sum: '$totalPoint' },
          usedPoint: { $sum: '$usedPoint' },
          availablePoint: { $sum: '$availablePoint' },
          notStartedPoint: { $sum: '$notStartedPoint' },
          notStartedNextYearPoint: { $sum: '$notStartedNextYearPoint' },
          expiredPoint: { $sum: '$expiredPoint' },
          createdAt: { $first: '$createdAt' },
        },
      },
      {
        $project: {
          customerName: '$_id.customerName',
          customerCode: '$_id.customerCode',
          totalCredit: { $divide: ['$totalPoint', 100] },
          usedCredit: { $divide: ['$usedPoint', 100] },
          availableCredit: { $divide: ['$availablePoint', 100] },
          notStartedCredit: { $divide: ['$notStartedPoint', 100] },
          notStartedNextYearCredit: { $divide: ['$notStartedNextYearPoint', 100] },
          expiredCredit: { $divide: ['$expiredPoint', 100] },
          _id: 0,
          createdAt: '$createdAt',
        },
      },
    ];
    return aggregate;
  }
  async exportEnrollmentReport(payload: ExportEnrollmentReportParams): Promise<any> {
    const { filter, customerCode } = payload;
    const { roundDateFrom, roundDateTo, customerPartnerCode, productSKUCodes } = filter;
    const customer = await this.customerRepository.findOneByCode(customerCode);
    if (!customer) {
      throw Exception.new({
        code: Code.ENTITY_NOT_FOUND_ERROR,
        data: { customerCode },
        domain: DOMAIN.CUSTOMER,
      });
    }

    let customerPartner;
    if (customerPartnerCode) {
      customerPartner = await this.customerPartnerRepository.findOne({ code: customerPartnerCode });
      if (!customerPartner) {
        throw Exception.new({
          code: Code.ENTITY_NOT_FOUND_ERROR,
          data: { customerPartner },
          domain: DOMAIN.CUSTOMER,
        });
      }
    }

    let customerPartnerCodeQuery: Record<string, any> = {};
    if (customerPartner) {
      customerPartnerCodeQuery = {
        $expr: {
          $eq: ['$customerPartnerId', customerPartner.id],
        },
      };
    }

    let courseIdQuery: Record<string, any> = {};

    if (productSKUCodes) {
      const productSKUs = await this.productSKURepository.aggregate([
        {
          $match: {
            code: { $in: productSKUCodes },
          },
        },
        {
          $lookup: {
            from: DBCollectionEnum.PRODUCT_SKU_COURSES,
            let: { productSKUId: '$id' },
            pipeline: [
              {
                $match: {
                  $expr: {
                    $eq: ['$productSKUId', '$$productSKUId'],
                  },
                },
              },
              {
                $lookup: {
                  from: DBCollectionEnum.COURSES,
                  let: { productSKUCourseId: '$id' },
                  pipeline: [
                    {
                      $match: {
                        $expr: {
                          $and: [{ $eq: ['$productSKUCourseId', '$$productSKUCourseId'] }],
                        },
                      },
                    },
                  ],
                  as: 'course',
                },
              },
              {
                $unwind: '$course',
              },
              {
                $project: {
                  id: 1,
                  productSKUId: 1,
                  courseId: '$course.id',
                },
              },
            ],
            as: 'productSKUCourse',
          },
        },
        {
          $unwind: '$productSKUCourse',
        },
      ]);

      const courseIds = productSKUs.map((val: any) => val.productSKUCourse.courseId);

      courseIdQuery = {
        $expr: {
          $in: ['$courseId', courseIds],
        },
      };
    }

    const aggregateParams = [
      {
        $match: {
          roundDate: { $gte: roundDateFrom, $lte: roundDateTo },
        },
      },
      { $sort: { roundDate: 1 } },
      {
        $project: {
          id: 1,
          roundDate: 1,
        },
      },
      {
        $lookup: {
          from: DBCollectionEnum.ENROLLMENTS,
          let: {
            roundId: '$id',
          },
          pipeline: [
            {
              $match: {
                $and: [
                  {
                    $expr: {
                      $eq: ['$roundId', '$$roundId'],
                    },
                  },
                  {
                    $expr: {
                      $eq: ['$business', BusinessType.B2B],
                    },
                  },
                  {
                    $expr: {
                      $eq: ['$customerCode', customerCode],
                    },
                  },
                  courseIdQuery,
                ],
              },
            },
            {
              $project: {
                _id: 0,
                id: 1,
                courseId: 1,
                courseVersionId: 1,
                roundId: 1,
                business: 1,
                customerCode: 1,
                status: 1,
                createdAt: 1,
                expiredAt: 1,
                requestedApprovalAt: 1,
                userId: 1,
                acceptedAt: 1,
                completedCourseItem: 1,
                approvalReason: 1,
              },
            },
          ],
          as: 'enrollment',
        },
      },
      {
        $unwind: '$enrollment',
      },
      {
        $match: {
          'enrollment.status': {
            $in: [
              EnrollmentStatusEnum.PASSED,
              EnrollmentStatusEnum.IN_PROGRESS,
              EnrollmentStatusEnum.PENDING_APPROVAL,
              EnrollmentStatusEnum.VERIFIED,
              EnrollmentStatusEnum.APPROVED,
              EnrollmentStatusEnum.REJECTED,
              EnrollmentStatusEnum.EXPIRED,
              EnrollmentStatusEnum.CANCELED,
            ],
          },
        },
      },
      {
        $lookup: {
          from: DBCollectionEnum.REGISTRATIONS,
          let: {
            enrollmentId: '$enrollment.id',
          },
          pipeline: [
            {
              $match: {
                $and: [
                  {
                    $expr: {
                      $eq: ['$enrollmentId', '$$enrollmentId'],
                    },
                  },
                  { ...customerPartnerCodeQuery },
                ],
              },
            },
            { $project: { id: 1, enrollmentId: 1, status: 1, customerPartnerId: 1 } },
          ],
          as: 'registration',
        },
      },
      {
        $unwind: '$registration',
      },
      {
        $lookup: {
          from: DBCollectionEnum.ENROLLMENT_REGULATOR_REPORTS,
          let: {
            registrationId: '$registration.id',
          },
          pipeline: [
            {
              $match: {
                $expr: {
                  $eq: ['$registrationId', '$$registrationId'],
                },
              },
            },
            { $project: { id: 1, preEnrollmentTransactionId: 1, registrationId: 1 } },
          ],
          as: 'enrollmentRegulatorReport',
        },
      },
      { $unwind: { path: '$enrollmentRegulatorReport', preserveNullAndEmptyArrays: true } },
    ];

    const datas: BuildExportEnrollmentReportParams[] = await this.roundRepository.aggregate(aggregateParams);

    const fileName = `enrollment_${date(roundDateFrom).format(DateFormat.yearMonthDay)}-${date(roundDateTo).format(
      DateFormat.yearMonthDay,
    )}`;

    const excelRecords = await this.buildEnrollmentReportExcel(datas, customerPartnerCode);
    const excelFormat = buildExcel(EnrollmentReportColumns, excelRecords, fileName).workbook;
    return excelFormat;
  }

  private async buildEnrollmentReportExcel(
    list: BuildExportEnrollmentReportParams[],
    customerPartnerCodeParams?: string,
  ): Promise<EnrollmentReportExportExcelParams[]> {
    const enrollmentIdList = union(list.map((item: BuildExportEnrollmentReportParams) => item.enrollment.id));
    const courseIdList = union(list.map((item: BuildExportEnrollmentReportParams) => item.enrollment.courseId));
    const userList = union(list.map((item: BuildExportEnrollmentReportParams) => item.enrollment.userId));

    const preEnrollmentTransactionIdList = list.map(
      (item: BuildExportEnrollmentReportParams) => item.enrollmentRegulatorReport?.preEnrollmentTransactionId,
    );

    const customerPartnerIdList = list.map(
      (item: BuildExportEnrollmentReportParams) => item.registration?.customerPartnerId,
    );

    const {
      quizAnswers = [],
      enrollmentCertificates = [],
      enrollmentAttachments = [],
      preEnrollmentTransactions = [],
      customerPartners = [],
      courses = [],
      users = [],
    } = await this.buildRelationEnrollmentReport(
      enrollmentIdList,
      preEnrollmentTransactionIdList,
      customerPartnerIdList,
      courseIdList,
      userList,
    );

    const result: EnrollmentReportExportExcelParams[] = list.map((item: BuildExportEnrollmentReportParams) => {
      const {
        enrollment: {
          id: enrollmentId,
          status: enrollmentStatus,
          expiredAt,
          acceptedAt,
          requestedApprovalAt,
          completedCourseItem,
          approvalReason,
          courseId,
          courseVersionId,
          userId,
          customerCode = '',
        },
        enrollmentRegulatorReport,
        registration: { customerPartnerId },
      } = item;
      const { preEnrollmentTransactionId } = enrollmentRegulatorReport || {};

      const course = courses.find((val: ProductSKUReportHistoryParams) => val.id === courseId);
      const user = users.find((val: UserReportHistoryParams) => val.guid === userId);
      const [quizAnswer] = quizAnswers.filter((val: QuizAnswerReportParams) => val.enrollmentId === enrollmentId);
      const filterEnrollmentCertificates = enrollmentCertificates.filter(
        (val: EnrollmentCertificateReportHistoryParams) => val.enrollmentId === enrollmentId,
      );
      const enrollmentAttachment = enrollmentAttachments.find(
        (val: EnrollmentAttachmentReportParams) => val.enrollmentId === enrollmentId,
      );
      const preEnrollmentTransaction = preEnrollmentTransactions.find(
        (val: PreEnrollmentTransactionReportParams) => val.id === preEnrollmentTransactionId,
      );

      const companyPartnerCode = (() => {
        let customerPartnerCode = '';

        if (customerPartnerCodeParams) return customerPartnerCodeParams;

        if (customerPartnerId && customerPartners.length) {
          const findCustomerPartner = customerPartners.find(
            (val: CustomerPartnerReportParams) => val.id === customerPartnerId,
          );
          customerPartnerCode = findCustomerPartner?.code || '';
        }

        return customerPartnerCode;
      })();

      const enrollCourseVersion = course?.courseVersions.find(
        (courseVersion: CourseVersion) => courseVersion.id === courseVersionId,
      );

      const roundDate = date(item.roundDate).format(DateFormat.dayMonthYearWithLeadingZero);
      const companyCode = customerCode;
      const email = user?.email ?? '';
      const title = user?.profile?.salute ?? '';
      const firstname = user?.profile?.firstname ?? '';
      const lastname = user?.profile?.lastname ?? '';
      const phone = user?.profile?.mobilePhoneNumber ?? '';
      const citizenId = user?.citizenId ?? '';
      const courseCode = course?.productSKUCode ?? '';
      const courseName = enrollCourseVersion ? enrollCourseVersion.name : '';
      const totalCourseItem = enrollCourseVersion ? enrollCourseVersion.totalCourseItems : 0;
      const regulator = course?.regulatorInfo?.regulator ?? '';
      const learningStatus = buildLearningStatus(enrollmentStatus, expiredAt, acceptedAt, course?.objectiveType);

      const {
        userPoint: posttestUserPoint,
        totalPoint: posttestTotalPoint,
        minPassPoint: posttestMinPassPoint,
        PCTUserPoint: posttestPCTUserPoint,
      } = buildTestScore(
        quizAnswer?.userPoint,
        quizAnswer?.totalPoint,
        !!quizAnswer?.finishedAt,
        quizAnswer?.criteriaCertificate?.passScore,
      );

      let posttestResult = '';
      if (quizAnswer?.finishedAt) {
        posttestResult = quizAnswer?.criteriaCertificate?.isPass ? 'passed' : 'failed';
      }
      const submittedDate = requestedApprovalAt
        ? date(requestedApprovalAt).format(DateFormat.dayMonthYearWithLeadingZero)
        : date(expiredAt).format(DateFormat.dayMonthYearWithLeadingZero);

      const tsiLicenseNoEnroll = preEnrollmentTransaction?.payload.tsiLicenseNo ?? '';
      const oicLifeLicenseNoEnroll = preEnrollmentTransaction?.payload.oicLicenseLifeNo ?? '';
      const oicNonlifeLicenseNoEnroll = preEnrollmentTransaction?.payload.oicLicenseNonLifeNo ?? '';
      const attachmentDeductionStatus = enrollmentAttachment?.status ?? '';

      const { tsiCertificateUrl, oicNonlifeCertificateUrl, oicLifeCertificateUrl } = buildCertificateUrl(
        filterEnrollmentCertificates,
        course?.objectiveType,
        course?.regulatorInfo,
      );

      const learningProgress = getLearningProgressPercentText(totalCourseItem, completedCourseItem);
      const rejectReason = enrollmentStatus === EnrollmentStatusEnum.REJECTED ? approvalReason : '';
      const evaluateResult = buildEvaluateResult(
        enrollmentStatus,
        expiredAt,
        learningStatus as LearningStatusTypeEnum,
        course?.objectiveType,
      );

      return {
        companyCode,
        companyPartnerCode,
        roundDate,
        courseCode,
        courseName,
        regulator,
        email,
        phone,
        title,
        firstname,
        lastname,
        citizenId,
        tsiLicenseNoEnroll,
        oicNonlifeLicenseNoEnroll,
        oicLifeLicenseNoEnroll,
        totalCourseItem,
        completedCourseItem,
        learningProgress,
        posttestTotalPoint,
        posttestUserPoint,
        posttestPCTUserPoint,
        posttestResult,
        posttestMinPassPoint,
        submittedDate,
        learningStatus,
        evaluateResult,
        attachmentDeductionStatus,
        rejectReason,
        tsiCertificateUrl,
        oicNonlifeCertificateUrl,
        oicLifeCertificateUrl,
        courseId,
      };
    });

    const cleanedData = injectionRiskSyntaxProtector(result);
    return orderBy(cleanedData, ['roundDate', 'productSKUId'], ['asc', 'asc']);
  }

  private async buildRelationEnrollmentReport(
    enrollmentIdList: GenericID[],
    preEnrollmentTransactionIdList: GenericID[],
    customerPartnerIdList: GenericID[],
    courseIdList: GenericID[],
    userIdList: GenericID[],
  ): Promise<BuildRelationEnrollmentReportParams> {
    const courseQuery = this.courseRepository.aggregate([
      {
        $match: {
          id: { $in: courseIdList },
        },
      },
      {
        $lookup: {
          from: DBCollectionEnum.PRODUCT_SKU_COURSES,
          let: { productSKUCourseId: '$productSKUCourseId' },
          pipeline: [
            {
              $match: {
                $expr: {
                  $eq: ['$id', '$$productSKUCourseId'],
                },
              },
            },
            {
              $project: {
                id: 1,
                productSKUId: 1,
              },
            },
          ],
          as: 'productSKUCourse',
        },
      },
      {
        $unwind: '$productSKUCourse',
      },
      {
        $lookup: {
          from: DBCollectionEnum.PRODUCT_SKUS,
          let: { productSKUId: '$productSKUCourse.productSKUId' },
          pipeline: [
            {
              $match: {
                $expr: {
                  $eq: ['$id', '$$productSKUId'],
                },
              },
            },
            {
              $project: {
                id: 1,
                code: 1,
              },
            },
          ],
          as: 'productSKU',
        },
      },
      {
        $unwind: '$productSKU',
      },
      {
        $lookup: {
          from: DBCollectionEnum.COURSE_VERSIONS,
          let: { id: '$id' },
          pipeline: [
            {
              $match: {
                $expr: {
                  $eq: ['$courseId', '$$id'],
                },
              },
            },
          ],
          as: 'courseVersions',
        },
      },
      {
        $project: {
          id: 1,
          productSKUId: '$productSKU.id',
          name: 1,
          objectiveType: 1,
          productSKUCode: '$productSKU.code',
          regulatorInfo: 1,
          totalCourseItems: 1,
          courseVersions: '$courseVersions',
        },
      },
    ]);

    const quizAnswersQuery = this.quizAnswerRepository.aggregate([
      {
        $match: {
          enrollmentId: { $in: enrollmentIdList },
          testType: QuizTestTypeEnum.POST_TEST,
        },
      },
      {
        $project: {
          id: 1,
          enrollmentId: 1,
          totalPoint: 1,
          userPoint: 1,
          criteriaCertificate: 1,
          createdAt: 1,
          finishedAt: 1,
        },
      },
    ]);

    const enrollmentCertificatesQuery = this.enrollmentCertificateRepository.aggregate([
      {
        $match: {
          enrollmentId: { $in: enrollmentIdList },
        },
      },
      { $project: { id: 1, enrollmentId: 1, type: 1, certificateUrl: 1, tsiCode: 1 } },
    ]);

    const enrollmentAttachmentsQuery = this.enrollmentAttachmentRepository.aggregate([
      {
        $match: {
          enrollmentId: { $in: enrollmentIdList },
          fileType: FILE_TYPE_ENROLLMENT_ATTACHMENT.DEDUCT,
        },
      },
      {
        $project: {
          id: 1,
          enrollmentId: 1,
          status: 1,
        },
      },
    ]);

    const preEnrollmentTransactionsQuery = this.preEnrollmentTransactionRepository.aggregate([
      {
        $match: {
          id: { $in: preEnrollmentTransactionIdList },
        },
      },
      {
        $project: {
          id: 1,
          payload: 1,
        },
      },
    ]);

    const customerPartnersQuery = this.customerPartnerRepository.aggregate([
      {
        $match: {
          id: { $in: customerPartnerIdList },
        },
      },
      {
        $project: {
          id: 1,
          code: 1,
        },
      },
    ]);

    const usersQuery = this.userRepository.aggregate([
      {
        $match: {
          guid: { $in: userIdList },
        },
      },
      {
        $project: {
          guid: 1,
          email: 1,
          profile: 1,
          citizenId: 1,
          last4DigitCitizenId: 1,
        },
      },
    ]);

    const [
      quizAnswers,
      enrollmentCertificates,
      enrollmentAttachments,
      preEnrollmentTransactions,
      customerPartners,
      courses,
      users,
    ] = await Promise.all([
      quizAnswersQuery,
      enrollmentCertificatesQuery,
      enrollmentAttachmentsQuery,
      preEnrollmentTransactionsQuery,
      customerPartnersQuery,
      courseQuery,
      usersQuery,
    ]);

    return {
      quizAnswers: quizAnswers.sort((a: Record<string, any>, b: Record<string, any>) => b.createdAt - a.createdAt),
      enrollmentCertificates,
      enrollmentAttachments,
      preEnrollmentTransactions,
      customerPartners,
      courses,
      users,
    };
  }
}
