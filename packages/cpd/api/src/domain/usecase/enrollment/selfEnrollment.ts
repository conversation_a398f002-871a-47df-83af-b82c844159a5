import { MongoClient, UpdateOneOptions } from 'mongodb';

import { GenericID, Nullable } from '@iso/constants/commonTypes';
import { UserNotificationTypeEnum } from '@iso/constants/userNotification';
import { DateFormat } from '@iso/helpers/dateUtils';
import {
  buildSelfEnrollmentNotificationMessage,
  buildSelfPreEnrollmentNotificationMessage,
} from '@iso/helpers/userNotification';
import {
  ContentProviderTypeEnum,
  CourseEnrollTypeEnum,
  CourseObjectiveTypeEnum,
  ExternalContentTypeEnum,
  LicenseRenewalEnum,
  LicenseTypeEnum,
  RegulatorEnum,
} from '@iso/lms/enums/course.enum';
import { CourseVersionStatusEnum } from '@iso/lms/enums/courseVersion.enum';
import { DBCollectionEnum } from '@iso/lms/enums/dbCollection.enum';
import { BusinessTypeEnum, EnrollmentStatusEnum, EnrollTypeEnum } from '@iso/lms/enums/enrollment.enum';
import {
  OICExtendYearTypeEnum,
  OICReductionTypeEnum,
  PreEnrollmentTransactionAutoBulkOparationEnum,
  PreEnrollmentTransactionEnrollByEnum,
  PreEnrollmentTransactionPaymentTypeEnum,
  PreEnrollmentTransactionStatusEnum,
} from '@iso/lms/enums/preEnrollmentTransaction.enum';
import { Course } from '@iso/lms/models/course.model';
import { Enrollment } from '@iso/lms/models/enrollment.model';
import { EnrollmentCertificate } from '@iso/lms/models/enrollmentCertificate.model';
import { EnrollmentRegulatorReport } from '@iso/lms/models/enrollmentRegulatorReport.model';
import { LearningPathVersion } from '@iso/lms/models/learningPathVersion.model';
import { Organization } from '@iso/lms/models/organization.model';
import { PreEnrollmentTransaction } from '@iso/lms/models/preEnrollmentTransaction.model';
import {
  checkHavePlanPackageLicenseAvailableToday,
  getAvailablePeriodLicenseByUser,
} from '@iso/lms/services/planPackageLicense.service';
import { CreateEnrollmentParams } from '@iso/lms/types/enrollment.type';
import { PlanPackageParams } from '@iso/lms/types/planPackage.type';
import { AvailablePeriodLicenseByUserParams } from '@iso/lms/types/planPackageLicense.type';
import { inject, injectable } from 'inversify';
import { isEmpty } from 'lodash';

import { UserErrorCode } from '@core/commons/code';
import { Code } from '@core/commons/code/Code';
import { Exception } from '@core/commons/exception/Exception';
import { PartParams } from '@entities/constants/course';
import {
  CourseDIToken,
  LearningPathEnrollmentDIToken,
  NotificationDIToken,
  OrganizationDIToken,
  PlanDIToken,
  UserDIToken,
} from '@entities/constants/di';
import {
  OutputSelfEnrollmentParams,
  SelfEnrollCoursePayload,
  StatusCheckEnrollableEnum,
} from '@entities/constants/enrollment';
import { DOMAIN } from '@entities/constants/error';
import {
  LearningPathContentProgressRegisterFromEnum,
  LearningPathContentProgressStatusEnum,
  LearningPathEnrollmentStatusEnum,
} from '@entities/constants/learningPathEnrollment';
import { LearningPathVersionStatusEnum } from '@entities/constants/learningPathVersion';
import {
  MailAttachmentParams,
  MailPayloadEnrollmentWelcomeEmailParams,
  MailPayloadPreEnrollmentWelcomeEmailParams,
} from '@entities/constants/mailer';
import { TYPES } from '@entities/constants/types';
import { RegulatorProfileParams } from '@entities/constants/user';
import { UserNotificationParams, UserNotificationPayloadParams } from '@entities/constants/userNotification';
import { ISelfEnrollmentUseCase } from '@entities/interface/enrollment.interface';
import { INotificationService } from '@entities/interface/notificationService.interface';
import {
  EnrollmentRepositoryInterface,
  ICourseItemCriteriaConfigRepository,
  ICourseRepository,
  ICourseVersionCertificateRepository,
  IEnrollmentCertificateRepository,
  IEnrollmentRegulatorReportRepository,
  IOrganizationRepository,
  IPlanPackageRepository,
  IPlanRepository,
  IPreEnrollmentTransactionRepository,
  IRoundRepository,
  ITransactionCounterRepository,
  UserRepositoryInterface,
} from '@entities/interface/repository';
import { ILearningPathEnrollmentRepository } from '@entities/interface/repository/learningPathEnrollmentRepository.interface';
import { IPlanPackageLicenseRepository } from '@entities/interface/repository/planPackageLicenseRepository.interface';
import { IUserNotificationRepository } from '@entities/interface/repository/userNotificationRepository.interface';
import { ISelfEnrollmentValidator } from '@entities/interface/selfEnrollmentValidator.interface';
import { Round } from '@entities/models/RoundModel';
import { UserNotification } from '@entities/models/userNotificationModel';
import { CourseService } from '@entities/services/courseService';
import { date, formatDate, isAfter, isBefore } from '@entities/services/dateUtils';
import { defaultPreEnrollmentPayload } from '@entities/services/preEnrollmentTransactionService';

@injectable()
export class SelfEnrollmentUseCase implements ISelfEnrollmentUseCase {
  constructor(
    @inject(TYPES.MongoClient)
    private readonly mongoClient: MongoClient,
    @inject(CourseDIToken.CourseRepository)
    private readonly courseRepository: ICourseRepository,
    @inject(CourseDIToken.CourseVersionCertificateRepository)
    private readonly courseVersionCertificateRepository: ICourseVersionCertificateRepository,
    @inject(TYPES.EnrollmentCertificateRepository)
    private readonly enrollmentCertificateRepository: IEnrollmentCertificateRepository,
    @inject(UserDIToken.UserRepository)
    private readonly userRepository: UserRepositoryInterface,
    @inject(OrganizationDIToken.OrganizationRepository)
    private readonly organizationRepository: IOrganizationRepository,
    @inject(TYPES.EnrollmentRepository)
    private readonly enrollmentRepository: EnrollmentRepositoryInterface,
    @inject(TYPES.RoundRepository)
    private readonly roundRepository: IRoundRepository,
    @inject(TYPES.TransactionCounterRepository)
    private readonly transactionCounterRepository: ITransactionCounterRepository,
    @inject(TYPES.PreEnrollmentTransactionRepository)
    private readonly preEnrollmentTransactionRepository: IPreEnrollmentTransactionRepository,
    @inject(TYPES.EnrollmentRegulatorReportRepository)
    private readonly enrollmentRegulatorReportRepository: IEnrollmentRegulatorReportRepository,
    @inject(TYPES.SelfEnrollmentValidatorFactory)
    private readonly selfEnrollmentValidatorFactory: (
      objectiveType: CourseObjectiveTypeEnum,
      regulator: RegulatorEnum,
    ) => ISelfEnrollmentValidator,
    @inject(LearningPathEnrollmentDIToken.LearningPathEnrollmentRepository)
    private readonly learningPathEnrollmentRepository: ILearningPathEnrollmentRepository,
    @inject(NotificationDIToken.UserNotificationRepository)
    private readonly userNotificationRepository: IUserNotificationRepository,
    @inject(TYPES.CourseItemCriteriaConfigRepository)
    private readonly courseItemCriteriaConfigRepository: ICourseItemCriteriaConfigRepository,
    @inject(PlanDIToken.PlanRepository)
    private readonly planRepository: IPlanRepository,
    @inject(PlanDIToken.PlanPackageRepository)
    private readonly planPackageRepository: IPlanPackageRepository,
    @inject(PlanDIToken.PlanPackageLicenseRepository)
    private readonly planPackageLicenseRepository: IPlanPackageLicenseRepository,

    //service
    @inject(TYPES.NotificationService)
    private readonly notificationService: INotificationService,
    @inject(TYPES.CourseService)
    private readonly courseService: CourseService,
  ) {}

  async execute(payload: SelfEnrollCoursePayload): Promise<OutputSelfEnrollmentParams> {
    const { userId, courseId, roundId, registerFrom } = payload;

    const session = this.mongoClient.startSession();
    const opts = { session };
    session.startTransaction();

    try {
      const user = await this.userRepository.findOneById(userId);

      if (!user) {
        throw Exception.new({
          code: UserErrorCode.ENTITY_NOT_FOUND_ERROR,
          message: 'User not found',
          data: { userId },
        });
      }

      const organization = await this.organizationRepository.findOneById(user.organizationId);

      if (!organization) {
        throw Exception.new({
          code: Code.ENTITY_NOT_FOUND_ERROR,
          message: 'Organization not found',
          data: { organizationId: user.organizationId },
          domain: DOMAIN.ORGANIZATION,
        });
      }

      const course = await this.courseRepository.getOnePublishCourseDetailById(courseId);

      if (!course) {
        throw Exception.new({
          code: Code.ENTITY_NOT_FOUND_ERROR,
          message: 'Course not found',
          data: { courseId },
          domain: DOMAIN.COURSE,
        });
      }

      const planPackageContentActiveList = await this.findUserPlanPackageContentActive(
        user.organizationId,
        course.productSKUCourseId,
      );

      const isCourseExternal = course.contentProviderType === ContentProviderTypeEnum.EXTERNAL;

      const isActiveCoursePlanPackage = planPackageContentActiveList.length > 0 && isCourseExternal;

      const availableUserPlanPackageLicenseContentList = await this.findAvailableUserPlanPackageLicenseContent(
        userId,
        planPackageContentActiveList,
      );

      // validate course
      this.validateCourse(course, isActiveCoursePlanPackage);

      if (isActiveCoursePlanPackage) {
        this.validateEnrollableCoursePlanPackage(availableUserPlanPackageLicenseContentList, course.enrollType);
      }

      const { objectiveType, regulatorInfo } = course;
      const selfEnrollmentValidator = this.selfEnrollmentValidatorFactory(
        objectiveType as CourseObjectiveTypeEnum,
        regulatorInfo.regulator as RegulatorEnum,
      );

      // check user already enrollment
      const existingEnrollment = await this.enrollmentRepository.getLatestStatusEnrolled(userId, courseId);
      if (existingEnrollment) {
        if (objectiveType !== CourseObjectiveTypeEnum.REGULAR) {
          throw Exception.new({
            code: Code.BAD_REQUEST_ERROR,
            message: 'Enrollment is already enrolled',
            domain: DOMAIN.ENROLLMENT,
          });
        }

        if (
          objectiveType === CourseObjectiveTypeEnum.REGULAR &&
          (existingEnrollment.status === EnrollmentStatusEnum.IN_PROGRESS ||
            existingEnrollment.status === EnrollmentStatusEnum.PENDING_RESULT)
        ) {
          throw Exception.new({
            code: Code.BAD_REQUEST_ERROR,
            message: 'Enrollment is already enrolled',
            domain: DOMAIN.ENROLLMENT,
          });
        }
      }

      // check user already pre-enrollment
      const existingPreEnrolled = await this.preEnrollmentTransactionRepository.getLatestPreEnrollment({
        citizenId: user.citizenId,
        courseId: course.id,
        organizationId: user.organizationId,
      });

      if (existingPreEnrolled) {
        throw Exception.new({
          code: Code.BAD_REQUEST_ERROR,
          message: 'PreEnrollment is already enrolled',
          data: { roundId },
          domain: DOMAIN.PRE_ENROLLMENT_TRANSACTION,
        });
      }

      if (course.enrollType === CourseEnrollTypeEnum.IMMEDIATE) {
        // create enrollment
        const responseValidate = await selfEnrollmentValidator.validate(user.guid, course, null);

        const { status } = responseValidate;
        if (status !== StatusCheckEnrollableEnum.AVAILABLE) {
          throw Exception.new({
            code: Code.BAD_REQUEST_ERROR,
            message: 'Can not create enrollment',
            data: { roundId },
            domain: DOMAIN.ENROLLMENT,
          });
        }

        if (objectiveType === CourseObjectiveTypeEnum.REGULAR && course.isReEnrollEnabled) {
          const latestEnrollment = await this.getLatestEnrollment(user.guid, course.id);
          if (latestEnrollment && latestEnrollment.status === EnrollmentStatusEnum.PASSED) {
            latestEnrollment.status = EnrollmentStatusEnum.COMPLETED;
            this.enrollmentRepository.save(latestEnrollment, opts);
          }
        }

        const createEnrollmentPayload: CreateEnrollmentParams = {
          courseId,
          courseVersionId: course.courseVersion.id,
          userId,
          status: EnrollmentStatusEnum.IN_PROGRESS,
          business: BusinessTypeEnum.B2B,
          organizationId: course.organizationId,
          isCountdownArticle: course.courseVersion.isCountdownArticle,
          isIdentityVerificationEnabled: course.courseVersion.isIdentityVerificationEnabled,
          expiredAt:
            course.courseVersion.expiryDay && course.courseVersion.expiryDay > 0
              ? date().add(course.courseVersion.expiryDay, 'day').endOf('day').toDate()
              : undefined,
          remark: '',
          enrollType: EnrollTypeEnum.VOLUNTARY,
          externalContentType: isActiveCoursePlanPackage
            ? ExternalContentTypeEnum.PLAN_PACKAGE
            : ExternalContentTypeEnum.NONE,
        };

        const newEnrollment = await Enrollment.new(createEnrollmentPayload);

        const newEnrollCertificates = [];

        if (course.courseVersion.isCertificateEnabled) {
          const certificates = await this.courseVersionCertificateRepository.findWithCertificateDetail(
            course.courseVersion.id,
          );
          const { report } = course.courseVersion;

          if (course.courseVersion?.isMultipleCertificate) {
            for (const certificate of certificates) {
              const { type } = certificate;
              const certificateModel = await EnrollmentCertificate.new({
                enrollmentId: newEnrollment.id,
                courseVersionCertificateId: certificate.id,
                payload: {
                  pillarName: report?.pillarName,
                  tsiCode: report?.tsiCode,
                  logoImageUrl: '',
                  issuedBy: '',
                },
                type,
                isSentEmailOnExpiredDate: course.courseVersion?.isSentCertificateEmailOnExpiredDate,
              });

              newEnrollCertificates.push(certificateModel);
            }
          } else {
            const [certificate] = certificates;
            const { type } = certificate;
            const enrollmentCertificateModel = await EnrollmentCertificate.new({
              enrollmentId: newEnrollment.id,
              courseVersionCertificateId: certificate.id,
              payload: {
                pillarName: report?.pillarName,
                tsiCode: report?.tsiCode,
                logoImageUrl: '',
                issuedBy: '',
              },
              type,
              isSentEmailOnExpiredDate: course.courseVersion.isSentCertificateEmailOnExpiredDate,
            });

            newEnrollCertificates.push(enrollmentCertificateModel);
          }
        }

        await this.enrollmentRepository.save(newEnrollment, opts);

        if (newEnrollCertificates.length > 0) {
          await this.enrollmentCertificateRepository.saveMany(newEnrollCertificates, opts);
        }

        await this.syncEnrollmentWithLearningPathEnrollment(
          userId,
          courseId,
          user.organizationId,
          newEnrollment.id,
          course.enrollType,
          registerFrom,
          opts,
        );

        const courseItemCriteriaConfigs = await this.courseItemCriteriaConfigRepository.find({
          courseVersionId: course.courseVersion.id,
        });

        let parts = await this.courseService.getPartByCourseId(
          course.id,
          course.contentProviderType,
          course.courseVersion.id,
        );
        parts = this.courseService.combineCourseItemCriteriaConfigToPart(parts, courseItemCriteriaConfigs);
        parts = this.courseService.removeUnavailableContent(parts);

        //!start implement notification
        const emailData: MailPayloadEnrollmentWelcomeEmailParams = {
          fullName: `${user.profile?.firstname} ${user.profile?.lastname}`,
          thumbnailUrl: course.thumbnailUrl ?? '',
          course,
          expiredAt: newEnrollment?.expiredAt ?? undefined,
          parts,
        };

        await this.sendSelfEnrollmentNotification({ course, enrollment: newEnrollment }, opts);
        this.sendEnrollmentEmail({ receiverEmail: user.email, emailData, organization });
        //!end implement notification

        await session.commitTransaction();

        return { enrollmentId: newEnrollment.id, status: newEnrollment.status };
      } else {
        // create pre-enrollment
        if (!user.citizenId && course.objectiveType !== CourseObjectiveTypeEnum.REGULAR) {
          throw Exception.new({
            code: Code.BAD_REQUEST_ERROR,
            message: 'User missing citizen id',
            domain: DOMAIN.USER,
          });
        }

        const round = await this.roundRepository.findOne({ id: roundId });
        if (!round) {
          throw Exception.new({
            code: Code.ENTITY_NOT_FOUND_ERROR,
            message: 'Round not found',
            data: { roundId },
            domain: DOMAIN.ROUND,
          });
        }

        if (!this.isRoundValid(course.id, round)) {
          throw Exception.new({
            code: Code.BAD_REQUEST_ERROR,
            message: 'Round invalid',
            data: { roundId },
            domain: DOMAIN.ROUND,
          });
        }

        const responseValidate = await selfEnrollmentValidator.validate(user.guid, course, round);
        const { status, activeLicenses } = responseValidate;

        if (status !== StatusCheckEnrollableEnum.AVAILABLE) {
          throw Exception.new({
            code: Code.BAD_REQUEST_ERROR,
            message: 'Can not create enrollment',
            data: { roundId, status },
            domain: DOMAIN.ENROLLMENT,
          });
        }

        let oicLicenseLifeNo = '';
        let oicLifeStartDate = '';
        let oicLifeEndDate = '';
        let oicLicenseNonLifeNo = '';
        let oicNonLifeStartDate = '';
        let oicNonLifeEndDate = '';
        let tsiLicenseNo = '';
        let tsiLicenseType = '';
        let tsiStartDate = '';
        let tsiEnddate = '';
        let oicExtendYearType = '';
        let oicReductionType = '';
        const regulatorProfile: RegulatorProfileParams = { prefix: '', firstname: '', lastname: '' };

        if (objectiveType === CourseObjectiveTypeEnum.REGULAR && course.isReEnrollEnabled) {
          const latestEnrollment = await this.getLatestEnrollment(user.guid, course.id);
          if (latestEnrollment && latestEnrollment.status === EnrollmentStatusEnum.PASSED) {
            latestEnrollment.status = EnrollmentStatusEnum.COMPLETED;
            this.enrollmentRepository.save(latestEnrollment, opts);
          }
        }

        if (objectiveType === CourseObjectiveTypeEnum.TRAINING && regulatorInfo.regulator === RegulatorEnum.OIC) {
          if (activeLicenses) {
            const lifeLicense = activeLicenses.find((val) => val.licenseType === LicenseTypeEnum.LIFE);
            if (lifeLicense) {
              oicLicenseLifeNo = lifeLicense.licenseNo;
              oicLifeStartDate = lifeLicense.startedAt
                ? formatDate(lifeLicense.startedAt, DateFormat.buddhistDayMonthYearWithLeadingZero)
                : '';
              oicLifeEndDate = lifeLicense.expiredAt
                ? formatDate(lifeLicense.expiredAt, DateFormat.buddhistDayMonthYearWithLeadingZero)
                : '';
            }

            const nonLifeLicense = activeLicenses.find((val) => val.licenseType === LicenseTypeEnum.NONLIFE);
            if (nonLifeLicense) {
              oicLicenseNonLifeNo = nonLifeLicense.licenseNo;
              oicNonLifeStartDate = nonLifeLicense.startedAt
                ? formatDate(nonLifeLicense.startedAt, DateFormat.buddhistDayMonthYearWithLeadingZero)
                : '';
              oicNonLifeEndDate = nonLifeLicense.expiredAt
                ? formatDate(nonLifeLicense.expiredAt, DateFormat.buddhistDayMonthYearWithLeadingZero)
                : '';
            }

            if (course.regulatorInfo?.licenseRenewal === LicenseRenewalEnum.RENEW4) {
              if (lifeLicense && nonLifeLicense) {
                oicExtendYearType = OICExtendYearTypeEnum.BOTH;
              } else if (lifeLicense) {
                oicExtendYearType = OICExtendYearTypeEnum.LIFE;
              } else if (nonLifeLicense) {
                oicExtendYearType = OICExtendYearTypeEnum.NONLIFE;
              }
              oicReductionType = course.regulatorInfo.isDeduct ? OICReductionTypeEnum.YES : OICReductionTypeEnum.NO;
            }
          }

          if (responseValidate.regulatorProfile) {
            regulatorProfile.prefix = responseValidate.regulatorProfile.prefix;
            regulatorProfile.firstname = responseValidate.regulatorProfile.firstname;
            regulatorProfile.lastname = responseValidate.regulatorProfile.lastname;
          }
        }

        if (objectiveType === CourseObjectiveTypeEnum.TRAINING && regulatorInfo.regulator === RegulatorEnum.TSI) {
          if (activeLicenses) {
            const tsiLicenses = activeLicenses.find((val) => val.licenseType === LicenseTypeEnum.INVESTMENT);
            if (tsiLicenses) {
              tsiLicenseNo = tsiLicenses.licenseNo;
              tsiLicenseType = tsiLicenses.type ?? '';
              tsiStartDate = tsiLicenses.startedAt
                ? formatDate(tsiLicenses.startedAt, DateFormat.buddhistDayMonthYearWithLeadingZero)
                : '';
              tsiEnddate = tsiLicenses.expiredAt
                ? formatDate(tsiLicenses.expiredAt, DateFormat.buddhistDayMonthYearWithLeadingZero)
                : '';
            }
          }
        }

        const preEnrollmentTransactionPayload = {
          ...defaultPreEnrollmentPayload,
          username: user.username,
          email: user.email.toLowerCase(),
          mobile: user.profile.mobilePhoneNumber,
          prefix: user.profile.salute,
          firstname: user.profile.firstname,
          lastname: user.profile.lastname,
          last4DigitCitizenId: user.citizenId.slice(-4),
          tsiLicenseNo,
          tsiLicenseType,
          tsiStartDate,
          tsiEnddate,
          oicLicenseLifeNo,
          oicLifeStartDate,
          oicLifeEndDate,
          oicLicenseNonLifeNo,
          oicNonLifeStartDate,
          oicNonLifeEndDate,
          oicExtendYearType,
          oicReductionType,
          citizenId: user.citizenId,
          courseCode: course.code,
          regulatorProfile,
        };

        const preEnrollmentTransactionId =
          await this.transactionCounterRepository.getLastValuePreEnrollmentTransactionId();

        const preEnrollmentTransactionModel = await PreEnrollmentTransaction.new({
          id: preEnrollmentTransactionId,
          jobId: '',
          roundId: `${roundId}` || '',
          preAssignContentId: '',
          isCheckRegulator: true,
          status: PreEnrollmentTransactionStatusEnum.PASSED,
          errorMsg: '',
          warnMsg: '',
          payload: preEnrollmentTransactionPayload,
          warnList: [],
          businessType: BusinessTypeEnum.B2B,
          contentItems: [],
          isUpdatedRetailOrder: false,
          preEnrollmentReservationId: '',
          organizationId: course.organizationId,
          paymentType: PreEnrollmentTransactionPaymentTypeEnum.NON_CREDIT,
          enrollBy: PreEnrollmentTransactionEnrollByEnum.SELF,
          autoBulkOparation: [PreEnrollmentTransactionAutoBulkOparationEnum.ENROLLMENT],
          userId,
          enrollType: EnrollTypeEnum.VOLUNTARY,
          externalContentType: isActiveCoursePlanPackage
            ? ExternalContentTypeEnum.PLAN_PACKAGE
            : ExternalContentTypeEnum.NONE,
        });

        this.preEnrollmentTransactionRepository.save(preEnrollmentTransactionModel, opts);

        await this.syncEnrollmentWithLearningPathEnrollment(
          userId,
          courseId,
          user.organizationId,
          preEnrollmentTransactionId,
          course.enrollType,
          registerFrom,
          opts,
        );

        const enrollmentRegulatorReportId =
          await this.transactionCounterRepository.getLastValueEnrollmentRegulatorReportId();
        const enrollmentRegulatorReportModel = await EnrollmentRegulatorReport.new({
          id: `EID${enrollmentRegulatorReportId}`,
          courseId,
          preEnrollmentTransactionId,
        });
        await this.enrollmentRegulatorReportRepository.save(enrollmentRegulatorReportModel, opts);

        const courseItemCriteriaConfigs = await this.courseItemCriteriaConfigRepository.find({
          courseVersionId: course.courseVersion.id,
        });

        let parts: PartParams[] = [];
        parts = await this.courseService.getPartByCourseId(
          course.id,
          course.contentProviderType,
          course.courseVersion.id,
        );
        parts = this.courseService.combineCourseItemCriteriaConfigToPart(parts, courseItemCriteriaConfigs);
        parts = this.courseService.removeUnavailableContent(parts);

        const emailData: MailPayloadPreEnrollmentWelcomeEmailParams = {
          fullName: `${user.profile?.firstname} ${user.profile?.lastname}`,
          courseName: course.courseVersion?.name ?? '',
          startDate: round.roundDate,
          endDate: course.courseVersion?.expiryDay
            ? date(round.roundDate).add(course.courseVersion?.expiryDay, 'day').toDate()
            : null,
          thumbnailUrl: course.thumbnailUrl ?? '',
          url: course.url,
          parts,
          courseVersion: course.courseVersion,
        };

        await this.sendSelfPreEnrollmentNotification(
          { course, preEnrollment: preEnrollmentTransactionModel, round },
          opts,
        );

        this.sendPreEnrollmentEmail({ receiverEmail: user.email, emailData, organization });
        //!end implement notification

        await session.commitTransaction();

        return {
          preEnrollmentTransactionId: preEnrollmentTransactionModel.id,
          status: preEnrollmentTransactionModel.status,
        };
      }
    } catch (error: any) {
      await session.abortTransaction();
      throw error;
    } finally {
      session.endSession();
    }
  }

  private isRoundValid(courseId: GenericID, round: Round): boolean {
    if (!round || !round.courseIds.includes(courseId)) {
      return false;
    }

    const today = date().toDate();
    const firstRegisDate = date(round.firstRegistrationDate).toDate();
    const lastRegisDate: Date = date(round.lastRegistrationDate).toDate();

    if (isBefore(today, firstRegisDate) || isAfter(today, lastRegisDate)) {
      return false;
    }

    return true;
  }

  private async getLatestEnrollment(userId: GenericID, courseId: GenericID): Promise<Nullable<Enrollment>> {
    const [enrollment] = await this.enrollmentRepository.aggregate([
      {
        $match: {
          userId,
          courseId,
        },
      },
      {
        $sort: { createdAt: -1 },
      },
      {
        $limit: 1,
      },
    ]);

    return enrollment;
  }

  private async getLearningPathEnrollmentWithLearningPathVersion(
    userId: GenericID,
    courseId: GenericID,
    organizationId: GenericID,
  ): Promise<any> {
    const aggregatePipeline: Record<string, unknown>[] = [
      {
        $match: {
          userId,
          organizationId,
          status: {
            $in: [LearningPathEnrollmentStatusEnum.ASSIGNED, LearningPathEnrollmentStatusEnum.IN_PROGRESS],
          },
        },
      },
      {
        $lookup: {
          from: DBCollectionEnum.LEARNING_PATH_VERSIONS,
          let: {
            learningPathId: '$learningPathId',
            version: '$version',
          },
          pipeline: [
            {
              $match: {
                $expr: {
                  $and: [
                    {
                      $eq: ['$learningPathId', '$$learningPathId'],
                    },
                    {
                      $eq: ['$version', '$$version'],
                    },
                    {
                      $ne: ['$status', LearningPathVersionStatusEnum.DRAFT],
                    },
                  ],
                },
              },
            },
            {
              $match: {
                'contents.courseId': courseId,
              },
            },
          ],
          as: 'learningPathVersion',
        },
      },
      {
        $project: {
          _id: 0,
          id: 1,
          contentProgress: 1,
          learningPathVersion: 1,
          status: 1,
        },
      },
    ];

    const learningPathEnrollments = await this.learningPathEnrollmentRepository.aggregate(aggregatePipeline);

    if (!isEmpty(learningPathEnrollments)) {
      const learningPathEnrollmentWithLearningPathVersion = learningPathEnrollments.filter(
        (item: { learningPathVersion: LearningPathVersion }) => !isEmpty(item.learningPathVersion),
      );
      return learningPathEnrollmentWithLearningPathVersion;
    }

    return [];
  }

  private async getExpiredOrRejectedEnrollmentIdsByCourseId(
    userId: GenericID,
    courseId: GenericID,
  ): Promise<GenericID[]> {
    const enrollments = await this.enrollmentRepository.find(
      {
        userId,
        courseId,
        status: { $in: [EnrollmentStatusEnum.EXPIRED, EnrollmentStatusEnum.REJECTED] },
      },
      { projection: { id: 1, _id: 0 } },
    );

    return enrollments.map((item: { id: GenericID }) => item.id);
  }

  private async syncEnrollmentWithLearningPathEnrollment(
    userId: GenericID,
    courseId: GenericID,
    organizationId: GenericID,
    newId: GenericID,
    registerType: CourseEnrollTypeEnum,
    registerFrom: LearningPathContentProgressRegisterFromEnum,
    opts: UpdateOneOptions,
  ): Promise<void> {
    const learningPathEnrollmentHaveThisCourseList = await this.getLearningPathEnrollmentWithLearningPathVersion(
      userId,
      courseId,
      organizationId,
    );

    if (learningPathEnrollmentHaveThisCourseList.length) {
      const operations = [];

      const expireOrRejectEnrollmentIds = await this.getExpiredOrRejectedEnrollmentIdsByCourseId(userId, courseId);

      for (const item of learningPathEnrollmentHaveThisCourseList) {
        const { id, contentProgress, status } = item;

        const isDuplicateCompleteCourse = contentProgress.some(
          (val: { courseId: GenericID; status: LearningPathContentProgressStatusEnum }) =>
            val.courseId === courseId && val.status === LearningPathContentProgressStatusEnum.COMPLETED,
        );

        if (isDuplicateCompleteCourse) continue;

        const contentProgressExpireList = contentProgress
          .filter((val: { enrollmentId: GenericID }) => expireOrRejectEnrollmentIds.includes(val.enrollmentId))
          .map((val: { enrollmentId: GenericID }) => val.enrollmentId);

        if (contentProgressExpireList.length) {
          operations.push({
            updateOne: {
              filter: { id },
              update: {
                $pull: {
                  contentProgress: {
                    enrollmentId: { $in: contentProgressExpireList },
                  },
                },
              },
            },
          });
        }

        const duplicatePreEnrollment = contentProgress.find(
          (val: { courseId: GenericID; status: LearningPathContentProgressStatusEnum }) =>
            val.courseId === courseId && val.status === LearningPathContentProgressStatusEnum.PRE_ENROLL,
        );

        if (duplicatePreEnrollment) {
          operations.push({
            updateOne: {
              filter: { id },
              update: {
                $pull: {
                  contentProgress: {
                    preEnrollmentId: duplicatePreEnrollment.preEnrollmentId,
                  },
                },
              },
            },
          });
        }

        const now = date().toDate();
        const contentProgressId =
          registerType === CourseEnrollTypeEnum.IMMEDIATE
            ? { enrollmentId: newId, preEnrollmentId: null }
            : { preEnrollmentId: newId, enrollmentId: null };
        const newContentProgress = {
          ...contentProgressId,
          courseId,
          status:
            registerType === CourseEnrollTypeEnum.IMMEDIATE
              ? LearningPathContentProgressStatusEnum.IN_PROGRESS
              : LearningPathContentProgressStatusEnum.PRE_ENROLL,
          registerFrom,
          createdAt: now,
          updatedAt: now,
        };

        operations.push({
          updateOne: {
            filter: { id },
            update: {
              $push: { contentProgress: newContentProgress },
            },
          },
        });

        if (registerType === CourseEnrollTypeEnum.IMMEDIATE && status === LearningPathEnrollmentStatusEnum.ASSIGNED) {
          operations.push({
            updateOne: {
              filter: { id },
              update: {
                $set: {
                  status: LearningPathEnrollmentStatusEnum.IN_PROGRESS,
                  startedAt: now,
                },
              },
            },
          });
        }
      }

      if (operations.length) {
        await this.learningPathEnrollmentRepository.bulkWrite(operations, opts);
      }
    }
  }

  private async sendSelfEnrollmentNotification(
    params: {
      course: Course;
      enrollment: Enrollment;
    },
    opts?: UpdateOneOptions,
  ): Promise<void> {
    const { course, enrollment } = params;

    const message = buildSelfEnrollmentNotificationMessage({
      contentName: course.courseVersion?.name ?? '',
      expiredAt: enrollment.expiredAt,
    });

    const payload: UserNotificationPayloadParams = {
      mediaId: course.thumbnailMediaId,
      message,
      url: { code: course.url, enrollmentId: enrollment.id, preEnrollmentId: null },
    };

    await this.saveInAppUserNotification(
      {
        userId: enrollment.userId,
        organizationId: enrollment.organizationId,
        payload,
        type: UserNotificationTypeEnum.SELF_ENROLLMENT,
      },
      opts,
    );

    this.notificationService.sendInApplicationNotification(message, enrollment.organizationId);
  }

  private async sendSelfPreEnrollmentNotification(
    params: {
      course: Course;
      preEnrollment: PreEnrollmentTransaction;
      round: Round;
    },
    opts?: UpdateOneOptions,
  ): Promise<void> {
    const { course, preEnrollment, round } = params;

    const message = buildSelfPreEnrollmentNotificationMessage({
      contentName: course.courseVersion?.name || '',
      roundDate: round.roundDate,
      expiryDay: course.courseVersion?.expiryDay || 0,
    });

    const payload: UserNotificationPayloadParams = {
      mediaId: course.thumbnailMediaId,
      message,
      url: { code: course.url, enrollmentId: null, preEnrollmentId: preEnrollment.id },
    };

    await this.saveInAppUserNotification(
      {
        userId: preEnrollment.userId,
        organizationId: preEnrollment.organizationId,
        payload,
        type: UserNotificationTypeEnum.SELF_PRE_ENROLLMENT,
      },
      opts,
    );

    this.notificationService.sendInApplicationNotification(message, preEnrollment.organizationId);
  }

  private sendEnrollmentEmail(params: {
    receiverEmail: string;
    emailData: MailPayloadEnrollmentWelcomeEmailParams;
    organization: Organization;
    attachments?: MailAttachmentParams[];
  }): void {
    const { receiverEmail, emailData, organization } = params;
    const { notificationConfig } = organization;
    if (!notificationConfig?.isActive) return;

    this.notificationService.notifyEnrollmentWelcome(receiverEmail, emailData, organization);
  }

  private sendPreEnrollmentEmail(params: {
    receiverEmail: string;
    emailData: MailPayloadPreEnrollmentWelcomeEmailParams;
    organization: Organization;
    attachments?: MailAttachmentParams[];
  }): void {
    const { receiverEmail, emailData, organization } = params;
    const { notificationConfig } = organization;
    if (!notificationConfig?.isActive) return;

    this.notificationService.notifySelfPreEnrollmentWelcome(receiverEmail, emailData, organization);
  }

  private async saveInAppUserNotification(
    params: {
      userId: GenericID;
      organizationId: GenericID;
      payload: UserNotificationPayloadParams;
      type: UserNotificationTypeEnum;
    },
    opts?: UpdateOneOptions,
  ): Promise<void> {
    const { userId, organizationId, type, payload } = params;

    const inAppUserNotificationParams: UserNotificationParams = {
      userId,
      organizationId,
      type,
      isRead: false,
      isView: false,
      payload,
    };

    const inAppUserNotification: UserNotification = new UserNotification(inAppUserNotificationParams);
    await this.userNotificationRepository.save(inAppUserNotification, opts);
  }

  private async findUserPlanPackageContentActive(organizationId: GenericID, productSKUCourseId: GenericID) {
    const organizationPlanPackageContentList = await this.planRepository.findPlanPackageContent(organizationId);
    const organizationPlanPackageContentIds = organizationPlanPackageContentList.map((item) => item.planPackage.id);
    const planPackageContentCustomActive = await this.planPackageRepository.findPlanPackageContentCustomActive(
      productSKUCourseId,
      organizationPlanPackageContentIds,
    );
    const planPackageContentSubscriptionActive =
      await this.planPackageRepository.findPlanPackageContentSubscriptionActive(
        productSKUCourseId,
        organizationPlanPackageContentIds,
      );

    const planPackageContentActiveList = planPackageContentCustomActive.concat(planPackageContentSubscriptionActive);
    return planPackageContentActiveList;
  }

  private async findAvailableUserPlanPackageLicenseContent(
    userId: GenericID,
    planPackageContentActiveList: PlanPackageParams[],
  ): Promise<AvailablePeriodLicenseByUserParams[]> {
    const planPackageContentIds = planPackageContentActiveList.map((item) => item.id);
    const planPackageContentLicenseSOPendingNotExpired =
      await this.planPackageLicenseRepository.findPlanPackageLicenseSOPendingNotExpired(userId, planPackageContentIds);

    const planPackageContentLicenseSOApprovedNotExpired =
      await this.planPackageLicenseRepository.findPlanPackageLicenseSOApprovedNotExpired(userId, planPackageContentIds);

    const planPackageContentLicenseNotExpired = planPackageContentLicenseSOPendingNotExpired.concat(
      planPackageContentLicenseSOApprovedNotExpired,
    );

    const availablePeriodLicenseByUserList = getAvailablePeriodLicenseByUser(
      planPackageContentLicenseNotExpired,
      userId,
    );

    return availablePeriodLicenseByUserList;
  }

  private validateEnrollableCoursePlanPackage(
    availableUserPlanPackageLicenseContentList: AvailablePeriodLicenseByUserParams[],
    registerType: CourseEnrollTypeEnum,
  ) {
    if (registerType === CourseEnrollTypeEnum.IMMEDIATE) {
      const isActivePlanPackageLicense = checkHavePlanPackageLicenseAvailableToday(
        availableUserPlanPackageLicenseContentList,
      );
      if (!isActivePlanPackageLicense) {
        throw Exception.new({
          code: Code.BAD_REQUEST_ERROR,
          message: 'Plan package license is not available',
          domain: DOMAIN.PLAN_PACKAGE,
        });
      }
    }

    if (registerType === CourseEnrollTypeEnum.PRE_ENROLL && availableUserPlanPackageLicenseContentList.length === 0) {
      throw Exception.new({
        code: Code.BAD_REQUEST_ERROR,
        message: 'Plan package license to access course is expired',
        domain: DOMAIN.PLAN_PACKAGE,
      });
    }
  }

  private validateCourse(course: Course, isActiveCoursePlanPackage: boolean) {
    const { isSelfEnrollEnabled, isEnabled } = course;
    const isPublished = course.courseVersion?.status === CourseVersionStatusEnum.PUBLISHED;

    const isValidCourse = isEnabled && isPublished && isSelfEnrollEnabled;

    if (
      !isValidCourse ||
      (!isActiveCoursePlanPackage && course.contentProviderType === ContentProviderTypeEnum.EXTERNAL)
    ) {
      throw Exception.new({
        code: Code.BAD_REQUEST_ERROR,
        message: 'Course invalid',
        data: { courseId: course.id },
        domain: DOMAIN.COURSE,
      });
    }
  }
}
