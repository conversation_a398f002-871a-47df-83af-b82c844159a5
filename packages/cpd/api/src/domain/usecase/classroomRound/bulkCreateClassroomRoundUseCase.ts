import { GenericID } from '@iso/constants/commonTypes';
import { DateFormat, TimeZoneEnum } from '@iso/helpers/dateUtils';
import { ClassroomLocationTypeEnum } from '@iso/lms/enums/classroomLocation.enum';
import { ClassroomRoundStatusEnum } from '@iso/lms/enums/classroomRound.enum';
import { OrganizationTransactionCounterEnum } from '@iso/lms/enums/organization.enum';
import { ClassroomLocation } from '@iso/lms/models/classroomLocation.model';
import { ClassroomRound } from '@iso/lms/models/classroomRound.model';
import { inject, injectable } from 'inversify';
import { union } from 'lodash';

import { ApiServerConfig } from '@configs/apiServerConfig';
import { ClassroomRoundErrorCode } from '@core/commons/code';
import { Exception } from '@core/commons/exception/Exception';
import { ClassroomLocationEnabledWaitlistEnum } from '@entities/constants/classroomLocation';
import { ClassroomRoundWithLocationParams, XlsxClassroomRoundParams } from '@entities/constants/classroomRound';
import { CourseVersionStatusEnum } from '@entities/constants/courseVersion';
import {
  ClassroomDIToken,
  CourseDIToken,
  InstructorDIToken,
  MaterialMediaDIToken,
  OrganizationDIToken,
} from '@entities/constants/di';
import { DOMAIN } from '@entities/constants/error';
import { KeyColumnBulkClassroomRoundEnum } from '@entities/constants/excel';
import { MaterialMediaStatusEnum } from '@entities/constants/materialMedia';
import { TYPES } from '@entities/constants/types';
import { ImportBulkParams } from '@entities/constants/user';
import { IBulkCreateClassroomRoundUseCase } from '@entities/interface/classroomRound.interface';
import {
  IClassroomLocationRepository,
  IClassroomRepository,
  IClassroomRoundRepository,
  ICourseVersionRepository,
  IInstructorRepository,
  IOrganizationRepository,
} from '@entities/interface/repository';
import {
  convertFormatDateToYearMonthDay,
  date,
  parseDate,
  validateFormatRoundDateTime,
} from '@entities/services/dateUtils';
import { trimOrDefault } from '@entities/services/utility';
import { SpreadsheetService } from '@infrastructure/spreadsheet';

@injectable()
export class BulkCreateClassroomRoundUseCase implements IBulkCreateClassroomRoundUseCase {
  constructor(
    @inject(MaterialMediaDIToken.ClassroomRepository)
    private readonly classroomRepository: IClassroomRepository,
    @inject(ClassroomDIToken.ClassroomRoundRepository)
    private readonly classroomRoundRepository: IClassroomRoundRepository,
    @inject(ClassroomDIToken.ClassroomLocationRepository)
    private readonly classroomLocationRepository: IClassroomLocationRepository,
    @inject(CourseDIToken.CourseVersionRepository)
    private readonly courseVersionRepository: ICourseVersionRepository,
    @inject(InstructorDIToken.InstructorRepository)
    private readonly instructorRepository: IInstructorRepository,
    @inject(OrganizationDIToken.OrganizationRepository)
    private readonly organizationRepository: IOrganizationRepository,
    // Service
    @inject(TYPES.SpreadsheetService)
    private readonly spreadsheetService: SpreadsheetService,
  ) {}

  async execute(payload: ImportBulkParams): Promise<void> {
    const { file, organizationId } = payload;
    const dataSpreadSheet = this.spreadsheetService.readFromPrimaryWorksheet(file.data, {
      firstRowAsHeader: true,
    });

    const { data: dataSheet, headingRow } = dataSpreadSheet;

    const headingText = [
      KeyColumnBulkClassroomRoundEnum.CLASSROOM_CODE,
      KeyColumnBulkClassroomRoundEnum.DATE,
      KeyColumnBulkClassroomRoundEnum.START_TIME,
      KeyColumnBulkClassroomRoundEnum.END_TIME,
      KeyColumnBulkClassroomRoundEnum.LOCATION_FORMAT,
      KeyColumnBulkClassroomRoundEnum.LOCATION_DETAIL,
      KeyColumnBulkClassroomRoundEnum.MAXIMUM_USER,
      KeyColumnBulkClassroomRoundEnum.ENABLE_WAITLIST,
      KeyColumnBulkClassroomRoundEnum.INSTRUCTORS,
    ];

    this.spreadsheetService.validateHeadingRowText(headingRow, headingText, DOMAIN.CLASSROOM_ROUND);

    const dataRows = this.createRoundDataRows(dataSheet);
    this.spreadsheetService.validateEmptyRow(dataRows.length, DOMAIN.CLASSROOM_ROUND);
    this.spreadsheetService.validateUploadRowLimit(
      dataRows.length,
      ApiServerConfig.BULK_UPLOAD_LIMIT,
      DOMAIN.CLASSROOM_ROUND,
    );

    const classroomList = await this.classroomRepository.aggregate([
      {
        $match: {
          organizationId,
        },
      },
      {
        $lookup: {
          from: 'material-medias',
          let: { materialMediaId: '$materialMediaId' },
          pipeline: [
            {
              $match: {
                $and: [
                  { $expr: { $eq: ['$id', '$$materialMediaId'] } },
                  { $expr: { $eq: ['$status', MaterialMediaStatusEnum.PUBLISHED] } },
                ],
              },
            },
          ],
          as: 'materialMedia',
        },
      },
      {
        $unwind: '$materialMedia',
      },
      {
        $project: {
          id: 1,
          materialMediaId: '$materialMedia.id',
          materialMediaCode: '$materialMedia.code',
        },
      },
      {
        $lookup: {
          from: 'course-items',
          let: { materialMediaId: '$materialMediaId' },
          pipeline: [
            {
              $match: {
                $and: [{ $expr: { $eq: ['$materialMediaId', '$$materialMediaId'] } }],
              },
            },
          ],
          as: 'courseItem',
        },
      },
      {
        $unwind: '$courseItem',
      },
      {
        $project: {
          id: 1,
          materialMediaId: 1,
          materialMediaCode: 1,
          courseItemId: '$courseItem.id',
          partId: '$courseItem.partId',
        },
      },
      {
        $lookup: {
          from: 'parts',
          let: { partId: '$partId' },
          pipeline: [
            {
              $match: {
                $and: [{ $expr: { $eq: ['$id', '$$partId'] } }],
              },
            },
          ],
          as: 'part',
        },
      },
      {
        $unwind: '$part',
      },
      {
        $project: {
          id: 1,
          materialMediaId: 1,
          materialMediaCode: 1,
          courseItemId: 1,
          partId: '$part.id',
          courseVersionId: '$part.courseVersionId',
        },
      },
    ]);

    const courseVersionIds = union(classroomList.map((val: Record<string, string>) => val.courseVersionId));

    const courseVersionList = await this.courseVersionRepository.aggregate([
      {
        $match: {
          id: { $in: courseVersionIds },
          status: { $in: [CourseVersionStatusEnum.PREVIEW, CourseVersionStatusEnum.PUBLISHED] },
        },
      },
      {
        $project: {
          id: 1,
          courseId: 1,
        },
      },
    ]);

    const instructorList = await this.instructorRepository.aggregate([
      {
        $match: {
          organizationId,
        },
      },
      {
        $project: {
          id: 1,
          firstname: 1,
          lastname: 1,
        },
      },
    ]);

    const classroomRoundWithLocation = this.validate(dataRows, classroomList, courseVersionList, instructorList);

    const output = await this.mergeWithExistingRounds(classroomRoundWithLocation, organizationId);

    if (output.classroomRounds.length > 0) {
      await this.classroomRoundRepository.saveMany(output.classroomRounds);
    }

    if (output.classroomLocations.length > 0) {
      await this.classroomLocationRepository.saveMany(output.classroomLocations);
    }
  }

  private createRoundDataRows(spreadsheet: string[][]): XlsxClassroomRoundParams[] {
    const result: XlsxClassroomRoundParams[] = [];

    for (let i = 0; i < spreadsheet.length; i++) {
      if (!spreadsheet[i].length) break;

      const [
        classroomCode,
        startDate,
        startTime,
        endTime,
        locationFormat,
        locationDetail,
        maximumUser,
        enableWaitlist,
        instructor,
      ] = spreadsheet[i];

      const item: XlsxClassroomRoundParams = {
        classroomCode: trimOrDefault(classroomCode).toUpperCase(),
        startDate: trimOrDefault(startDate),
        startTime: trimOrDefault(startTime),
        endTime: trimOrDefault(endTime),
        locationFormat: trimOrDefault(locationFormat).toUpperCase(),
        locationDetail: trimOrDefault(locationDetail),
        maximumUser: trimOrDefault(maximumUser),
        enableWaitlist: trimOrDefault(enableWaitlist).toUpperCase(),
        instructors: [],
      };

      if (instructor) {
        const tempRefs = instructor.split(';');
        for (const tempRef of tempRefs) {
          item.instructors.push(trimOrDefault(tempRef).toUpperCase());
        }
      }
      result.push(item);
    }

    return result;
  }

  private validate(
    dataRows: XlsxClassroomRoundParams[],
    classRoomList: Record<string, string>[],
    courseVersionList: Record<string, string>[],
    instructorList: Record<string, string>[],
  ): ClassroomRoundWithLocationParams[] {
    const result: ClassroomRoundWithLocationParams[] = [];

    const rows = dataRows.length;
    for (let index = 0; index < rows; index++) {
      const rawData = dataRows[index];
      const rowNumber = index + 1;

      if (!rawData.classroomCode) {
        throw Exception.new({
          code: ClassroomRoundErrorCode.BULK_CLASSROOM_ROUND_INCOMPLETE_CLASSROOM_CODE,
          data: { row: rowNumber },
          domain: DOMAIN.CLASSROOM_ROUND,
        });
      }

      const classroomItem = classRoomList.find((val) => val.materialMediaCode === rawData.classroomCode);
      if (!classroomItem) {
        throw Exception.new({
          code: ClassroomRoundErrorCode.BULK_CLASSROOM_ROUND_INVALID_CLASSROOM_CODE,
          data: { row: rowNumber },
          domain: DOMAIN.CLASSROOM_ROUND,
        });
      }

      if (!rawData.locationFormat) {
        throw Exception.new({
          code: ClassroomRoundErrorCode.BULK_CLASSROOM_ROUND_INCOMPLETE_LOCATION_FORMAT,
          data: { row: rowNumber },
          domain: DOMAIN.CLASSROOM_ROUND,
        });
      }

      const isValidClassroomLocationType = [
        ClassroomLocationTypeEnum.ONSITE,
        ClassroomLocationTypeEnum.VIRTUAL,
        ClassroomLocationTypeEnum.REMOTE,
      ].includes(rawData.locationFormat as ClassroomLocationTypeEnum);

      if (!isValidClassroomLocationType) {
        throw Exception.new({
          code: ClassroomRoundErrorCode.BULK_CLASSROOM_ROUND_INVALID_LOCATION_FORMAT,
          data: { row: rowNumber },
          domain: DOMAIN.CLASSROOM_ROUND,
        });
      }

      if (!rawData.startDate) {
        throw Exception.new({
          code: ClassroomRoundErrorCode.BULK_CLASSROOM_ROUND_INCOMPLETE_START_DATE,
          data: { row: rowNumber },
          domain: DOMAIN.CLASSROOM_ROUND,
        });
      }

      this.validateDateFormat(
        rawData.startDate,
        false,
        rowNumber,
        ClassroomRoundErrorCode.BULK_CLASSROOM_ROUND_INVALID_START_DATE,
      );

      if (!rawData.startTime) {
        throw Exception.new({
          code: ClassroomRoundErrorCode.BULK_CLASSROOM_ROUND_INCOMPLETE_START_TIME,
          data: { row: rowNumber },
          domain: DOMAIN.CLASSROOM_ROUND,
        });
      }

      const startTimeText = `${rawData.startDate} ${rawData.startTime}`;

      this.validateDateFormat(
        startTimeText,
        true,
        rowNumber,
        ClassroomRoundErrorCode.BULK_CLASSROOM_ROUND_INVALID_START_TIME,
      );

      const startedAtText = this.convertFormatDateBuddistToChrist(convertFormatDateToYearMonthDay(startTimeText));

      const startedAt = parseDate(
        startedAtText,
        TimeZoneEnum.Bangkok,
        DateFormat.yearMonthDayHourMinuteWithLeadingZero,
      );

      const dateMoreThanOneHour = date().add(1, 'hour').second(0).millisecond(0).toDate();

      if (date(startedAt).isBefore(dateMoreThanOneHour)) {
        throw Exception.new({
          code: ClassroomRoundErrorCode.BULK_CLASSROOM_ROUND_DATE_AND_TIME_MORE_THAN_CURRENT_DATE,
          data: { row: rowNumber },
          domain: DOMAIN.CLASSROOM_ROUND,
        });
      }

      if (!rawData.endTime) {
        throw Exception.new({
          code: ClassroomRoundErrorCode.BULK_CLASSROOM_ROUND_INCOMPLETE_END_TIME,
          data: { row: rowNumber },
          domain: DOMAIN.CLASSROOM_ROUND,
        });
      }

      const endTimeText = `${rawData.startDate} ${rawData.endTime}`;

      this.validateDateFormat(
        endTimeText,
        true,
        rowNumber,
        ClassroomRoundErrorCode.BULK_CLASSROOM_ROUND_INVALID_END_TIME,
      );

      const expiredAtText = this.convertFormatDateBuddistToChrist(convertFormatDateToYearMonthDay(endTimeText));

      const expiredAt = parseDate(
        expiredAtText,
        TimeZoneEnum.Bangkok,
        DateFormat.yearMonthDayHourMinuteWithLeadingZero,
      );

      if (date(expiredAt).isSameOrBefore(startedAt)) {
        throw Exception.new({
          code: ClassroomRoundErrorCode.BULK_CLASSROOM_ROUND_START_TIME_MUST_BEFORE_END_TIME,
          data: { row: rowNumber },
          domain: DOMAIN.CLASSROOM_ROUND,
        });
      }

      if (!rawData.locationDetail) {
        throw Exception.new({
          code: ClassroomRoundErrorCode.BULK_CLASSROOM_ROUND_INCOMPLETE_LOCATION_DETAIL,
          data: { row: rowNumber },
          domain: DOMAIN.CLASSROOM_ROUND,
        });
      }

      const maximumCharacter = 200;
      if (rawData.locationDetail.length > maximumCharacter) {
        throw Exception.new({
          code: ClassroomRoundErrorCode.BULK_CLASSROOM_ROUND_LIMIT_CHARACTER_LOCATION_DETAIL,
          data: { row: rowNumber },
          domain: DOMAIN.CLASSROOM_ROUND,
        });
      }

      const maximumUser = Number(rawData.maximumUser);
      const isValidMaximumUser = Number.isInteger(maximumUser) && maximumUser > 0;

      if (rawData.maximumUser && !isValidMaximumUser) {
        throw Exception.new({
          code: ClassroomRoundErrorCode.BULK_CLASSROOM_ROUND_INVALID_MAXIMUM_USER,
          data: { row: rowNumber },
          domain: DOMAIN.CLASSROOM_ROUND,
        });
      }

      const isValidEnableWaitlist = [
        ClassroomLocationEnabledWaitlistEnum.TRUE,
        ClassroomLocationEnabledWaitlistEnum.FALSE,
      ].includes(rawData.enableWaitlist as ClassroomLocationEnabledWaitlistEnum);

      const isEnabledWaitlist = rawData.enableWaitlist === ClassroomLocationEnabledWaitlistEnum.TRUE;

      if (rawData.enableWaitlist && !isValidEnableWaitlist) {
        throw Exception.new({
          code: ClassroomRoundErrorCode.BULK_CLASSROOM_ROUND_INVALID_ENABLE_WAITLIST,
          data: { row: rowNumber },
          domain: DOMAIN.CLASSROOM_ROUND,
        });
      }

      if (isEnabledWaitlist && (!rawData.maximumUser || (rawData.maximumUser && !isValidMaximumUser))) {
        throw Exception.new({
          code: ClassroomRoundErrorCode.BULK_CLASSROOM_ROUND_ENABLE_WAITLIST_MUST_REQUIRED_MAXIMUM_USER,
          data: { row: rowNumber },
          domain: DOMAIN.CLASSROOM_ROUND,
        });
      }

      const instructorIds: GenericID[] = [];
      const instructorError = [];
      for (const instructor of rawData.instructors) {
        const instructorData = instructorList.find(
          (val) => `${val.firstname} ${val.lastname ? val.lastname : ''}`.trim().toUpperCase() === instructor,
        );
        if (instructorData) {
          instructorIds.push(instructorData.id);
        } else {
          instructorError.push(instructor);
        }
      }

      if (instructorError.length > 0) {
        throw Exception.new({
          code: ClassroomRoundErrorCode.BULK_CLASSROOM_ROUND_INVALID_INSTRUCTOR,
          data: { row: rowNumber, text: instructorError.join(', ') },
          domain: DOMAIN.CLASSROOM_ROUND,
        });
      }

      const courseVersionItems = courseVersionList.filter((val) => val.id === classroomItem.courseVersionId);
      const courseIds = courseVersionItems.map((val) => val.courseId);

      const item: ClassroomRoundWithLocationParams = {
        materialMediaId: classroomItem.materialMediaId,
        courseIds,
        startedAt,
        expiredAt,
        type: ClassroomLocationTypeEnum[rawData.locationFormat as ClassroomLocationTypeEnum],
        location: rawData.locationDetail,
        maximumUser: parseInt(rawData.maximumUser) || 0,
        isEnabledWaitlist,
        instructorIds,
      };

      result.push(item);
    }

    return result;
  }

  private validateDateFormat(dateText: string, isValidateTime: boolean, rowNumber: number, errorCode: any): void {
    const isValidFormat = validateFormatRoundDateTime(dateText, isValidateTime);
    const dateYearMonthDayFormat = convertFormatDateToYearMonthDay(dateText);
    const isValidDate = date(this.convertFormatDateBuddistToChrist(dateYearMonthDayFormat)).isValid();

    if (isValidDate && isValidFormat) return;

    throw Exception.new({
      code: errorCode,
      data: { row: rowNumber },
      domain: DOMAIN.CLASSROOM_ROUND,
    });
  }

  private async mergeWithExistingRounds(
    datas: ClassroomRoundWithLocationParams[],
    organizationId: GenericID,
  ): Promise<{ classroomRounds: ClassroomRound[]; classroomLocations: ClassroomLocation[] }> {
    if (!datas || !datas.length) return { classroomRounds: [], classroomLocations: [] };

    const classroomRounds: ClassroomRound[] = [];
    const classroomLocations: ClassroomLocation[] = [];

    const findByDateQuery = datas.map((round) => ({
      startedAt: round.startedAt,
      expiredAt: round.expiredAt,
      organizationId,
    }));

    const existingRounds = await this.classroomRoundRepository.find({
      $or: findByDateQuery,
      status: { $in: [ClassroomRoundStatusEnum.PRE_ENROLL, ClassroomRoundStatusEnum.IN_PROGRESS] },
    });

    const endOfDayInBangkok = date().endOf('day').toDate();

    let runningNumLocationCode =
      (await this.organizationRepository.findOneTransactionCounterByKey(
        organizationId,
        OrganizationTransactionCounterEnum.CLASSROOM_LOCATION,
      )) || 0;

    for (const newData of datas) {
      const oldClassroomAndRound = [...existingRounds, ...classroomRounds].find(
        (val) =>
          val.materialMediaId === newData.materialMediaId &&
          this.isSameRound(val.startedAt, val.expiredAt, newData.startedAt, newData.expiredAt),
      );

      let newStatus = ClassroomRoundStatusEnum.IN_PROGRESS;
      if (date(newData.startedAt).isAfter(endOfDayInBangkok)) {
        newStatus = ClassroomRoundStatusEnum.PRE_ENROLL;
      } else {
        newStatus = ClassroomRoundStatusEnum.IN_PROGRESS;
      }

      runningNumLocationCode++;

      const classroomLocationCode = `CLASS_LOCATION_${runningNumLocationCode}`;

      if (!oldClassroomAndRound) {
        const classroomRound = await ClassroomRound.new({
          organizationId,
          materialMediaId: newData.materialMediaId,
          status: newStatus,
          courseIds: newData.courseIds,
          startedAt: newData.startedAt,
          expiredAt: newData.expiredAt,
        });

        const classroomLocation = await ClassroomLocation.new({
          classroomRoundId: classroomRound.id,
          code: classroomLocationCode,
          type: newData.type,
          location: newData.location,
          maximumUser: newData.maximumUser,
          isEnabledWaitlist: newData.isEnabledWaitlist,
          instructorIds: newData.instructorIds,
        });

        classroomRounds.push(classroomRound);
        classroomLocations.push(classroomLocation);
      } else {
        oldClassroomAndRound.courseIds = newData.courseIds;

        const classroomLocation = await ClassroomLocation.new({
          classroomRoundId: oldClassroomAndRound.id,
          code: classroomLocationCode,
          type: newData.type,
          location: newData.location,
          maximumUser: newData.maximumUser,
          isEnabledWaitlist: newData.isEnabledWaitlist,
          instructorIds: newData.instructorIds,
        });

        classroomRounds.push(oldClassroomAndRound);
        classroomLocations.push(classroomLocation);
      }
    }

    await this.organizationRepository.findOneAndUpdate(
      { id: organizationId },
      {
        $set: {
          [`transactionCounter.${OrganizationTransactionCounterEnum.CLASSROOM_LOCATION}`]: runningNumLocationCode,
        },
      },
    );

    return { classroomRounds, classroomLocations };
  }

  private isSameRound(roundStartedAt: Date, roundExpiredAt: Date, otherStartedAt: Date, otherExpiredAt: Date): boolean {
    return (
      roundStartedAt.getTime() === otherStartedAt.getTime() && roundExpiredAt.getTime() === otherExpiredAt.getTime()
    );
  }

  private convertFormatDateBuddistToChrist(str: string): string {
    const [dateStr, timeStr] = str.split(' ');

    let splitSyntax = '-';
    if (dateStr.includes('/')) {
      splitSyntax = '/';
    }
    const [year, month, day] = dateStr.split(splitSyntax);
    const newDate = [parseInt(year) - 543, month, day].join(splitSyntax);
    return timeStr ? `${newDate} ${timeStr}` : newDate;
  }
}
