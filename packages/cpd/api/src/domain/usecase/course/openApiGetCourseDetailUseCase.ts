import { ObjectValue } from '@iso/constants/commonTypes';
import { ContentProviderTypeEnum } from '@iso/lms/enums/course.enum';
import { inject, injectable } from 'inversify';
import { sumBy } from 'lodash';

import { Code } from '@core/commons/code';
import { OrganizationStorageType } from '@core/commons/enum/organizationStorageEnum';
import { Exception } from '@core/commons/exception/Exception';
import { OpenApiGetCourseDetailParams, OutputOpenApiGetCourseDetailParams } from '@entities/constants/course';
import {
  CourseDIToken,
  InstructorDIToken,
  MediaDIToken,
  OrganizationDIToken,
  OrganizationStorageDIToken,
  ProductSKUDIToken,
} from '@entities/constants/di';
import { DOMAIN } from '@entities/constants/error';
import { TYPES } from '@entities/constants/types';
import { IOpenApiGetCourseDetailUseCase } from '@entities/interface/openApi.interface';
import {
  ICourseRepository,
  IInstructorRepository,
  IMediaRepository,
  IOrganizationRepository,
  IOrganizationStorageRepository,
  IProductSKUCourseRepository,
} from '@entities/interface/repository';
import { Storage as StorageService } from '@entities/services';
import { CourseService } from '@entities/services/courseService';

@injectable()
export class OpenApiGetCourseDetailUseCase implements IOpenApiGetCourseDetailUseCase {
  constructor(
    @inject(CourseDIToken.CourseRepository)
    private readonly courseRepository: ICourseRepository,
    @inject(OrganizationDIToken.OrganizationRepository)
    private readonly organizationRepository: IOrganizationRepository,
    @inject(ProductSKUDIToken.ProductSKUCourseRepository)
    private readonly productSKUCourseRepository: IProductSKUCourseRepository,
    @inject(InstructorDIToken.InstructorRepository)
    private readonly instructorRepository: IInstructorRepository,
    @inject(TYPES.CourseService)
    private readonly courseService: CourseService,
    @inject(OrganizationStorageDIToken.OrganizationStorageRepository)
    private readonly organizationStorageRepository: IOrganizationStorageRepository,
    @inject(MediaDIToken.MediaRepository)
    private readonly mediaRepository: IMediaRepository,
    @inject(OrganizationDIToken.OrganizationRepository) private readonly,
  ) {}

  async execute(params: OpenApiGetCourseDetailParams): Promise<OutputOpenApiGetCourseDetailParams> {
    const { organizationId, code } = params;

    const organization = await this.organizationRepository.findOne({
      id: organizationId,
    });

    if (!organization) {
      throw Exception.new({
        code: Code.ENTITY_NOT_FOUND_ERROR,
        message: 'Organization not found.',
        data: { id: organizationId },
        domain: DOMAIN.ORGANIZATION,
      });
    }

    const course = await this.courseRepository.getLatestPublishedCourseDetailByCode(code, organizationId);

    if (!course || !course.isEnabled || !course.courseVersion) {
      throw Exception.new({
        code: Code.ENTITY_NOT_FOUND_ERROR,
        message: 'Course does not exist',
        data: { courseCode: code },
        domain: DOMAIN.COURSE,
      });
    }

    const { courseVersion } = course;
    const { instructorIds } = courseVersion;
    let parts = [];
    let instructors: Array<{
      avatar: string;
      firstname: string;
      lastname: string;
      highlightDescription?: string;
      biology?: string;
    }> = [];

    if (course.contentProviderType === ContentProviderTypeEnum.EXTERNAL) {
      parts = await this.courseService.getPartByCourseId(course.id, ContentProviderTypeEnum.EXTERNAL, courseVersion.id);

      const [productSKUCourse] = await this.productSKUCourseRepository.aggregate([
        {
          $lookup: {
            from: 'courses',
            let: { productSKUCourseId: '$id' },
            pipeline: [
              {
                $match: {
                  $expr: {
                    $and: [{ $eq: ['$id', course.id] }, { $eq: ['$productSKUCourseId', '$$productSKUCourseId'] }],
                  },
                },
              },
            ],
            as: 'course',
          },
        },
        {
          $unwind: '$course',
        },
      ]);

      if (productSKUCourse) {
        const { instructors: productInstructors } = productSKUCourse;
        instructors = productInstructors;
      }
    } else {
      parts = await this.courseService.getPartByCourseId(course.id, ContentProviderTypeEnum.SELF, courseVersion.id);
      parts = await this.courseService.combineStorageAttachmentUrlToPart(course.organizationId, parts);
      parts = this.courseService.removeUnavailableContent(parts);

      if (instructorIds) {
        const instructorsEntity = await this.instructorRepository.find({
          id: {
            $in: instructorIds,
          },
        });
        const sortedInstructors = instructorIds.map((id) => {
          return instructorsEntity.find((instructor) => instructor.id === id);
        });
        instructors = sortedInstructors.map((instructor) => ({
          avatar: instructor.avatar,
          firstname: instructor.firstname,
          lastname: instructor.lastname,
          highlightDescription: instructor.highlightDescription,
          biology: instructor.biology,
        }));
      }
    }

    const preparedParts = parts.map((part) => {
      const { id, name, courseItems } = part;

      const preparedCourseItems = courseItems.map((courseItem) => {
        return {
          id: courseItem.id,
          name: courseItem.name,
          position: courseItem.position,
          type: courseItem.type,
          isEnabled: courseItem.isEnabled,
          duration: courseItem.duration,
          testType: courseItem?.testType,
          surveyType: courseItem?.surveyType,
          totalAttachment: courseItem.attachments?.length ?? 0,
          totalScore: courseItem.totalScore,
        };
      });

      return {
        id,
        name,
        courseItems: preparedCourseItems,
      };
    });

    const totalAttachment = sumBy(parts, (part) =>
      sumBy(
        part.courseItems,
        (courseItem: { attachments: Array<ObjectValue> }) => courseItem?.attachments?.length ?? 0,
      ),
    );

    const organizationStorage = await this.organizationStorageRepository.findOne({
      organizationId,
      storageType: OrganizationStorageType.RESOURCE,
    });

    const bucketUrl = StorageService.getBucketProviderUrl(
      organizationStorage?.storageProvider,
      organizationStorage?.bucket,
      organizationStorage?.additional?.region,
      organizationStorage?.rootDirectory,
    );

    const media = await this.mediaRepository.findOne({ id: course?.thumbnailMediaId });

    let thumbnailUrl = null;
    if (course?.contentProviderType === ContentProviderTypeEnum.EXTERNAL) {
      thumbnailUrl = StorageService.getExternalImagePath(course?.thumbnailUrl);
    } else {
      thumbnailUrl = StorageService.getImagePath(bucketUrl, {
        path: media?.path,
        filename: media?.filename,
      });
    }

    return {
      id: course.id,
      name: courseVersion.name,
      code: course.code,
      description: course.description,
      thumbnailUrl,
      expiryDay: courseVersion.expiryDay,
      instructors,
      regulatorInfo: {
        regulator: course.regulatorInfo?.regulator,
        licenseRenewal: course.regulatorInfo?.licenseRenewal,
        applicantType: course.regulatorInfo?.applicantType,
        licenseType: course.regulatorInfo?.licenseType,
        isDeduct: course.regulatorInfo?.isDeduct ?? false,
      },
      parts: preparedParts,
      totalCourseItems: courseVersion.totalCourseItems,
      totalVideos: courseVersion.totalVideos,
      totalQuizzes: courseVersion.totalQuizzes,
      totalArticles: courseVersion.totalArticles,
      totalSurveys: courseVersion.totalSurveys,
      totalDurationSec: courseVersion.totalDurationSec,
      totalFileDownload: totalAttachment,
    };
  }
}
