import { Nullable } from '@iso/constants/commonTypes';
import { ClassroomLocationEnrollmentStatusEnum } from '@iso/lms/enums/classroomLocationEnrollment.enum';
import { ClassroomRoundStatusEnum } from '@iso/lms/enums/classroomRound.enum';
import { injectable } from 'inversify';

import { FilterClassroomLocationEnrollmentStatusEnum } from '@entities/constants/classroomLocationEnrollment';
import {
  FilterClassroomRoundEnrollmentStatusEnum,
  FilterClassroomRoundStatusEnum,
} from '@entities/constants/classroomRound';
import { IClassroomService } from '@entities/interface/classroom.interface';
import { date } from '@entities/services/dateUtils';

@injectable()
export class ClassroomService implements IClassroomService {
  constructor() {}
  getClassroomRoundEnrollmentStatus(
    status: ClassroomRoundStatusEnum,
    startedAt: Date,
    expiredAt: Date,
    isUserEnroll: boolean,
  ): FilterClassroomRoundEnrollmentStatusEnum {
    const currentDate = date().toDate();

    if (status === ClassroomRoundStatusEnum.CANCELED) {
      return FilterClassroomRoundEnrollmentStatusEnum.CANCELED;
    }

    if (status === ClassroomRoundStatusEnum.COMPLETED) {
      return FilterClassroomRoundEnrollmentStatusEnum.COMPLETED;
    }

    if (date(currentDate).isBefore(startedAt)) {
      return FilterClassroomRoundEnrollmentStatusEnum.PRE_ENROLL;
    }

    if (date(currentDate).isSameOrAfter(startedAt) && date(currentDate).isSameOrBefore(expiredAt)) {
      return FilterClassroomRoundEnrollmentStatusEnum.IN_PROGRESS;
    }

    if (date(currentDate).isAfter(expiredAt) && isUserEnroll) {
      return FilterClassroomRoundEnrollmentStatusEnum.PENDING_RESULT;
    }

    return FilterClassroomRoundEnrollmentStatusEnum.CANCELED;
  }

  getClassroomRoundStatus(status: ClassroomRoundStatusEnum, startedAt: Date): FilterClassroomRoundStatusEnum {
    if (status === ClassroomRoundStatusEnum.CANCELED) {
      return FilterClassroomRoundStatusEnum.CANCELED;
    }

    const currentDate = date().toDate();
    const dateEditable = date(startedAt).subtract(1, 'hour').toDate();
    if (date(currentDate).isBefore(dateEditable)) {
      return FilterClassroomRoundStatusEnum.AVAILABLE;
    }

    return FilterClassroomRoundStatusEnum.EXPIRED;
  }

  getClassroomLocationEnrollmentStatus(
    status: Nullable<ClassroomLocationEnrollmentStatusEnum>,
    startedAt: Date,
    expiredAt: Date,
  ): FilterClassroomLocationEnrollmentStatusEnum {
    const currentDate = date().toDate();

    if (!status) {
      return FilterClassroomLocationEnrollmentStatusEnum.NOT_REGISTERED;
    }

    if (status === ClassroomLocationEnrollmentStatusEnum.CANCELED) {
      return FilterClassroomLocationEnrollmentStatusEnum.CANCELED;
    }

    if (status === ClassroomLocationEnrollmentStatusEnum.PASSED) {
      return FilterClassroomLocationEnrollmentStatusEnum.PASSED;
    }

    if (status === ClassroomLocationEnrollmentStatusEnum.NOT_PASS) {
      return FilterClassroomLocationEnrollmentStatusEnum.NOT_PASS;
    }

    if (date(currentDate).isBefore(startedAt)) {
      if (status === ClassroomLocationEnrollmentStatusEnum.PRE_ENROLL) {
        return FilterClassroomLocationEnrollmentStatusEnum.PRE_ENROLL;
      }

      const startedOfDay = date(startedAt).startOf('day').toDate();
      if (
        status === ClassroomLocationEnrollmentStatusEnum.WAITING_PRE_ENROLL &&
        date(currentDate).isSameOrAfter(startedOfDay)
      ) {
        return FilterClassroomLocationEnrollmentStatusEnum.CANCELED;
      }

      if (status === ClassroomLocationEnrollmentStatusEnum.WAITING_PRE_ENROLL) {
        return FilterClassroomLocationEnrollmentStatusEnum.WAITING_PRE_ENROLL;
      }

      if (status === ClassroomLocationEnrollmentStatusEnum.IN_PROGRESS) {
        return FilterClassroomLocationEnrollmentStatusEnum.PRE_ENROLL;
      }
    }

    if (date(currentDate).isSameOrAfter(startedAt) && date(currentDate).isSameOrBefore(expiredAt)) {
      if (status === ClassroomLocationEnrollmentStatusEnum.WAITING_PRE_ENROLL) {
        return FilterClassroomLocationEnrollmentStatusEnum.CANCELED;
      }

      return FilterClassroomLocationEnrollmentStatusEnum.IN_PROGRESS;
    }

    if (date(currentDate).isAfter(expiredAt)) {
      return FilterClassroomLocationEnrollmentStatusEnum.PENDING_RESULT;
    }

    return FilterClassroomLocationEnrollmentStatusEnum.CANCELED;
  }
}
