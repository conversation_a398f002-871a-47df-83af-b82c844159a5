import { ClassroomLocationEnrollmentStatusEnum } from '@iso/lms/enums/classroomLocationEnrollment.enum';
import { ClassroomRoundStatusEnum } from '@iso/lms/enums/classroomRound.enum';

import { FilterClassroomLocationEnrollmentStatusEnum } from '@entities/constants/classroomLocationEnrollment';
import {
  FilterClassroomRoundEnrollmentStatusEnum,
  FilterClassroomRoundStatusEnum,
} from '@entities/constants/classroomRound';
import { ClassroomService } from '@entities/services/classroomService';
import { date } from '@entities/services/dateUtils';

describe('Testing Classroom Service', () => {
  let service: ClassroomService;
  const currentDate = date().toDate();
  const mockBeforeRoundDate = {
    startedAt: date(currentDate).add(1, 'hour').toDate(),
    expiredAt: date(currentDate).add(3, 'hour').toDate(),
  };

  const mockBetweenRoundDate = {
    startedAt: date(currentDate).add(-1, 'hour').toDate(),
    expiredAt: date(currentDate).add(3, 'hour').toDate(),
  };

  const mockAfterRoundDate = {
    startedAt: date(currentDate).add(-1, 'hour').toDate(),
    expiredAt: date(currentDate).add(-3, 'hour').toDate(),
  };

  const mockOneDayBeforeStartRoundDate = {
    startedAt: date(currentDate).add(1, 'day').toDate(),
    expiredAt: date(currentDate).add(1, 'day').add(3, 'hour').toDate(),
  };

  const mock30MinuteBeforeStartRoundDate = {
    startedAt: date(currentDate).add(30, 'minute').toDate(),
    expiredAt: date(currentDate).add(3, 'hour').toDate(),
  };

  beforeEach(() => {
    service = new ClassroomService();
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('getClassroomRoundEnrollmentStatus function', () => {
    describe('Case: PRE_ENROLL', () => {
      it('when currentDate < startedAt and status = PRE_ENROLL, expect status = PRE_ENROLL', () => {
        const mockStatus = ClassroomRoundStatusEnum.PRE_ENROLL;
        const result = service.getClassroomRoundEnrollmentStatus(
          mockStatus,
          mockBeforeRoundDate.startedAt,
          mockBeforeRoundDate.expiredAt,
          true,
        );
        expect(result).toBe(FilterClassroomRoundEnrollmentStatusEnum.PRE_ENROLL);
      });

      it('when currentDate > startedAt and currentDate < expiredAt and status = PRE_ENROLL, expect status != PRE_ENROLL', () => {
        const mockStatus = ClassroomRoundStatusEnum.PRE_ENROLL;
        const result = service.getClassroomRoundEnrollmentStatus(
          mockStatus,
          mockBetweenRoundDate.startedAt,
          mockBetweenRoundDate.expiredAt,
          true,
        );
        expect(result).not.toBe(FilterClassroomRoundEnrollmentStatusEnum.PRE_ENROLL);
      });

      it('when currentDate > expiredAt and status = PRE_ENROLL, expect status != PRE_ENROLL', () => {
        const mockStatus = ClassroomRoundStatusEnum.PRE_ENROLL;
        const result = service.getClassroomRoundEnrollmentStatus(
          mockStatus,
          mockAfterRoundDate.startedAt,
          mockAfterRoundDate.expiredAt,
          true,
        );
        expect(result).not.toBe(FilterClassroomRoundEnrollmentStatusEnum.PRE_ENROLL);
      });
    });
    describe('Case: IN_PROGRESS', () => {
      it('when currentDate < startedAt and status = IN_PROGRESS, expect status != IN_PROGRESS', () => {
        const mockStatus = ClassroomRoundStatusEnum.IN_PROGRESS;
        const result = service.getClassroomRoundEnrollmentStatus(
          mockStatus,
          mockBeforeRoundDate.startedAt,
          mockBeforeRoundDate.expiredAt,
          true,
        );
        expect(result).not.toBe(FilterClassroomRoundEnrollmentStatusEnum.IN_PROGRESS);
      });

      it('when currentDate > startedAt and currentDate < expiredAt and status = IN_PROGRESS, expect status = IN_PROGRESS', () => {
        const mockStatus = ClassroomRoundStatusEnum.IN_PROGRESS;
        const result = service.getClassroomRoundEnrollmentStatus(
          mockStatus,
          mockBetweenRoundDate.startedAt,
          mockBetweenRoundDate.expiredAt,
          true,
        );
        expect(result).toBe(FilterClassroomRoundEnrollmentStatusEnum.IN_PROGRESS);
      });

      it('when currentDate > startedAt and currentDate < expiredAt and status = PRE_ENROLL, expect status = IN_PROGRESS', () => {
        const mockStatus = ClassroomRoundStatusEnum.PRE_ENROLL;
        const result = service.getClassroomRoundEnrollmentStatus(
          mockStatus,
          mockBetweenRoundDate.startedAt,
          mockBetweenRoundDate.expiredAt,
          true,
        );
        expect(result).toBe(FilterClassroomRoundEnrollmentStatusEnum.IN_PROGRESS);
      });

      it('when currentDate > expiredAt and status = IN_PROGRESS, expect status != IN_PROGRESS', () => {
        const mockStatus = ClassroomRoundStatusEnum.IN_PROGRESS;
        const result = service.getClassroomRoundEnrollmentStatus(
          mockStatus,
          mockAfterRoundDate.startedAt,
          mockAfterRoundDate.expiredAt,
          true,
        );
        expect(result).not.toBe(FilterClassroomRoundEnrollmentStatusEnum.IN_PROGRESS);
      });
    });
    describe('Case: PENDING_RESULT', () => {
      it('when currentDate > expiredAt and status = IN_PROGRESS, expect status = PENDING_RESULT', () => {
        const mockStatus = ClassroomRoundStatusEnum.IN_PROGRESS;
        const result = service.getClassroomRoundEnrollmentStatus(
          mockStatus,
          mockAfterRoundDate.startedAt,
          mockAfterRoundDate.expiredAt,
          true,
        );
        expect(result).toBe(FilterClassroomRoundEnrollmentStatusEnum.PENDING_RESULT);
      });
    });
    describe('Case: CANCELED', () => {
      it('when currentDate < startedAt and status = CANCELED, expect status = CANCELED', () => {
        const mockStatus = ClassroomRoundStatusEnum.CANCELED;
        const result = service.getClassroomRoundEnrollmentStatus(
          mockStatus,
          mockBeforeRoundDate.startedAt,
          mockBeforeRoundDate.expiredAt,
          true,
        );
        expect(result).toBe(FilterClassroomRoundEnrollmentStatusEnum.CANCELED);
      });

      it('when currentDate > startedAt and currentDate < expiredAt and status = CANCELED, expect status = CANCELED', () => {
        const mockStatus = ClassroomRoundStatusEnum.CANCELED;
        const result = service.getClassroomRoundEnrollmentStatus(
          mockStatus,
          mockBetweenRoundDate.startedAt,
          mockBetweenRoundDate.expiredAt,
          true,
        );
        expect(result).toBe(FilterClassroomRoundEnrollmentStatusEnum.CANCELED);
      });

      it('when currentDate > expiredAt and status = CANCELED, expect status = CANCELED', () => {
        const mockStatus = ClassroomRoundStatusEnum.CANCELED;
        const result = service.getClassroomRoundEnrollmentStatus(
          mockStatus,
          mockAfterRoundDate.startedAt,
          mockAfterRoundDate.expiredAt,
          true,
        );
        expect(result).toBe(FilterClassroomRoundEnrollmentStatusEnum.CANCELED);
      });
    });
    describe('Case: COMPLETED', () => {
      it('when currentDate < startedAt and status = COMPLETED and isUserEnroll = false, expect status = COMPLETED', () => {
        const mockStatus = ClassroomRoundStatusEnum.COMPLETED;
        const result = service.getClassroomRoundEnrollmentStatus(
          mockStatus,
          mockBeforeRoundDate.startedAt,
          mockBeforeRoundDate.expiredAt,
          false,
        );
        expect(result).toBe(FilterClassroomRoundEnrollmentStatusEnum.COMPLETED);
      });

      it('when currentDate < startedAt and status = COMPLETED, expect status = COMPLETED', () => {
        const mockStatus = ClassroomRoundStatusEnum.COMPLETED;
        const result = service.getClassroomRoundEnrollmentStatus(
          mockStatus,
          mockBeforeRoundDate.startedAt,
          mockBeforeRoundDate.expiredAt,
          true,
        );
        expect(result).toBe(FilterClassroomRoundEnrollmentStatusEnum.COMPLETED);
      });

      it('when currentDate > startedAt and currentDate < expiredAt and status = COMPLETED, expect status = COMPLETED', () => {
        console.log('mockBetweenRoundDate.startedAt :>> ', mockBetweenRoundDate.startedAt);
        const mockStatus = ClassroomRoundStatusEnum.COMPLETED;
        const result = service.getClassroomRoundEnrollmentStatus(
          mockStatus,
          mockBetweenRoundDate.startedAt,
          mockBetweenRoundDate.expiredAt,
          true,
        );
        expect(result).toBe(FilterClassroomRoundEnrollmentStatusEnum.COMPLETED);
      });

      it('when currentDate > expiredAt and status = COMPLETED, expect status = COMPLETED', () => {
        const mockStatus = ClassroomRoundStatusEnum.COMPLETED;
        const result = service.getClassroomRoundEnrollmentStatus(
          mockStatus,
          mockAfterRoundDate.startedAt,
          mockAfterRoundDate.expiredAt,
          true,
        );
        expect(result).toBe(FilterClassroomRoundEnrollmentStatusEnum.COMPLETED);
      });

      it('when currentDate > expiredAt and status = IN_PROGRESS, expect status != COMPLETED', () => {
        const mockStatus = ClassroomRoundStatusEnum.IN_PROGRESS;
        const result = service.getClassroomRoundEnrollmentStatus(
          mockStatus,
          mockAfterRoundDate.startedAt,
          mockAfterRoundDate.expiredAt,
          true,
        );
        expect(result).not.toBe(FilterClassroomRoundEnrollmentStatusEnum.COMPLETED);
      });
    });
  });

  describe('getClassroomRoundStatus function', () => {
    describe('Case: AVAILABLE', () => {
      it('when currentDate < startedAt (1 day before start to roundDate) and status = PRE_ENROLL, expect status = AVAILABLE', () => {
        const mockStatus = ClassroomRoundStatusEnum.PRE_ENROLL;
        const result = service.getClassroomRoundStatus(mockStatus, mockOneDayBeforeStartRoundDate.startedAt);
        expect(result).toBe(FilterClassroomRoundStatusEnum.AVAILABLE);
      });

      it('when currentDate < startedAt (30 minute before start to roundDate) and status = PRE_ENROLL, expect status = EXPIRED', () => {
        const mockStatus = ClassroomRoundStatusEnum.PRE_ENROLL;
        const result = service.getClassroomRoundStatus(mockStatus, mock30MinuteBeforeStartRoundDate.startedAt);
        expect(result).toBe(FilterClassroomRoundStatusEnum.EXPIRED);
      });

      it('when currentDate < startedAt and status = CANCELED, expect status != AVAILABLE', () => {
        const mockStatus = ClassroomRoundStatusEnum.CANCELED;
        const result = service.getClassroomRoundStatus(mockStatus, mockBeforeRoundDate.startedAt);
        expect(result).not.toBe(FilterClassroomRoundStatusEnum.AVAILABLE);
      });

      it('when currentDate > startedAt and status = COMPLETED, expect status != AVAILABLE', () => {
        const mockStatus = ClassroomRoundStatusEnum.COMPLETED;
        const result = service.getClassroomRoundStatus(mockStatus, mockBetweenRoundDate.startedAt);
        expect(result).not.toBe(FilterClassroomRoundStatusEnum.AVAILABLE);
      });

      it('when currentDate > startedAt and status = IN_PROGRESS, expect status != AVAILABLE', () => {
        const mockStatus = ClassroomRoundStatusEnum.IN_PROGRESS;
        const result = service.getClassroomRoundStatus(mockStatus, mockAfterRoundDate.startedAt);
        expect(result).not.toBe(FilterClassroomRoundStatusEnum.AVAILABLE);
      });
    });
    describe('Case: EXPIRED', () => {
      it('when currentDate < startedAt and status = CANCELED, expect status != EXPIRED', () => {
        const mockStatus = ClassroomRoundStatusEnum.CANCELED;
        const result = service.getClassroomRoundStatus(mockStatus, mockBeforeRoundDate.startedAt);
        expect(result).not.toBe(FilterClassroomRoundStatusEnum.EXPIRED);
      });

      it('when currentDate > startedAt and status = IN_PROGRESS, expect status = EXPIRED', () => {
        const mockStatus = ClassroomRoundStatusEnum.IN_PROGRESS;
        const result = service.getClassroomRoundStatus(mockStatus, mockAfterRoundDate.startedAt);
        expect(result).toBe(FilterClassroomRoundStatusEnum.EXPIRED);
      });
      it('when currentDate > startedAt and status = COMPLETED, expect status = EXPIRED', () => {
        const mockStatus = ClassroomRoundStatusEnum.COMPLETED;
        const result = service.getClassroomRoundStatus(mockStatus, mockAfterRoundDate.startedAt);
        expect(result).toBe(FilterClassroomRoundStatusEnum.EXPIRED);
      });

      it('when currentDate > startedAt and status = CANCELED, expect status != EXPIRED', () => {
        const mockStatus = ClassroomRoundStatusEnum.CANCELED;
        const result = service.getClassroomRoundStatus(mockStatus, mockAfterRoundDate.startedAt);
        expect(result).not.toBe(FilterClassroomRoundStatusEnum.EXPIRED);
      });
    });
    describe('Case: CANCELED', () => {
      it('when currentDate < startedAt and status = CANCELED, expect status = CANCELED', () => {
        const mockStatus = ClassroomRoundStatusEnum.CANCELED;
        const result = service.getClassroomRoundStatus(mockStatus, mockBeforeRoundDate.startedAt);
        expect(result).toBe(FilterClassroomRoundStatusEnum.CANCELED);
      });
      it('when currentDate > startedAt and status = CANCELED, expect status = CANCELED', () => {
        const mockStatus = ClassroomRoundStatusEnum.CANCELED;
        const result = service.getClassroomRoundStatus(mockStatus, mockAfterRoundDate.startedAt);
        expect(result).toBe(FilterClassroomRoundStatusEnum.CANCELED);
      });
    });
  });

  describe('getClassroomLocationEnrollmentStatus function', () => {
    describe('Case: NOT_REGISTERED', () => {
      it('when status = null, expect status = NOT_REGISTERED', () => {
        const mockStatus = null;
        const result = service.getClassroomLocationEnrollmentStatus(
          mockStatus,
          mockBeforeRoundDate.startedAt,
          mockBeforeRoundDate.expiredAt,
        );
        expect(result).toBe(FilterClassroomLocationEnrollmentStatusEnum.NOT_REGISTERED);
      });
    });
    describe('Case: PRE_ENROLL', () => {
      it('when currentDate < startedAt and status = PRE_ENROLL, expect status = PRE_ENROLL', () => {
        const mockStatus = ClassroomLocationEnrollmentStatusEnum.PRE_ENROLL;
        const result = service.getClassroomLocationEnrollmentStatus(
          mockStatus,
          mockBeforeRoundDate.startedAt,
          mockBeforeRoundDate.expiredAt,
        );
        expect(result).toBe(FilterClassroomLocationEnrollmentStatusEnum.PRE_ENROLL);
      });

      it('when currentDate > startedAt and status = PRE_ENROLL, expect status != PRE_ENROLL', () => {
        const mockStatus = ClassroomLocationEnrollmentStatusEnum.PRE_ENROLL;
        const result = service.getClassroomLocationEnrollmentStatus(
          mockStatus,
          mockBetweenRoundDate.startedAt,
          mockBetweenRoundDate.expiredAt,
        );
        expect(result).not.toBe(FilterClassroomLocationEnrollmentStatusEnum.PRE_ENROLL);
      });
    });
    describe('Case: WAITING_PRE_ENROLL', () => {
      it('when after 00.00 AM of roundDate and currentDate < startedAt and status = WAITING_PRE_ENROLL, expect status = CANCELED', () => {
        const mockStatus = ClassroomLocationEnrollmentStatusEnum.WAITING_PRE_ENROLL;
        const result = service.getClassroomLocationEnrollmentStatus(
          mockStatus,
          mockBeforeRoundDate.startedAt,
          mockBeforeRoundDate.expiredAt,
        );

        const startOfDayCurrentDate = date(currentDate).startOf('day').toDate();
        const startOfDayRoundDate = date(mockBeforeRoundDate.startedAt).startOf('day').toDate();
        if (startOfDayCurrentDate.getTime() === startOfDayRoundDate.getTime()) {
          expect(result).toBe(FilterClassroomLocationEnrollmentStatusEnum.CANCELED);
        } else {
          expect(result).toBe(FilterClassroomLocationEnrollmentStatusEnum.WAITING_PRE_ENROLL);
        }
      });

      it('when before 00.00 AM of roundDate and currentDate < startedAt and status = WAITING_PRE_ENROLL, expect status = WAITING_PRE_ENROLL', () => {
        const mockStatus = ClassroomLocationEnrollmentStatusEnum.WAITING_PRE_ENROLL;
        const result = service.getClassroomLocationEnrollmentStatus(
          mockStatus,
          mockOneDayBeforeStartRoundDate.startedAt,
          mockOneDayBeforeStartRoundDate.expiredAt,
        );
        expect(result).toBe(FilterClassroomLocationEnrollmentStatusEnum.WAITING_PRE_ENROLL);
      });

      it('when currentDate > startedAt and status = WAITING_PRE_ENROLL, expect status = CANCELED', () => {
        const mockStatus = ClassroomLocationEnrollmentStatusEnum.WAITING_PRE_ENROLL;
        const result = service.getClassroomLocationEnrollmentStatus(
          mockStatus,
          mockBetweenRoundDate.startedAt,
          mockBetweenRoundDate.expiredAt,
        );
        expect(result).toBe(FilterClassroomLocationEnrollmentStatusEnum.CANCELED);
      });
    });
    describe('Case: IN_PROGRESS', () => {
      it('when currentDate < startedAt and status = PRE_ENROLL, expect status != IN_PROGRESS', () => {
        const mockStatus = ClassroomLocationEnrollmentStatusEnum.PRE_ENROLL;
        const result = service.getClassroomLocationEnrollmentStatus(
          mockStatus,
          mockBeforeRoundDate.startedAt,
          mockBeforeRoundDate.expiredAt,
        );
        expect(result).not.toBe(FilterClassroomLocationEnrollmentStatusEnum.IN_PROGRESS);
      });

      it('when currentDate < startedAt and status = IN_PROGRESS, expect status = PRE_ENROLL', () => {
        const mockStatus = ClassroomLocationEnrollmentStatusEnum.IN_PROGRESS;
        const result = service.getClassroomLocationEnrollmentStatus(
          mockStatus,
          mockBeforeRoundDate.startedAt,
          mockBeforeRoundDate.expiredAt,
        );
        expect(result).toBe(FilterClassroomLocationEnrollmentStatusEnum.PRE_ENROLL);
      });

      it('when currentDate > startedAt and currentDate < expiredAt and status = IN_PROGRESS, expect status = IN_PROGRESS', () => {
        const mockStatus = ClassroomLocationEnrollmentStatusEnum.IN_PROGRESS;
        const result = service.getClassroomLocationEnrollmentStatus(
          mockStatus,
          mockBetweenRoundDate.startedAt,
          mockBetweenRoundDate.expiredAt,
        );
        expect(result).toBe(FilterClassroomLocationEnrollmentStatusEnum.IN_PROGRESS);
      });

      it('when currentDate > expiredAt and status = IN_PROGRESS, expect status != IN_PROGRESS', () => {
        const mockStatus = ClassroomLocationEnrollmentStatusEnum.IN_PROGRESS;
        const result = service.getClassroomLocationEnrollmentStatus(
          mockStatus,
          mockAfterRoundDate.startedAt,
          mockAfterRoundDate.expiredAt,
        );
        expect(result).not.toBe(FilterClassroomLocationEnrollmentStatusEnum.IN_PROGRESS);
      });
    });
    describe('Case: PENDING_RESULT', () => {
      it('when currentDate < startedAt and status = IN_PROGRESS, expect status != PENDING_RESULT', () => {
        const mockStatus = ClassroomLocationEnrollmentStatusEnum.IN_PROGRESS;
        const result = service.getClassroomLocationEnrollmentStatus(
          mockStatus,
          mockBeforeRoundDate.startedAt,
          mockBeforeRoundDate.expiredAt,
        );
        expect(result).not.toBe(FilterClassroomLocationEnrollmentStatusEnum.PENDING_RESULT);
      });

      it('when currentDate > startedAt and currentDate < expiredAt and status = IN_PROGRESS, expect status != PENDING_RESULT', () => {
        const mockStatus = ClassroomLocationEnrollmentStatusEnum.IN_PROGRESS;
        const result = service.getClassroomLocationEnrollmentStatus(
          mockStatus,
          mockBetweenRoundDate.startedAt,
          mockBetweenRoundDate.expiredAt,
        );
        expect(result).not.toBe(FilterClassroomLocationEnrollmentStatusEnum.PENDING_RESULT);
      });

      it('when currentDate > expiredAt and status = IN_PROGRESS, expect status = PENDING_RESULT', () => {
        const mockStatus = ClassroomLocationEnrollmentStatusEnum.IN_PROGRESS;
        const result = service.getClassroomLocationEnrollmentStatus(
          mockStatus,
          mockAfterRoundDate.startedAt,
          mockAfterRoundDate.expiredAt,
        );
        expect(result).toBe(FilterClassroomLocationEnrollmentStatusEnum.PENDING_RESULT);
      });
    });
    describe('Case: PASSED', () => {
      it('when currentDate > expiredAt and status = PASSED, expect status = PASSED', () => {
        const mockStatus = ClassroomLocationEnrollmentStatusEnum.PASSED;
        const result = service.getClassroomLocationEnrollmentStatus(
          mockStatus,
          mockAfterRoundDate.startedAt,
          mockAfterRoundDate.expiredAt,
        );
        expect(result).toBe(FilterClassroomLocationEnrollmentStatusEnum.PASSED);
      });

      it('when currentDate > expiredAt and status = IN_PROGRESS, expect status != PASSED', () => {
        const mockStatus = ClassroomLocationEnrollmentStatusEnum.IN_PROGRESS;
        const result = service.getClassroomLocationEnrollmentStatus(
          mockStatus,
          mockAfterRoundDate.startedAt,
          mockAfterRoundDate.expiredAt,
        );
        expect(result).not.toBe(FilterClassroomLocationEnrollmentStatusEnum.PASSED);
      });
    });
    describe('Case: NOT_PASS', () => {
      it('when currentDate > expiredAt and status = NOT_PASS, expect status = NOT_PASS', () => {
        const mockStatus = ClassroomLocationEnrollmentStatusEnum.NOT_PASS;
        const result = service.getClassroomLocationEnrollmentStatus(
          mockStatus,
          mockAfterRoundDate.startedAt,
          mockAfterRoundDate.expiredAt,
        );
        expect(result).toBe(FilterClassroomLocationEnrollmentStatusEnum.NOT_PASS);
      });

      it('when currentDate > expiredAt and status = IN_PROGRESS, expect status != NOT_PASS', () => {
        const mockStatus = ClassroomLocationEnrollmentStatusEnum.IN_PROGRESS;
        const result = service.getClassroomLocationEnrollmentStatus(
          mockStatus,
          mockAfterRoundDate.startedAt,
          mockAfterRoundDate.expiredAt,
        );
        expect(result).not.toBe(FilterClassroomLocationEnrollmentStatusEnum.NOT_PASS);
      });
    });
    describe('Case: CANCELED', () => {
      it('when currentDate < startedAt and status = CANCELED, expect status = CANCELED', () => {
        const mockStatus = ClassroomLocationEnrollmentStatusEnum.CANCELED;
        const result = service.getClassroomLocationEnrollmentStatus(
          mockStatus,
          mockBeforeRoundDate.startedAt,
          mockBeforeRoundDate.expiredAt,
        );
        expect(result).toBe(FilterClassroomLocationEnrollmentStatusEnum.CANCELED);
      });

      it('when currentDate > startedAt and currentDate < expiredAt and status = CANCELED, expect status = CANCELED', () => {
        const mockStatus = ClassroomLocationEnrollmentStatusEnum.CANCELED;
        const result = service.getClassroomLocationEnrollmentStatus(
          mockStatus,
          mockBetweenRoundDate.startedAt,
          mockBetweenRoundDate.expiredAt,
        );
        expect(result).toBe(FilterClassroomLocationEnrollmentStatusEnum.CANCELED);
      });

      it('when currentDate > expiredAt and status = CANCELED, expect status = CANCELED', () => {
        const mockStatus = ClassroomLocationEnrollmentStatusEnum.CANCELED;
        const result = service.getClassroomLocationEnrollmentStatus(
          mockStatus,
          mockAfterRoundDate.startedAt,
          mockAfterRoundDate.expiredAt,
        );
        expect(result).toBe(FilterClassroomLocationEnrollmentStatusEnum.CANCELED);
      });
    });
  });
});
