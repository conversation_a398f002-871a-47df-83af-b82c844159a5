import { GenericID, Nullable, ObjectValue } from '@iso/constants/commonTypes';
import {
  ApplicantTypeEnum,
  ContentProviderTypeEnum,
  ContentTypeEnum,
  CourseAccessTypeEnum,
  CourseEnrollTypeEnum,
  CourseObjectiveTypeEnum,
  LicenseRenewalEnum,
  LicenseTypeEnum,
  RegulatorEnum,
} from '@iso/lms/enums/course.enum';
import { CourseVersionStatusEnum } from '@iso/lms/enums/courseVersion.enum';
import { MaterialMediaTypeEnum } from '@iso/lms/enums/materialMedia.enum';
import { QuizTestTypeEnum } from '@iso/lms/enums/quiz.enum';
import { SurveyTypeEnum } from '@iso/lms/enums/survey.enum';
import { Course } from '@iso/lms/models/course.model';
import { CourseItem } from '@iso/lms/models/courseItem.model';
import { CourseVersion } from '@iso/lms/models/courseVersion.model';
import { CourseVersionCertificate } from '@iso/lms/models/courseVersionCertificate.model';
import { Instructor } from '@iso/lms/models/instructor.model';
import { CourseParams } from '@iso/lms/types/course.type';
import { AttachmentMaterialMediaParams } from '@iso/lms/types/courseItem.type';
import { CourseItemProgressParams } from '@iso/lms/types/courseItemProgress.type';
import { CourseVersionCompletionCriteriaParams, CourseVersionParams } from '@iso/lms/types/courseVersion.type';
import {
  QuestionSetParams,
  QuizLimitTimeDurationParams,
  QuizRetestParams,
  QuizShowAnswerParams,
} from '@iso/lms/types/quiz.type';
import { UserRegulatorProfileParams } from '@iso/lms/types/user.type';
import { BaseEntityParams } from '@shared/lms/core/constant/types';

import { PaginateParam } from '@core/commons/type/PaginateTypes';
import { DefaultFilterParams, OrderByEnum } from '@entities/constants/core';
import { ICourseItemProgress } from '@entities/constants/courseItemProgress';
import { EditCourseVersionParams, OutputUpdateCourseVersionParams } from '@entities/constants/courseVersion';
import {
  CourseObjectiveTypeFilterEnum,
  EnrollmentClassroomCurriculumParams,
  RegulatorLicenseParams,
  StatusCheckEnrollableEnum,
} from '@entities/constants/enrollment';
import { InstructorParams } from '@entities/constants/instructor';
import { ImagePathParams, MediaParams, ThumbnailMediaIdParams } from '@entities/constants/media';
import { MediaTranscodeParams } from '@entities/constants/mediaTranscode';
import { QuestionParams } from '@entities/constants/quiz';
import { UserGroupParams } from '@entities/constants/userGroup';
import { CourseItemCriteriaConfig } from '@entities/models/courseItemCriteriaConfigModel';
import { Part } from '@entities/models/partModel';
import { GetCourseInstructorListUseCaseDto } from '@usecase/course/dto/getCourseInstructorList';

export type PatchCourseParams = {
  regulatorInfo: CourseRegulatorParams;
  rpcEnabled?: boolean;
  courseCode?: string;
  isCertificateEnabled?: boolean;
  isMultipleCertificate?: boolean;
  isSentCertificateEmailOnExpiredDate?: boolean;
  expiryDay?: number;
  credit?: number;
};

export type CourseRegulatorParams = {
  regulator: RegulatorEnum;
  trainingCenter: string;
  licenseRenewal: LicenseRenewalEnum;
  applicantType: ApplicantTypeEnum | '';
  licenseType: LicenseTypeEnum[];
  isDeduct: boolean;
  isRequireDeductDocument: boolean;
};

export type CourseSubjectParams = {
  courseName: string;
  courseShortName: string;
  courseCode: string;
  diplomaName: string;
  subjectName: string;
  subjectCode: string;
  pillarName: string;
  trainingType: string;
  trainingDuration?: string;
};

export interface LearningSessionDigestRequestDTO {
  email: string;
}

export type CoursePreviewVideoParams = {
  url: string;
  chapterId?: Nullable<number>;
  mediaId?: Nullable<GenericID>;
  name: string;
};

export type CourseItemParams = {
  id: GenericID;
  partId?: GenericID;
  materialMediaId?: GenericID;

  name: string;
  type: MaterialMediaTypeEnum;
  position: number;
  isEnabled: boolean;
  duration?: number;
  description?: string;
  attachments?: AttachmentMaterialMediaParams[];
  learningProgress?: CourseItemProgressParams;
  mediaTranscode?: MediaTranscodeParams;

  // video
  chapterId?: number; // use for verify video token on B2C Content
  videoUrl?: string;
  isPreviewVideo?: boolean;
  transcodeStatus?: string;
  mediaId?: GenericID;
  videoId?: GenericID;

  // article
  articleId?: GenericID;

  // survey
  surveyId?: GenericID;
  contentHtml?: string;
  surveyType?: SurveyTypeEnum;
  formSchema?: Record<string, any>;

  // quiz
  quizId?: GenericID;
  testType?: QuizTestTypeEnum;
  retest?: QuizRetestParams;
  limitTimeDuration?: QuizLimitTimeDurationParams;
  showAnswer?: QuizShowAnswerParams;
  questionSets?: QuestionSetParams[];
  totalScore?: number;
  publishedAt?: Date;

  // classroom
  classroomId?: GenericID;
};

export type CourseNameParams = {
  id: string;
  name: string;
};

export const DeductAttachmentType = {
  agent: {
    duration: 14,
    text: 'ตัวแทนประกันภัย',
  },
  broker: {
    duration: 25,
    text: 'นายหน้าประกันภัย',
  },
} as const;

export type DeductAttachmentType = keyof typeof DeductAttachmentType;

export enum TrainingCenterEnum {
  TII = 'TII',
  TIPA = 'TIPA',
  SKILLLANE = 'SKILLLANE',
}

export const LicenseRenewalLMSToOICMapper: Record<LicenseRenewalEnum, number | string> = {
  [LicenseRenewalEnum.NONE]: 99,
  [LicenseRenewalEnum.NEW]: 0,
  [LicenseRenewalEnum.RENEW1]: 1,
  [LicenseRenewalEnum.RENEW2]: 2,
  [LicenseRenewalEnum.RENEW3]: 3,
  [LicenseRenewalEnum.RENEW4]: 4,
  [LicenseRenewalEnum.UL]: 'UL',
  [LicenseRenewalEnum.UK]: 'UK',
} as const;

export const LicenseRenewalOICToLMSMapper: Record<number, string> = {
  99: 'NONE',
  0: 'OIC0',
  1: 'OIC1',
  2: 'OIC2',
  3: 'OIC3',
  4: 'OIC4',
} as const;

export type CourseReportHistoryParams = {
  id: number;
  name: string;
  courseCode?: string;
  regulatorInfo: CourseRegulatorParams;
};

export type CourseWithVersionParams = {
  id?: GenericID;
  organizationId: GenericID;
  code?: string;
  url?: string;
  contentType: ContentTypeEnum;
  contentProviderType: ContentProviderTypeEnum;
  objectiveType?: Nullable<CourseObjectiveTypeEnum>;
  regulatorInfo?: Nullable<CourseRegulatorParams>;
  description?: string;
  thumbnailUrl?: Nullable<string | ImagePathParams>;
  thumbnailId?: Nullable<string>;
  thumbnailMediaId?: Nullable<ThumbnailMediaIdParams>;
  videoPreviewImage?: Nullable<string>;
  disableIntro?: boolean;
  isEnabled?: boolean;
  isSelfEnrollEnabled?: boolean;
  isReEnrollEnabled?: boolean;
  reEnrollDay?: Nullable<number>;
  isReEnrollExpireEnabled?: boolean;
  reEnrollExpireDay?: Nullable<number>;
  accessType?: CourseAccessTypeEnum;
  enrollType?: CourseEnrollTypeEnum;
  subjects?: CourseSubjectParams[];
  courseVersionId: GenericID;
  version: number;
  status: CourseVersionStatusEnum;
  name: string;
  expiryDay?: Nullable<number>;
  instructorIds?: Array<string>;
  instructors?: Nullable<Instructor[]>;
  totalCourseItems?: number;
  totalVideos?: number;
  totalQuizzes?: number;
  totalArticles?: number;
  totalSurveys?: number;
  totalDurationSec?: number;
  totalDurationArticleSec?: number;
  totalClassrooms?: number;
  isRPCEnabled?: boolean;
  isOCREnabled?: boolean;
  isLivenessEnabled?: boolean;
  isCountdownArticle?: boolean;
  isCertificateEnabled?: boolean;
  isMultipleCertificate?: boolean;
  isSentCertificateEmailOnExpiredDate?: boolean;
  isSeekEnabled?: boolean;
  isVideoSpeedEnabled?: boolean;
  isPlayVideoBackground?: boolean;
  isLearnableFullscreen?: boolean;
  isIdentityVerificationEnabled?: boolean;
  isAttentionCheckEnabled?: boolean;
  isAutoApproveEnabled?: boolean;
  publishedByUserId?: Nullable<GenericID>;
  publishedAt?: Nullable<Date>;
  createdAt?: Date;
  updatedAt?: Date;
};

export type CreateCourseParams = Partial<CourseParams> & {
  organizationId: GenericID;
  contentType: ContentTypeEnum;
  name: string;
  code: string;
  contentProviderType: ContentProviderTypeEnum;
  objectiveType: CourseObjectiveTypeEnum;
  regulator: RegulatorEnum;
};

export type UpdateCourseParams = Omit<
  Partial<CourseParams>,
  'id' | 'organizationId' | 'contentType' | 'contentProviderType' | 'courseVersion' | 'createdAt'
>;

export type FindCourseByParams = Partial<CourseParams>;

export type UpdateCourseDetailParams = UpdateCourseParams & EditCourseVersionParams;

/**
 * @deprecated use PartParams from shared
 */
export type PartParams = BaseEntityParams & {
  courseVersionId: GenericID;
  courseName?: string;
  name: string;
  position: number;
  description?: string;
  courseItems: CourseItemParams[];
};

export type LearningProgressResponseParams = {
  id?: GenericID;
  courseId?: GenericID;
  name: string;
  position: number;
  description?: string;
  courseItems: (CourseItemParams & { learningProgress: ICourseItemProgress })[];
  createdAt?: Date;
  updatedAt?: Date;
};

export type Chapter = {
  id: number;
  itemId: number;
  position: number;
  type: string;
  name: string;
  chapterNumber: number;
  duration: number;
  transcodeStatus: string;
  videoVersion: number;
  preview: boolean;
  videoPreviewUrl: string;
};

export type GetCoursePayload = {
  organizationId: GenericID;
  id: string;
  version?: number;
};

export type GetCourseListPayload = {
  organizationId: GenericID;
  filters: Array<{ id: string; data: Array<{ field: string; value: unknown }> }>;
  pagination: PaginateParam;
};

export enum CourseIncludedTypeEnum {
  ARTICLE = 'article',
  ATTACHMENT = 'attachment',
  CERTIFICATE = 'certificate',
  QUIZ = 'quiz',
}

export enum CourseErrorEnum {
  NO_COURSE = 'NO_COURSE',
  NO_ROUND = 'NO_ROUND',
  NO_EXPIRYDAY = 'NO_EXPIRYDAY',
  NO_OBJECTIVETYPE = 'NO_OBJECTIVETYPE',
  NO_REGULATORINFO = 'NO_REGULATORINFO',
  NO_COURSE_ITEM = 'NO_COURSE_ITEM',
  INCOMPLETE_REGULATOR_INFO_OIC = 'INCOMPLETE_REGULATOR_INFO_OIC',
  INCOMPLETE_REGULATOR_INFO_TSI = 'INCOMPLETE_REGULATOR_INFO_TSI',
  INCOMPLETE_REGULATOR_INFO_TFAC = 'INCOMPLETE_REGULATOR_INFO_TFAC',
  INCOMPLETE_REGULATOR_INFO_NONE = 'INCOMPLETE_REGULATOR_INFO_NONE',
  INCOMPLETE_REPORT_SETTING_TSI = 'INCOMPLETE_REPORT_SETTING_TSI',
  INCOMPLETE_CRITERIA_SETTING = 'INCOMPLETE_CRITERIA_SETTING',
}

export type FilterCourseCatalogParams = DefaultFilterParams & {
  organizationId: GenericID;
  guid: GenericID;
  categoryId: GenericID;
  filterCategoryIds: GenericID[];
  orderBy?: OrderByEnum;
  isFilterQuiz?: boolean;
  isFilterArticle?: boolean;
  isFilterCertificate?: boolean;
  isFilterAssignCourse?: boolean;
  objectiveTypes?: CourseObjectiveTypeFilterEnum[];
  keyword?: string;
};

export type UpdateCourseCurriculumParams = {
  courseVersionId: GenericID;
  parts: {
    id: string;
    position: number;
  }[];
  courseItems: {
    partId: string;
    id: string;
    position: number;
  }[];
};

export type OutputAllCourseListParams = {
  id: string;
  name: string;
  code?: string;
  isEnabled?: boolean;
  status?: CourseVersionStatusEnum;
};

export type CheckDuplicatedCodeParams = {
  organizationId: GenericID;
  code: string;
};

export type OutputCheckDuplicatedCodeParams = {
  code: string;
  isDuplicated: boolean;
};

export type GetCourseDetailByUrlParams = {
  courseURL: string;
  instructorId?: GenericID;
  version?: number;
  organizationId: GenericID;
};

export type GetCourseAccessByIdParams = Omit<GetCourseDetailByUrlParams, 'instructorId' | 'courseURL'> & {
  id: GenericID;
  userId: string;
};

export type OutputCourseDetailByUrlParams = {
  id: GenericID;
  contentId: GenericID;
  organizationId: GenericID;
  contentType: ContentTypeEnum;
  url: string;
  thumbnailUrl: ImagePathParams;
  description: string;
  code: string;
  contentProviderType: ContentProviderTypeEnum;
  objectiveType: Nullable<CourseObjectiveTypeEnum>;
  regulatorInfo: Nullable<CourseRegulatorParams>;
  disableIntro: boolean;
  accessType: CourseAccessTypeEnum;
  subjects: CourseSubjectParams[];
  enrollType: CourseEnrollTypeEnum;
  isSelfEnrollEnabled: boolean;
  isReEnrollEnabled: boolean;
  reEnrollDay: number;
  isReEnrollExpireEnabled: boolean;
  reEnrollExpireDay: number;

  // course version config
  name: string;
  status: CourseVersionStatusEnum;
  isIdentityVerificationEnabled: boolean;
  isOCREnabled: boolean;
  isLivenessEnabled: boolean;
  isRPCEnabled: boolean;
  isAttentionCheckEnabled: boolean;
  isSeekEnabled: boolean;
  isCountdownArticle: boolean;
  isPlayVideoBackground: boolean;
  isLearnableFullscreen: boolean;
  isVideoSpeedEnabled: boolean;
  isCertificateEnabled: boolean;
  isMultipleCertificate: boolean;
  videoPreviewImage: string;
  totalArticles: number;
  totalVideos: number;
  totalQuizzes: number;
  totalSurveys: number;
  totalDurationArticleSec: number;
  totalDurationSec: number;
  totalClassrooms: number;
  expiryDay: Nullable<number>;
  completionCriteria: CourseVersionCompletionCriteriaParams;

  instructors: InstructorParams[];
  isShowDuration: boolean;
  parts: PartParams[];
  previewVideo: Nullable<CoursePreviewVideoParams>;
};

export type GetCourseQuizConfigParams = {
  courseVersionId: GenericID;
  courseItemId: GenericID;
  name: string;
  isEnabled: boolean;
  maxScore: number;
  passScore: number;
};

export type GetCourseClassroomConfigParams = {
  courseVersionId: GenericID;
  courseItemId: GenericID;
  name: string;
  isEnabled: boolean;
  maxScoreHomework: number;
  passScoreHomework: number;
  maxAttendance: number;
  passAttendance: number;
};

export type GetCourseItemCriteriaConfigParams = {
  quizzes: GetCourseQuizConfigParams[];
  classrooms: GetCourseClassroomConfigParams[];
};

export enum CourseDomain {
  COURSE = 'COURSE',
  PRODUCT_SKU = 'PRODUCT_SKU',
}

export type GetPartListParams = {
  parts: PartParams[];
  isSeekEnabled: boolean;
  courseName: string;
};

export type GetCourseInstructorDropdownPayloadParams = {
  organizationId: GenericID;
  courseId: GenericID;
  version: number;
};

export type GetCourseInstructorDropdownOutputParams = GetCourseInstructorListUseCaseDto[];

export type GetRoundListByCourseIdParams = {
  courseId: GenericID;
  version: number;
  organizationId: GenericID;
  pagination: PaginateParam;
};

export type GetCourseInstructorListPayloadParams = {
  organizationId: GenericID;
  courseId: GenericID;
  version: number;
};

export type GetCourseInstructorListOutputParams = GetCourseInstructorListUseCaseDto[];

export type UpdateCourseInstructorPayloadParams = {
  organizationId: GenericID;
  courseId: GenericID;
  version: number;
  instructorIds: string[];
};

export type OutputGetCourseParams = CourseParams & {
  courseVersion: CourseVersionParams;
  userGroups: UserGroupParams;
};

export type UpdateCourseAccessParams = {
  id: string;
  organizationId: GenericID;
  accessType: CourseAccessTypeEnum;
  userGroupIds: string[];
  version: number;
};

export type OutputUpdateCourseAccessParams = { userGroups: UserGroupParams[]; name: string };

export type DeleteCourseParams = {
  id: string;
  organizationId: GenericID;
};

export type OutputGetCourseAccessParams = {
  isAccess: boolean;
};

export type UpdateCourseEnrollTypeParams = {
  id: string;
  enrollType: CourseEnrollTypeEnum;
  version: number;
};

export type UpdateCourseGeneralParams = { by: FindCourseByParams; payload: UpdateCourseDetailParams };

export type OutputCreatePartParams = OutputUpdateCourseVersionParams & {
  parts?: PartParams[];
  newPart: PartParams;
};
export type OutputUpdatePartParams = OutputUpdateCourseVersionParams & { parts?: PartParams[]; updatePart: PartParams };
export type OutputDeletePartParams = OutputUpdateCourseVersionParams & {
  parts?: PartParams[];
  deletePart: Pick<PartParams, 'id' | 'name'>;
};

export type OutputUpdateCourseParams = OutputUpdateCourseVersionParams & {
  course: Omit<CourseParams, 'thumbnailUrl'> & {
    thumbnailUrl: ImagePathParams;
  };
};

export type OldToNewIdMappingParams = { mapKey: string; idMap: Record<string, string> };

export enum CourseRelatedCollectionsEnum {
  CERTIFICATE = 'CERTIFICATE',
  PART = 'PART',
  COURSE_ITEM = 'COURSE_ITEM',
  COURSE_ITEM_CRITERIA_CONFIG = 'COURSE_ITEM_CRITERIA_CONFIG',
}

export enum CourseRelatedActionModeEnum {
  CREATE = 'CREATE',
  UPDATE = 'UPDATE',
  DELETE = 'DELETE',
}

export type CourseRelatedModel = CourseVersionCertificate | Part | CourseItem | CourseItemCriteriaConfig;

export type CloneCourseRelateParams = {
  data: CourseRelatedModel[];
  oldIds: GenericID[];
  newIds: GenericID[];
};

export type OutputPartsOfCourseParams = {
  parts: PartParams[];
};

export type OutputUpdateCourseCurriculumParams = OutputUpdateCourseVersionParams & {
  parts?: PartParams[];
};

export type OutputFindCourseVersionClassroomsParams = Course & {
  courseVersions: Array<
    CourseVersion & {
      parts: EnrollmentClassroomCurriculumParams[];
    }
  >;
  media: MediaParams;
};

export type OpenApiCourseParams = {
  id: GenericID;
  name: string;
  code: string;
  objectiveType: CourseObjectiveTypeEnum;
  instructors: Array<{
    firstname: string;
    lastname: string;
  }>;
  regulatorInfo: {
    regulator: RegulatorEnum;
    licenseRenewal: LicenseRenewalEnum;
    applicantType: ApplicantTypeEnum;
    licenseType: LicenseTypeEnum[];
  };
  thumbnailUrl: ImagePathParams;
  publishedAt: Date;
  expiryDay?: Nullable<number>;
};

export type OpenApiGetCourseListParams = {
  organizationId: GenericID;
  citizenId?: string;
  objectiveTypes?: string[];
  trainingCenter?: string;
};

export type OutputOpenApiGetCourseListParams = {
  citizenId: Nullable<string>;
  courses: OpenApiCourseParams[];
};

export type OpenApiGetCourseRoundParams = {
  organizationId: GenericID;
  citizenId: string;
  code: string;
};

export type CourseRoundParams = {
  id: GenericID;
  roundDate: Date;
  expireDate: Date;
  firstRegistrationDate: Date;
  lastRegistrationDate: Date;
};

export type OutputOpenApiGetCourseRoundParams = {
  courseCode: string;
  rounds: Array<CourseRoundParams>;
};

export type OpenApiGetCourseDetailParams = {
  organizationId: GenericID;
  code: string;
};

export type OpenApiCourseItemParams = {
  id: GenericID;
  name: string;
  attachments?: AttachmentMaterialMediaParams[];
  position: number;
  type: MaterialMediaTypeEnum;
  isEnabled: boolean;
  duration?: number;
  totalAttachment?: number;
  totalScore?: number;
  questions?: QuestionParams[];
  testType?: QuizTestTypeEnum;
  surveyType?: string;
};

export type OpenApiPartParams = {
  id: GenericID;
  name: string;
  courseItems: Array<OpenApiCourseItemParams>;
};

export type OutputOpenApiGetCourseDetailParams = {
  id: GenericID;
  name: string;
  code: string;
  description: string;
  expiryDay?: Nullable<number>;
  thumbnailUrl: ImagePathParams;
  instructors: Array<{
    avatar: string;
    firstname: string;
    lastname: string;
    highlightDescription?: string;
    biology?: string;
  }>;
  regulatorInfo?: {
    regulator?: RegulatorEnum;
    licenseRenewal?: LicenseRenewalEnum;
    applicantType?: ApplicantTypeEnum | '';
    licenseType?: Array<LicenseTypeEnum>;
    isDeduct: boolean;
  };
  parts: OpenApiPartParams[];
  totalCourseItems: number;
  totalVideos: number;
  totalQuizzes: number;
  totalArticles: number;
  totalSurveys: number;
  totalDurationSec: number;
  totalFileDownload: number;
};

export type OpenApiGetCourseEnrollmentEligibilityParams = {
  organizationId: GenericID;
  courseCode: string;
  citizenId: string;
  roundId?: GenericID;
};

export type OutputOpenApiGetCourseEnrollmentEligibilityParams = {
  courseCode: string;
  status: StatusCheckEnrollableEnum;
  citizenId: string;
  profile?: UserRegulatorProfileParams;
  activeLicenses?: RegulatorLicenseParams[];
};

export type OutputTransformCourseDataParams = {
  courseVersions: ObjectValue<CourseVersionParams>[];
};

export type CourseWithCourseVersionParams = CourseParams & {
  courseVersion: CourseVersionParams;
};

export enum FilterContentProviderTypeEnum {
  SELF = 'SELF',
  PLAN_PACKAGE = 'PLAN_PACKAGE',
  MARKETPLACE = 'MARKETPLACE',
}
