const {
  ColumnSettingAlignmentEnum,
  ColumnSettingComponentTypeEnum,
  ColumnSettingDataTypeEnum,
  ColumnSettingDropdownValueTypeEnum,
  ColumnSettingFieldTypeEnum,
  ColumnSettingFilterTypeEnum,
  ColumnSettingModuleEnum,
  ColumnSettingKeyEnum,
  ColumnSettingNameDefaultEnum,
} = require('@iso/lms/enums/columnSetting.enum');
const { ColumnSetting } = require('@iso/lms/models/columnSetting.model');
const { config } = require('../../../migrations/helper/utility');
const { askToConfirm } = require('../../utils/askConfirmScript');
const { connectToDb } = require('../../utils/db');
const { CourseObjectiveTypeEnum, RegulatorEnum } = require('@iso/lms/enums/course.enum');

const userColumnSettingList = [
  {
    key: ColumnSettingKeyEnum.USER_USERNAME,
    name: ColumnSettingNameDefaultEnum.USER_USERNAME,
    localeKey: 'user.username',
    module: ColumnSettingModuleEnum.USER,
    groupFields: [
      {
        root: '',
        dataType: ColumnSettingDataTypeEnum.text,
        columnCode: 'username',
        relationalPath: null,
        defaultValue: null,
        isFilter: true,
        isDisplay: true,
      },
    ],
    fieldType: ColumnSettingFieldTypeEnum.primary,
    filterType: ColumnSettingFilterTypeEnum.text,
    dataType: ColumnSettingDataTypeEnum.text,
    dropdownValue: [],
    dropdownValueType: ColumnSettingDropdownValueTypeEnum.static,
    columnBulkHeader: 'username',
    isUnique: true,
  },
  {
    key: ColumnSettingKeyEnum.USER_EMAIL,
    name: ColumnSettingNameDefaultEnum.USER_EMAIL,
    localeKey: 'user.email',
    module: ColumnSettingModuleEnum.USER,
    groupFields: [
      {
        root: '',
        dataType: ColumnSettingDataTypeEnum.text,
        columnCode: 'email',
        relationalPath: null,
        isFilter: true,
        isDisplay: true,
      },
    ],
    dataType: ColumnSettingDataTypeEnum.text,
    fieldType: ColumnSettingFieldTypeEnum.primary,
    filterType: ColumnSettingFilterTypeEnum.text,
    dropdownValue: [],
    dropdownValueType: ColumnSettingDropdownValueTypeEnum.static,
    columnBulkHeader: 'email',
    isUnique: true,
  },
  {
    key: ColumnSettingKeyEnum.USER_FULLNAME,
    name: ColumnSettingNameDefaultEnum.USER_FULLNAME,
    localeKey: 'user.fullname',
    module: ColumnSettingModuleEnum.USER,
    groupFields: [
      {
        root: 'profile',
        dataType: ColumnSettingDataTypeEnum.text,
        columnCode: 'salute',
        relationalPath: null,
        isFilter: false,
        isDisplay: true,
      },
      {
        root: 'profile',
        dataType: ColumnSettingDataTypeEnum.text,
        columnCode: 'firstname',
        relationalPath: null,
        isFilter: true,
        isDisplay: true,
      },
      {
        root: 'profile',
        dataType: ColumnSettingDataTypeEnum.text,
        columnCode: 'middlename',
        relationalPath: null,
        isFilter: false,
        isDisplay: true,
      },
      {
        root: 'profile',
        dataType: ColumnSettingDataTypeEnum.text,
        columnCode: 'lastname',
        relationalPath: null,
        isFilter: true,
        isDisplay: true,
      },
    ],
    dataType: ColumnSettingDataTypeEnum.text,
    fieldType: ColumnSettingFieldTypeEnum.primary,
    filterType: ColumnSettingFilterTypeEnum.text,
    dropdownValue: [],
    dropdownValueType: ColumnSettingDropdownValueTypeEnum.static,
    columnBulkHeader: 'fullname',
  },
  {
    key: ColumnSettingKeyEnum.USER_FIRSTNAME_LASTNAME,
    name: ColumnSettingNameDefaultEnum.USER_FIRSTNAME_LASTNAME,
    localeKey: 'd.user_group.user_full_name',
    module: ColumnSettingModuleEnum.USER,
    groupFields: [
      {
        root: 'profile',
        dataType: ColumnSettingDataTypeEnum.text,
        columnCode: 'firstname',
        relationalPath: null,
        isFilter: true,
        isDisplay: true,
      },
      {
        root: 'profile',
        dataType: ColumnSettingDataTypeEnum.text,
        columnCode: 'lastname',
        relationalPath: null,
        isFilter: true,
        isDisplay: true,
      },
    ],
    dataType: ColumnSettingDataTypeEnum.text,
    fieldType: ColumnSettingFieldTypeEnum.primary,
    filterType: ColumnSettingFilterTypeEnum.text,
    dropdownValue: [],
    dropdownValueType: ColumnSettingDropdownValueTypeEnum.static,
    columnBulkHeader: 'firstname_lastname',
  },
  {
    key: ColumnSettingKeyEnum.USER_SALUTE,
    name: ColumnSettingNameDefaultEnum.USER_SALUTE,
    localeKey: 'user.salute',
    module: ColumnSettingModuleEnum.USER,
    groupFields: [
      {
        root: 'profile',
        dataType: ColumnSettingDataTypeEnum.text,
        columnCode: 'salute',
        relationalPath: null,
        isFilter: true,
        isDisplay: true,
      },
    ],
    dataType: ColumnSettingDataTypeEnum.text,
    fieldType: ColumnSettingFieldTypeEnum.primary,
    filterType: ColumnSettingFilterTypeEnum.text,
    dropdownValue: [],
    dropdownValueType: ColumnSettingDropdownValueTypeEnum.static,
    columnBulkHeader: 'salute',
  },
  {
    key: ColumnSettingKeyEnum.USER_FIRSTNAME,
    name: ColumnSettingNameDefaultEnum.USER_FIRSTNAME,
    localeKey: 'user.firstname',
    module: ColumnSettingModuleEnum.USER,
    groupFields: [
      {
        root: 'profile',
        dataType: ColumnSettingDataTypeEnum.text,
        columnCode: 'firstname',
        relationalPath: null,
        isFilter: true,
        isDisplay: true,
      },
    ],
    fieldType: ColumnSettingFieldTypeEnum.primary,
    filterType: ColumnSettingFilterTypeEnum.text,
    dataType: ColumnSettingDataTypeEnum.text,
    dropdownValue: [],
    dropdownValueType: ColumnSettingDropdownValueTypeEnum.static,
    columnBulkHeader: 'firstname',
  },
  {
    key: ColumnSettingKeyEnum.USER_MIDDLENAME,
    name: ColumnSettingNameDefaultEnum.USER_MIDDLENAME,
    localeKey: 'user.middlename',
    module: ColumnSettingModuleEnum.USER,
    groupFields: [
      {
        root: 'profile',
        dataType: ColumnSettingDataTypeEnum.text,
        columnCode: 'middlename',
        relationalPath: null,
        isFilter: true,
        isDisplay: true,
      },
    ],
    fieldType: ColumnSettingFieldTypeEnum.primary,
    filterType: ColumnSettingFilterTypeEnum.text,
    dataType: ColumnSettingDataTypeEnum.text,
    dropdownValue: [],
    dropdownValueType: ColumnSettingDropdownValueTypeEnum.static,
    columnBulkHeader: 'middlename',
  },
  {
    key: ColumnSettingKeyEnum.USER_LASTNAME,
    name: ColumnSettingNameDefaultEnum.USER_LASTNAME,
    localeKey: 'user.lastname',
    module: ColumnSettingModuleEnum.USER,
    groupFields: [
      {
        root: 'profile',
        dataType: ColumnSettingDataTypeEnum.text,
        columnCode: 'lastname',
        relationalPath: null,
        isFilter: true,
        isDisplay: true,
      },
    ],
    fieldType: ColumnSettingFieldTypeEnum.primary,
    filterType: ColumnSettingFilterTypeEnum.text,
    dataType: ColumnSettingDataTypeEnum.text,
    dropdownValue: [],
    dropdownValueType: ColumnSettingDropdownValueTypeEnum.static,
    columnBulkHeader: 'lastname',
  },
  {
    key: ColumnSettingKeyEnum.USER_CITIZEN_ID,
    name: ColumnSettingNameDefaultEnum.USER_CITIZEN_ID,
    localeKey: 'user.citizenId',
    module: ColumnSettingModuleEnum.USER,
    groupFields: [
      {
        root: '',
        dataType: ColumnSettingDataTypeEnum.citizenId,
        columnCode: 'citizenId',
        relationalPath: null,
        isFilter: true,
        isDisplay: true,
      },
    ],
    fieldType: ColumnSettingFieldTypeEnum.primary,
    filterType: ColumnSettingFilterTypeEnum.text,
    dataType: ColumnSettingDataTypeEnum.citizenId,
    dropdownValue: [],
    dropdownValueType: ColumnSettingDropdownValueTypeEnum.static,
    columnBulkCode: 'citizenId',
    columnBulkHeader: 'citizenId',
    isUnique: true,
  },
  {
    key: ColumnSettingKeyEnum.USER_MOBILE_PHONE_NUMBER,
    name: ColumnSettingNameDefaultEnum.USER_MOBILE_PHONE_NUMBER,
    localeKey: 'user.mobilePhoneNumber',
    module: ColumnSettingModuleEnum.USER,
    groupFields: [
      {
        root: 'profile',
        dataType: ColumnSettingDataTypeEnum.phone,
        columnCode: 'mobilePhoneNumber',
        relationalPath: null,
        isFilter: true,
        isDisplay: true,
      },
    ],
    fieldType: ColumnSettingFieldTypeEnum.primary,
    filterType: ColumnSettingFilterTypeEnum.text,
    dataType: ColumnSettingDataTypeEnum.phone,
    dropdownValue: [],
    dropdownValueType: ColumnSettingDropdownValueTypeEnum.static,
    columnBulkCode: 'mobilePhoneNumber',
    columnBulkHeader: 'phone_no',
  },
  {
    key: ColumnSettingKeyEnum.USER_GENDER,
    name: ColumnSettingNameDefaultEnum.USER_GENDER,
    localeKey: 'user.gender',
    module: ColumnSettingModuleEnum.USER,
    groupFields: [
      {
        root: 'profile',
        dataType: ColumnSettingDataTypeEnum.enum,
        columnCode: 'gender',
        relationalPath: null,
        isFilter: true,
        isDisplay: true,
      },
    ],
    fieldType: ColumnSettingFieldTypeEnum.primary,
    filterType: ColumnSettingFilterTypeEnum.singleSelector,
    dataType: ColumnSettingDataTypeEnum.text,
    dropdownValue: [
      {
        value: 'MALE',
        label: 'ชาย',
      },
      {
        value: 'FEMALE',
        label: 'หญิง',
      },
      {
        value: 'UNSPECIFIC',
        label: 'ไม่ระบุ',
      },
    ],
    dropdownValueType: ColumnSettingDropdownValueTypeEnum.static,
    columnBulkCode: 'gender',
    columnBulkHeader: 'gender_(FEMALE/MALE/UNSPECIFIC)',
  },
  {
    key: ColumnSettingKeyEnum.USER_DATE_OF_BIRTH,
    name: ColumnSettingNameDefaultEnum.USER_DATE_OF_BIRTH,
    localeKey: 'user.dateOfBirth',
    module: ColumnSettingModuleEnum.USER,
    groupFields: [
      {
        root: 'profile',
        dataType: ColumnSettingDataTypeEnum.date,
        columnCode: 'dateOfBirth',
        relationalPath: null,
        isFilter: true,
        isDisplay: true,
      },
    ],
    fieldType: ColumnSettingFieldTypeEnum.primary,
    filterType: ColumnSettingFilterTypeEnum.dateRange,
    dataType: ColumnSettingDataTypeEnum.date,
    dropdownValue: [],
    dropdownValueType: ColumnSettingDropdownValueTypeEnum.static,
    columnBulkCode: 'dateOfBirth',
    columnBulkHeader: 'date_of_birth_(DD/MM/YYYY)',
  },
  {
    key: ColumnSettingKeyEnum.USER_EMPLOYEE_ID,
    name: ColumnSettingNameDefaultEnum.USER_EMPLOYEE_ID,
    localeKey: 'user.employeeId',
    module: ColumnSettingModuleEnum.USER,
    groupFields: [
      {
        root: '',
        dataType: ColumnSettingDataTypeEnum.text,
        columnCode: 'employeeId',
        relationalPath: null,
        isFilter: true,
        isDisplay: true,
      },
    ],
    fieldType: ColumnSettingFieldTypeEnum.primary,
    filterType: ColumnSettingFilterTypeEnum.text,
    dataType: ColumnSettingDataTypeEnum.text,
    dropdownValue: [],
    dropdownValueType: ColumnSettingDropdownValueTypeEnum.static,
    columnBulkCode: 'employeeId',
    columnBulkHeader: 'employeeId',
  },
  {
    key: ColumnSettingKeyEnum.USER_POSITION,
    name: ColumnSettingNameDefaultEnum.USER_POSITION,
    localeKey: 'user.position',
    module: ColumnSettingModuleEnum.USER,
    groupFields: [
      {
        root: '',
        dataType: ColumnSettingDataTypeEnum.text,
        columnCode: 'position',
        relationalPath: null,
        isFilter: true,
        isDisplay: true,
      },
    ],
    fieldType: ColumnSettingFieldTypeEnum.primary,
    filterType: ColumnSettingFilterTypeEnum.text,
    dataType: ColumnSettingDataTypeEnum.text,
    dropdownValue: [],
    dropdownValueType: ColumnSettingDropdownValueTypeEnum.static,
    columnBulkCode: 'position',
    columnBulkHeader: 'position',
  },

  {
    key: ColumnSettingKeyEnum.USER_AVATAR,
    name: ColumnSettingNameDefaultEnum.USER_AVATAR,
    localeKey: 'user.avatar',
    module: ColumnSettingModuleEnum.USER,
    groupFields: [
      {
        root: 'profile',
        dataType: ColumnSettingDataTypeEnum.imageAvatarUrl,
        columnCode: 'avatar',
        relationalPath: null,
        isFilter: false,
        isDisplay: true,
      },
    ],
    fieldType: ColumnSettingFieldTypeEnum.primary,
    filterType: ColumnSettingFilterTypeEnum.text,
    dataType: ColumnSettingDataTypeEnum.text,
    dropdownValue: [],
    dropdownValueType: ColumnSettingDropdownValueTypeEnum.static,
    columnBulkHeader: 'avatar',
  },
  {
    key: ColumnSettingKeyEnum.USER_ACTIVE,
    name: ColumnSettingNameDefaultEnum.USER_ACTIVE,
    localeKey: 'user.active',
    module: ColumnSettingModuleEnum.USER,
    groupFields: [
      {
        root: '',
        dataType: ColumnSettingDataTypeEnum.flag,
        columnCode: 'active',
        relationalPath: null,
        isFilter: true,
        isDisplay: true,
      },
    ],
    fieldType: ColumnSettingFieldTypeEnum.primary,
    filterType: ColumnSettingFilterTypeEnum.singleSelector,
    dataType: ColumnSettingDataTypeEnum.flag,
    dropdownValue: [
      {
        value: true,
        label: 'เปิดใช้งาน',
      },
      {
        value: false,
        label: 'ปิดใช้งาน',
      },
    ],
    dropdownValueType: ColumnSettingDropdownValueTypeEnum.static,
    columnBulkHeader: 'active',
  },
  {
    key: ColumnSettingKeyEnum.USER_OIC_LICENSE_LIFE_NO,
    name: ColumnSettingNameDefaultEnum.USER_OIC_LICENSE_LIFE_NO,
    localeKey: 'user.oicLicenseLifeNo',
    module: ColumnSettingModuleEnum.USER,
    groupFields: [
      {
        root: '',
        dataType: ColumnSettingDataTypeEnum.text,
        columnCode: 'licenseNo',
        relationalPath: 'users.guid->licenses.userId',
        isFilter: true,
        isDisplay: true,
        displayConditions: [
          {
            columnCode: 'licenseTypeCode',
            value: 'OIC-002',
          },
        ],
      },
      {
        root: '',
        dataType: ColumnSettingDataTypeEnum.text,
        columnCode: 'licenseTypeCode',
        relationalPath: 'users.guid->licenses.userId',
        isDefault: true,
        defaultValue: 'OIC-002',
        isFilter: true,
        isDisplay: false,
        filterType: ColumnSettingFilterTypeEnum.text,
      },
    ],
    fieldType: ColumnSettingFieldTypeEnum.primary,
    filterType: ColumnSettingFilterTypeEnum.text,
    dropdownValue: [],
    dropdownValueType: ColumnSettingDropdownValueTypeEnum.static,
    columnBulkCode: 'license.oicLicenseLifeNumber',
    columnBulkHeader: 'oic_license_life',
  },
  {
    key: ColumnSettingKeyEnum.USER_OIC_LICENSE_LIFE_START_DATE,
    name: ColumnSettingNameDefaultEnum.USER_OIC_LICENSE_LIFE_START_DATE,
    localeKey: 'user.oicStartDateLife',
    module: ColumnSettingModuleEnum.USER,
    groupFields: [
      {
        root: '',
        dataType: ColumnSettingDataTypeEnum.date,
        columnCode: 'startedAt',
        relationalPath: 'users.guid->licenses.userId',
        isFilter: true,
        isDisplay: true,
        displayConditions: [
          {
            columnCode: 'licenseTypeCode',
            value: 'OIC-002',
          },
        ],
      },
      {
        root: '',
        dataType: ColumnSettingDataTypeEnum.text,
        columnCode: 'licenseTypeCode',
        relationalPath: 'users.guid->licenses.userId',
        isDefault: true,
        defaultValue: 'OIC-002',
        isFilter: true,
        isDisplay: false,
        filterType: ColumnSettingFilterTypeEnum.text,
      },
    ],
    fieldType: ColumnSettingFieldTypeEnum.primary,
    filterType: ColumnSettingFilterTypeEnum.dateRange,
    dropdownValue: [],
    dropdownValueType: ColumnSettingDropdownValueTypeEnum.static,
    columnBulkCode: 'license.oicLicenseLifeStartedAt',
    columnBulkHeader: 'oic_startdate_life_(DD/MM/YYYY)',
  },
  {
    key: ColumnSettingKeyEnum.USER_OIC_LICENSE_LIFE_END_DATE,
    name: ColumnSettingNameDefaultEnum.USER_OIC_LICENSE_LIFE_END_DATE,
    localeKey: 'user.oicEndDateLife',
    module: ColumnSettingModuleEnum.USER,
    groupFields: [
      {
        root: '',
        dataType: ColumnSettingDataTypeEnum.date,
        columnCode: 'expiredAt',
        relationalPath: 'users.guid->licenses.userId',
        isFilter: true,
        isDisplay: true,
        displayConditions: [
          {
            columnCode: 'licenseTypeCode',
            value: 'OIC-002',
          },
        ],
      },
      {
        root: '',
        dataType: ColumnSettingDataTypeEnum.text,
        columnCode: 'licenseTypeCode',
        relationalPath: 'users.guid->licenses.userId',
        isDefault: true,
        defaultValue: 'OIC-002',
        filterType: ColumnSettingFilterTypeEnum.text,
        isFilter: true,
        isDisplay: false,
      },
    ],
    fieldType: ColumnSettingFieldTypeEnum.primary,
    filterType: ColumnSettingFilterTypeEnum.dateRange,
    dropdownValue: [],
    dropdownValueType: ColumnSettingDropdownValueTypeEnum.static,
    columnBulkCode: 'license.oicLicenseLifeExpiredAt',
    columnBulkHeader: 'oic_enddate_life_(DD/MM/YYYY)',
  },
  {
    key: ColumnSettingKeyEnum.USER_OIC_LICENSE_NON_LIFE_NO,
    name: ColumnSettingNameDefaultEnum.USER_OIC_LICENSE_NON_LIFE_NO,
    localeKey: 'user.oicLicenseNonLife',
    module: ColumnSettingModuleEnum.USER,
    groupFields: [
      {
        root: '',
        dataType: ColumnSettingDataTypeEnum.text,
        columnCode: 'licenseNo',
        relationalPath: 'users.guid->licenses.userId',
        isFilter: true,
        isDisplay: true,
        displayConditions: [
          {
            columnCode: 'licenseTypeCode',
            value: 'OIC-001',
          },
        ],
      },
      {
        root: '',
        dataType: ColumnSettingDataTypeEnum.text,
        columnCode: 'licenseTypeCode',
        relationalPath: 'users.guid->licenses.userId',
        isDefault: true,
        defaultValue: 'OIC-001',
        isFilter: true,
        isDisplay: false,
        filterType: ColumnSettingFilterTypeEnum.text,
      },
    ],
    fieldType: ColumnSettingFieldTypeEnum.primary,
    filterType: ColumnSettingFilterTypeEnum.text,
    dropdownValue: [],
    dropdownValueType: ColumnSettingDropdownValueTypeEnum.static,
    columnBulkCode: 'license.oicLicenseNonLifeNumber',
    columnBulkHeader: 'oic_license_nonlife',
  },
  {
    key: ColumnSettingKeyEnum.USER_OIC_LICENSE_NON_LIFE_START_DATE,
    name: ColumnSettingNameDefaultEnum.USER_OIC_LICENSE_NON_LIFE_START_DATE,
    localeKey: 'user.oicStartDateNonLife',
    module: ColumnSettingModuleEnum.USER,
    groupFields: [
      {
        root: '',
        dataType: ColumnSettingDataTypeEnum.date,
        columnCode: 'startedAt',
        relationalPath: 'users.guid->licenses.userId',
        isFilter: true,
        isDisplay: true,
        displayConditions: [
          {
            columnCode: 'licenseTypeCode',
            value: 'OIC-001',
          },
        ],
      },
      {
        root: '',
        dataType: ColumnSettingDataTypeEnum.text,
        columnCode: 'licenseTypeCode',
        relationalPath: 'users.guid->licenses.userId',
        isDefault: true,
        defaultValue: 'OIC-001',
        isFilter: true,
        isDisplay: false,
        filterType: ColumnSettingFilterTypeEnum.text,
      },
    ],
    fieldType: ColumnSettingFieldTypeEnum.primary,
    filterType: ColumnSettingFilterTypeEnum.dateRange,
    dropdownValue: [],
    dropdownValueType: ColumnSettingDropdownValueTypeEnum.static,
    columnBulkCode: 'license.oicLicenseNonLifeStartedAt',
    columnBulkHeader: 'oic_startdate_nonlife_(DD/MM/YYYY)',
  },
  {
    key: ColumnSettingKeyEnum.USER_OIC_LICENSE_NON_LIFE_END_DATE,
    name: ColumnSettingNameDefaultEnum.USER_OIC_LICENSE_NON_LIFE_END_DATE,
    localeKey: 'user.oicEndDateNonLife',
    module: ColumnSettingModuleEnum.USER,
    groupFields: [
      {
        root: '',
        dataType: ColumnSettingDataTypeEnum.date,
        columnCode: 'expiredAt',
        relationalPath: 'users.guid->licenses.userId',
        isFilter: true,
        isDisplay: true,
        displayConditions: [
          {
            columnCode: 'licenseTypeCode',
            value: 'OIC-001',
          },
        ],
      },
      {
        root: '',
        dataType: ColumnSettingDataTypeEnum.text,
        columnCode: 'licenseTypeCode',
        relationalPath: 'users.guid->licenses.userId',
        isDefault: true,
        defaultValue: 'OIC-001',
        filterType: ColumnSettingFilterTypeEnum.text,
        isFilter: true,
        isDisplay: false,
      },
    ],
    fieldType: ColumnSettingFieldTypeEnum.primary,
    filterType: ColumnSettingFilterTypeEnum.dateRange,
    dropdownValue: [],
    dropdownValueType: ColumnSettingDropdownValueTypeEnum.static,
    columnBulkCode: 'license.oicLicenseNonLifeExpiredAt',
    columnBulkHeader: 'oic_enddate_nonlife_(DD/MM/YYYY)',
  },
  {
    key: ColumnSettingKeyEnum.USER_TSI_LICENSE_NO,
    name: ColumnSettingNameDefaultEnum.USER_TSI_LICENSE_NO,
    localeKey: 'user.tsiLicenseNo',
    module: ColumnSettingModuleEnum.USER,
    groupFields: [
      {
        root: '',
        dataType: ColumnSettingDataTypeEnum.text,
        columnCode: 'licenseNo',
        relationalPath: 'users.guid->licenses.userId',
        isFilter: true,
        isDisplay: true,
        displayConditions: [
          {
            columnCode: 'licenseTypeCode',
            value: 'TSI-001',
          },
        ],
      },
      {
        root: '',
        dataType: ColumnSettingDataTypeEnum.text,
        columnCode: 'licenseTypeCode',
        relationalPath: 'users.guid->licenses.userId',
        isDefault: true,
        defaultValue: 'TSI-001',
        isFilter: true,
        isDisplay: false,
        filterType: ColumnSettingFilterTypeEnum.text,
      },
    ],
    fieldType: ColumnSettingFieldTypeEnum.primary,
    filterType: ColumnSettingFilterTypeEnum.text,
    dropdownValue: [],
    dropdownValueType: ColumnSettingDropdownValueTypeEnum.static,
    columnBulkCode: 'license.tsiLicenseNumber',
    columnBulkHeader: 'tsi_license_no',
  },
  {
    key: ColumnSettingKeyEnum.USER_TSI_LICENSE_TYPE,
    name: ColumnSettingNameDefaultEnum.USER_TSI_LICENSE_TYPE,
    localeKey: 'user.tsiLicenseType',
    module: ColumnSettingModuleEnum.USER,
    groupFields: [
      {
        root: '',
        dataType: ColumnSettingDataTypeEnum.text,
        columnCode: 'type',
        relationalPath: 'users.guid->licenses.userId',
        isFilter: true,
        isDisplay: true,
        displayConditions: [
          {
            columnCode: 'licenseTypeCode',
            value: 'TSI-001',
          },
        ],
      },
      {
        root: '',
        dataType: ColumnSettingDataTypeEnum.text,
        columnCode: 'licenseTypeCode',
        relationalPath: 'users.guid->licenses.userId',
        isDefault: true,
        defaultValue: 'TSI-001',
        isFilter: true,
        isDisplay: false,
        filterType: ColumnSettingFilterTypeEnum.text,
      },
    ],
    fieldType: ColumnSettingFieldTypeEnum.primary,
    filterType: ColumnSettingFilterTypeEnum.multipleSelector,
    dropdownValue: [
      {
        value: 'ผู้แนะนำการลงทุน',
        label: 'ผู้แนะนำการลงทุน',
      },
      {
        value: 'ผู้วางแผนการลงทุน',
        label: 'ผู้วางแผนการลงทุน',
      },
      {
        value: 'นักวิเคราะห์การลงทุน',
        label: 'นักวิเคราะห์การลงทุน',
      },
    ],
    dropdownValueType: ColumnSettingDropdownValueTypeEnum.static,
    columnBulkCode: 'license.tsiLicenseType',
    columnBulkHeader: 'tsi_license_type',
  },
  {
    key: ColumnSettingKeyEnum.USER_TSI_LICENSE_START_DATE,
    name: ColumnSettingNameDefaultEnum.USER_TSI_LICENSE_START_DATE,
    localeKey: 'user.tsiStartDate',
    module: ColumnSettingModuleEnum.USER,
    groupFields: [
      {
        root: '',
        dataType: ColumnSettingDataTypeEnum.date,
        columnCode: 'startedAt',
        relationalPath: 'users.guid->licenses.userId',
        isFilter: true,
        isDisplay: true,
        displayConditions: [
          {
            columnCode: 'licenseTypeCode',
            value: 'TSI-001',
          },
        ],
      },
      {
        root: '',
        dataType: ColumnSettingDataTypeEnum.text,
        columnCode: 'licenseTypeCode',
        relationalPath: 'users.guid->licenses.userId',
        isDefault: true,
        defaultValue: 'TSI-001',
        isFilter: true,
        isDisplay: false,
        filterType: ColumnSettingFilterTypeEnum.text,
      },
    ],
    fieldType: ColumnSettingFieldTypeEnum.primary,
    filterType: ColumnSettingFilterTypeEnum.dateRange,
    dropdownValueType: ColumnSettingDropdownValueTypeEnum.static,
    dropdownValue: [],
    columnBulkCode: 'license.tsiLicenseStartedAt',
    columnBulkHeader: 'tsi_startdate_(DD/MM/YYYY)',
  },
  {
    key: ColumnSettingKeyEnum.USER_TSI_LICENSE_END_DATE,
    name: ColumnSettingNameDefaultEnum.USER_TSI_LICENSE_END_DATE,
    localeKey: 'user.tsiEndDate',
    module: ColumnSettingModuleEnum.USER,
    groupFields: [
      {
        root: '',
        dataType: ColumnSettingDataTypeEnum.date,
        columnCode: 'expiredAt',
        relationalPath: 'users.guid->licenses.userId',
        isFilter: true,
        isDisplay: true,
        displayConditions: [
          {
            columnCode: 'licenseTypeCode',
            value: 'TSI-001',
          },
        ],
      },
      {
        root: '',
        dataType: ColumnSettingDataTypeEnum.text,
        columnCode: 'licenseTypeCode',
        relationalPath: 'users.guid->licenses.userId',
        isDefault: true,
        defaultValue: 'TSI-001',
        isFilter: true,
        isDisplay: false,
        filterType: ColumnSettingFilterTypeEnum.text,
      },
    ],
    fieldType: ColumnSettingFieldTypeEnum.primary,
    filterType: ColumnSettingFilterTypeEnum.dateRange,
    dropdownValue: [],
    dropdownValueType: ColumnSettingDropdownValueTypeEnum.static,
    columnBulkCode: 'license.tsiLicenseExpiredAt',
    columnBulkHeader: 'tsi_enddate_(DD/MM/YYYY)',
  },
  {
    key: ColumnSettingKeyEnum.USER_IS_TERMINATED,
    name: ColumnSettingNameDefaultEnum.USER_IS_TERMINATED,
    localeKey: 'user.isTerminated',
    module: ColumnSettingModuleEnum.USER,
    groupFields: [
      {
        columnCode: 'isTerminated',
        root: '',
        dataType: ColumnSettingDataTypeEnum.enum,
        relationalPath: null,
        isFilter: true,
        isDisplay: true,
      },
    ],
    fieldType: ColumnSettingFieldTypeEnum.primary,
    filterType: ColumnSettingFilterTypeEnum.singleSelector,
    dataType: ColumnSettingDataTypeEnum.flag,
    dropdownValue: [
      {
        value: false,
        label: 'ปฏิบัติงาน',
      },
      {
        value: true,
        label: 'สิ้นสุดการปฏิบัติงาน',
      },
    ],
    dropdownValueType: ColumnSettingDropdownValueTypeEnum.static,
    columnBulkCode: 'isTerminated',
    columnBulkHeader: 'is_terminated',
  },
  {
    key: ColumnSettingKeyEnum.USER_IS_PASSED_UL_SALE_QUALIFY,
    name: ColumnSettingNameDefaultEnum.USER_IS_PASSED_UL_SALE_QUALIFY,
    localeKey: 'user.isPassedUlSaleQualify',
    module: ColumnSettingModuleEnum.USER,
    groupFields: [
      {
        columnCode: 'isPassedUlSaleQualify',
        root: '',
        dataType: ColumnSettingDataTypeEnum.enum,
        relationalPath: null,
        isFilter: true,
        isDisplay: true,
      },
    ],
    fieldType: ColumnSettingFieldTypeEnum.primary,
    filterType: ColumnSettingFilterTypeEnum.singleSelector,
    dataType: ColumnSettingDataTypeEnum.flag,
    dropdownValue: [
      {
        value: true,
        label: 'ผ่าน',
      },
      {
        value: false,
        label: 'ไม่ผ่าน',
      },
    ],
    dropdownValueType: ColumnSettingDropdownValueTypeEnum.static,
    columnBulkCode: 'isPassedUlSaleQualify',
    columnBulkHeader: 'is_passed_ul_sale_qualify',
  },
  {
    key: ColumnSettingKeyEnum.USER_DEPARTMENT,
    name: ColumnSettingNameDefaultEnum.USER_DEPARTMENT,
    localeKey: 'user.department',
    module: ColumnSettingModuleEnum.USER,
    groupFields: [
      {
        root: '',
        dataType: ColumnSettingDataTypeEnum.text,
        columnCode: 'id',
        relationalPath: 'users.guid->departments.userIds',
        isFilter: true,
        isDisplay: false,
      },
      {
        root: '',
        dataType: ColumnSettingDataTypeEnum.text,
        columnCode: 'name',
        relationalPath: 'users.guid->departments.userIds',
        isFilter: false,
        isDisplay: true,
      },
    ],
    fieldType: ColumnSettingFieldTypeEnum.primary,
    filterType: ColumnSettingFilterTypeEnum.multipleSelector,
    dropdownValue: [],
    dropdownValueType: ColumnSettingDropdownValueTypeEnum.static,
    columnBulkHeader: 'department',
  },
  {
    key: ColumnSettingKeyEnum.USER_SUPERVISOR,
    name: ColumnSettingNameDefaultEnum.USER_SUPERVISOR,
    localeKey: 'user.supervisor',
    module: ColumnSettingModuleEnum.USER,
    groupFields: [
      {
        root: 'profile',
        dataType: ColumnSettingDataTypeEnum.text,
        columnCode: 'salute',
        relationalPath: 'users.guid->user-direct-reports.userId,user-direct-reports.directReportUserId->users.guid',
        isFilter: false,
        isDisplay: true,
      },
      {
        root: 'profile',
        dataType: ColumnSettingDataTypeEnum.text,
        columnCode: 'firstname',
        relationalPath: 'users.guid->user-direct-reports.userId,user-direct-reports.directReportUserId->users.guid',
        isFilter: true,
        isDisplay: true,
      },
      {
        root: 'profile',
        dataType: ColumnSettingDataTypeEnum.text,
        columnCode: 'lastname',
        relationalPath: 'users.guid->user-direct-reports.userId,user-direct-reports.directReportUserId->users.guid',
        isFilter: true,
        isDisplay: true,
      },
    ],
    fieldType: ColumnSettingFieldTypeEnum.primary,
    filterType: ColumnSettingFilterTypeEnum.text,
    dropdownValue: [],
    dropdownValueType: ColumnSettingDropdownValueTypeEnum.static,
    columnBulkHeader: 'supervisor',
  },
  {
    key: ColumnSettingKeyEnum.USER_PERMISSION_GROUP,
    name: ColumnSettingNameDefaultEnum.USER_PERMISSION_GROUP,
    localeKey: 'user.permissionGroup',
    module: ColumnSettingModuleEnum.USER,
    groupFields: [
      {
        root: '',
        dataType: ColumnSettingDataTypeEnum.text,
        columnCode: 'id',
        relationalPath: 'users.permissionGroupIds->permission-groups.id',
        isFilter: true,
        isDisplay: false,
      },
      {
        root: '',
        dataType: ColumnSettingDataTypeEnum.text,
        columnCode: 'name',
        relationalPath: 'users.permissionGroupIds->permission-groups.id',
        isFilter: false,
        isDisplay: true,
      },
    ],
    fieldType: ColumnSettingFieldTypeEnum.primary,
    filterType: ColumnSettingFilterTypeEnum.multipleSelector,
    dropdownValue: [],
    dropdownValueType: ColumnSettingDropdownValueTypeEnum.static,
    columnBulkHeader: 'permission_group',
  },
  {
    key: ColumnSettingKeyEnum.USER_CUSTOMER,
    name: ColumnSettingNameDefaultEnum.USER_CUSTOMER,
    localeKey: 'user.customer',
    module: ColumnSettingModuleEnum.USER,
    groupFields: [
      {
        root: '',
        dataType: ColumnSettingDataTypeEnum.text,
        columnCode: 'customerCode',
        relationalPath: 'users.customerCodes->customers.customerCode',
        isFilter: true,
        isDisplay: false,
      },
      {
        root: '',
        dataType: ColumnSettingDataTypeEnum.text,
        columnCode: 'customerName',
        relationalPath: 'users.customerCodes->customers.customerCode',
        isFilter: true,
        isDisplay: true,
      },
    ],
    fieldType: ColumnSettingFieldTypeEnum.primary,
    filterType: ColumnSettingFilterTypeEnum.text,
    dropdownValue: [],
    dropdownValueType: ColumnSettingDropdownValueTypeEnum.static,
    columnBulkHeader: 'customer',
  },
];

const courseColumnSettingList = [
  {
    key: ColumnSettingKeyEnum.COURSE_CODE,
    name: ColumnSettingNameDefaultEnum.COURSE_CODE,
    localeKey: 'course.code',
    module: ColumnSettingModuleEnum.COURSE,
    groupFields: [
      {
        root: '',
        dataType: ColumnSettingDataTypeEnum.text,
        columnCode: 'code',
        relationalPath: null,
        isFilter: true,
        isDisplay: true,
      },
    ],
    fieldType: ColumnSettingFieldTypeEnum.primary,
    filterType: ColumnSettingFilterTypeEnum.text,
    displayComponent: {
      componentType: ColumnSettingComponentTypeEnum.text,
      alignment: ColumnSettingAlignmentEnum.left,
    },
    dropdownValue: [],
    dropdownValueType: ColumnSettingDropdownValueTypeEnum.static,
  },
  {
    key: ColumnSettingKeyEnum.COURSE_IMAGE,
    name: ColumnSettingNameDefaultEnum.COURSE_IMAGE,
    localeKey: 'course.image',
    module: ColumnSettingModuleEnum.COURSE,
    groupFields: [
      {
        root: '',
        dataType: ColumnSettingDataTypeEnum.imageUrl,
        columnCode: 'thumbnailUrl',
        relationalPath: null,
        isFilter: false,
        isDisplay: true,
      },
    ],
    fieldType: ColumnSettingFieldTypeEnum.primary,
    filterType: ColumnSettingFilterTypeEnum.text,
    displayComponent: {
      componentType: ColumnSettingComponentTypeEnum.imageRectangle,
    },
    dropdownValue: [],
    dropdownValueType: ColumnSettingDropdownValueTypeEnum.static,
  },
  {
    key: ColumnSettingKeyEnum.COURSE_NAME,
    name: ColumnSettingNameDefaultEnum.COURSE_NAME,
    localeKey: 'course.name',
    module: ColumnSettingModuleEnum.COURSE,
    groupFields: [
      {
        root: '',
        dataType: ColumnSettingDataTypeEnum.text,
        columnCode: 'name',
        relationalPath: 'courses.id->course-versions.courseId',
        isFilter: true,
        isDisplay: true,
      },
    ],
    fieldType: ColumnSettingFieldTypeEnum.primary,
    filterType: ColumnSettingFilterTypeEnum.text,
    displayComponent: {
      componentType: ColumnSettingComponentTypeEnum.text,
      alignment: ColumnSettingAlignmentEnum.left,
    },
    dropdownValue: [],
    dropdownValueType: ColumnSettingDropdownValueTypeEnum.static,
  },
  {
    key: ColumnSettingKeyEnum.COURSE_OBJECTIVE,
    name: ColumnSettingNameDefaultEnum.COURSE_OBJECTIVE,
    localeKey: 'course.objective',
    module: ColumnSettingModuleEnum.COURSE,
    groupFields: [
      {
        root: '',
        dataType: ColumnSettingDataTypeEnum.enum,
        columnCode: 'objectiveType',
        relationalPath: null,
        isFilter: true,
        isDisplay: true,
      },
    ],
    displayComponent: {
      componentType: ColumnSettingComponentTypeEnum.text,
      refValue: 'dropdownValue.value',
    },
    fieldType: ColumnSettingFieldTypeEnum.primary,
    filterType: ColumnSettingFilterTypeEnum.multipleSelector,
    dropdownValue: [
      {
        value: CourseObjectiveTypeEnum.REGULAR,
        label: 'การเรียนทั่วไป',
      },
      {
        value: CourseObjectiveTypeEnum.TRAINING,
        label: 'การอบรม',
      },
    ],
    dropdownValueType: ColumnSettingDropdownValueTypeEnum.static,
  },
  {
    key: ColumnSettingKeyEnum.COURSE_REGULATOR,
    name: ColumnSettingNameDefaultEnum.COURSE_REGULATOR,
    localeKey: 'course.regulator',
    module: ColumnSettingModuleEnum.COURSE,
    groupFields: [
      {
        root: 'regulatorInfo',
        dataType: ColumnSettingDataTypeEnum.enum,
        columnCode: 'regulator',
        relationalPath: null,
        isFilter: true,
        isDisplay: true,
      },
    ],
    displayComponent: {
      componentType: ColumnSettingComponentTypeEnum.text,
      refValue: 'dropdownValue.value',
    },
    fieldType: ColumnSettingFieldTypeEnum.primary,
    filterType: ColumnSettingFilterTypeEnum.multipleSelector,
    dropdownValue: [
      {
        value: '',
        label: 'อบรมทั่วไป',
      },
      {
        value: RegulatorEnum.TSI,
        label: 'อบรมวิชาชีพการลงทุน (TSI)',
      },
      {
        value: RegulatorEnum.OIC,
        label: 'อบรมวิชาชีพประกัน (OIC)',
      },
      {
        value: RegulatorEnum.TFAC,
        label: 'อบรมวิชาชีพบัญชี (TFAC)',
      },
    ],
    dropdownValueType: ColumnSettingDropdownValueTypeEnum.static,
  },
  {
    key: ColumnSettingKeyEnum.COURSE_IS_CERTIFICATE_ENABLED,
    name: ColumnSettingNameDefaultEnum.COURSE_CERTIFICATE,
    localeKey: 'course.certificate',
    module: ColumnSettingModuleEnum.COURSE,
    groupFields: [
      {
        root: '',
        dataType: ColumnSettingDataTypeEnum.flag,
        columnCode: 'isCertificateEnabled',
        relationalPath: 'courses.id->course-versions.courseId',
        isFilter: true,
        isDisplay: true,
      },
    ],
    displayComponent: {
      componentType: ColumnSettingComponentTypeEnum.text,
      alignment: ColumnSettingAlignmentEnum.left,
      refValue: 'dropdownValue.value',
    },
    fieldType: ColumnSettingFieldTypeEnum.primary,
    filterType: ColumnSettingFilterTypeEnum.singleSelector,
    dropdownValue: [
      {
        value: true,
        label: 'มี',
      },
      {
        value: false,
        label: 'ไม่มี',
      },
    ],
    dropdownValueType: ColumnSettingDropdownValueTypeEnum.static,
  },
  {
    key: ColumnSettingKeyEnum.COURSE_SELF_ENROLL,
    name: ColumnSettingNameDefaultEnum.COURSE_SELF_ENROLL,
    localeKey: 'course.selfEnroll',
    module: ColumnSettingModuleEnum.COURSE,
    groupFields: [
      {
        root: '',
        dataType: ColumnSettingDataTypeEnum.flag,
        columnCode: 'isSelfEnrollEnabled',
        relationalPath: null,
        isFilter: true,
        isDisplay: true,
      },
    ],
    displayComponent: {
      componentType: ColumnSettingComponentTypeEnum.text,
      alignment: ColumnSettingAlignmentEnum.left,
      refValue: 'dropdownValue.value',
    },
    fieldType: ColumnSettingFieldTypeEnum.primary,
    filterType: ColumnSettingFilterTypeEnum.singleSelector,
    dropdownValue: [
      {
        value: true,
        label: 'เปิดใช้งาน',
      },
      {
        value: false,
        label: 'ปิดใช้งาน',
      },
    ],
    dropdownValueType: ColumnSettingDropdownValueTypeEnum.static,
  },
  {
    key: ColumnSettingKeyEnum.COURSE_IS_ENABLED,
    name: ColumnSettingNameDefaultEnum.COURSE_IS_ENABLED,
    localeKey: 'course.isEnabled',
    module: ColumnSettingModuleEnum.COURSE,
    groupFields: [
      {
        root: '',
        dataType: ColumnSettingDataTypeEnum.flag,
        columnCode: 'isEnabled',
        relationalPath: null,
        isFilter: true,
        isDisplay: true,
      },
    ],
    displayComponent: {
      componentType: ColumnSettingComponentTypeEnum.tag,
      alignment: ColumnSettingAlignmentEnum.left,
      refValue: 'dropdownValue.value',
    },
    fieldType: ColumnSettingFieldTypeEnum.primary,
    filterType: ColumnSettingFilterTypeEnum.singleSelector,
    dropdownValue: [
      {
        value: true,
        label: 'เปิดใช้งาน',
      },
      {
        value: false,
        label: 'ปิดใช้งาน',
      },
    ],
    dropdownValueType: ColumnSettingDropdownValueTypeEnum.static,
  },
  {
    key: ColumnSettingKeyEnum.COURSE_CREATED_AT,
    name: ColumnSettingNameDefaultEnum.COURSE_CREATED_AT,
    localeKey: 'course.createdAt',
    module: ColumnSettingModuleEnum.COURSE,
    groupFields: [
      {
        root: '',
        dataType: ColumnSettingDataTypeEnum.date,
        columnCode: 'createdAt',
        relationalPath: 'courses.id->course-versions.courseId',
        isFilter: true,
        isDisplay: true,
      },
    ],
    displayComponent: {
      componentType: ColumnSettingComponentTypeEnum.text,
      alignment: ColumnSettingAlignmentEnum.left,
    },
    fieldType: ColumnSettingFieldTypeEnum.primary,
    filterType: ColumnSettingFilterTypeEnum.dateRange,
    dropdownValue: [],
    dropdownValueType: ColumnSettingDropdownValueTypeEnum.static,
  },
  {
    key: ColumnSettingKeyEnum.COURSE_UPDATED_AT,
    name: ColumnSettingNameDefaultEnum.COURSE_UPDATED_AT,
    localeKey: 'course.updatedAt',
    module: ColumnSettingModuleEnum.COURSE,
    groupFields: [
      {
        root: '',
        dataType: ColumnSettingDataTypeEnum.date,
        columnCode: 'updatedAt',
        relationalPath: 'courses.id->course-versions.courseId',
        isFilter: true,
        isDisplay: true,
      },
    ],
    displayComponent: {
      componentType: ColumnSettingComponentTypeEnum.text,
      alignment: ColumnSettingAlignmentEnum.left,
    },
    fieldType: ColumnSettingFieldTypeEnum.primary,
    filterType: ColumnSettingFilterTypeEnum.dateRange,
    dropdownValue: [],
    dropdownValueType: ColumnSettingDropdownValueTypeEnum.static,
  },
  {
    key: ColumnSettingKeyEnum.COURSE_PRODUCT_SKU_ID,
    name: ColumnSettingNameDefaultEnum.COURSE_PRODUCT_SKU_ID,
    localeKey: 'course.productSKUId',
    module: ColumnSettingModuleEnum.COURSE,
    groupFields: [
      {
        root: '',
        dataType: ColumnSettingDataTypeEnum.number,
        columnCode: 'productSKUId',
        relationalPath: 'courses.productSKUCourseId->product-sku-courses.id',
        isFilter: true,
        isDisplay: true,
      },
    ],
    displayComponent: {
      componentType: ColumnSettingComponentTypeEnum.text,
      alignment: ColumnSettingAlignmentEnum.left,
    },
    fieldType: ColumnSettingFieldTypeEnum.primary,
    filterType: ColumnSettingFilterTypeEnum.text,
    dropdownValue: [],
    dropdownValueType: ColumnSettingDropdownValueTypeEnum.static,
  },
  {
    key: ColumnSettingKeyEnum.COURSE_PILLAR_NAME,
    name: ColumnSettingNameDefaultEnum.COURSE_PILLAR_NAME,
    localeKey: 'course.pillar_name',
    module: ColumnSettingModuleEnum.COURSE,
    groupFields: [
      {
        root: 'report',
        dataType: ColumnSettingDataTypeEnum.text,
        columnCode: 'pillarName',
        relationalPath: 'courses.id->course-versions.courseId',
        isFilter: true,
        isDisplay: true,
      },
    ],
    displayComponent: {
      componentType: ColumnSettingComponentTypeEnum.text,
      alignment: ColumnSettingAlignmentEnum.left,
    },
    fieldType: ColumnSettingFieldTypeEnum.primary,
    filterType: ColumnSettingFilterTypeEnum.text,
    dropdownValue: [],
    dropdownValueType: ColumnSettingDropdownValueTypeEnum.static,
  },
  {
    key: ColumnSettingKeyEnum.COURSE_CONTENT_PROVIDER,
    name: ColumnSettingNameDefaultEnum.COURSE_CONTENT_PROVIDER,
    localeKey: 'course.contentProvider',
    module: ColumnSettingModuleEnum.COURSE,
    groupFields: [
      {
        root: '',
        dataType: ColumnSettingDataTypeEnum.enum,
        columnCode: 'externalContentTypes',
        relationalPath: null,
        isFilter: true,
        isDisplay: true,
      },
    ],
    displayComponent: {
      componentType: ColumnSettingComponentTypeEnum.text,
      alignment: ColumnSettingAlignmentEnum.left,
    },
    fieldType: ColumnSettingFieldTypeEnum.primary,
    filterType: ColumnSettingFilterTypeEnum.multipleSelector,
    dropdownValue: [
      {
        value: 'SELF',
        label: 'สร้างเนื้อหาเอง',
      },
      {
        value: 'PLAN_PACKAGE',
        label: 'SkillLane Subscription',
      },
      {
        value: 'MARKETPLACE',
        label: 'SkillLane Credit',
      },
    ],
    dropdownValueType: ColumnSettingDropdownValueTypeEnum.static,
  },
  {
    key: ColumnSettingKeyEnum.COURSE_IS_ACTIVATED,
    name: ColumnSettingNameDefaultEnum.COURSE_IS_ACTIVATED,
    localeKey: 'course.isActivated',
    module: ColumnSettingModuleEnum.COURSE,
    groupFields: [
      {
        root: '',
        dataType: ColumnSettingDataTypeEnum.flag,
        columnCode: 'isActivated',
        relationalPath: null,
        isFilter: true,
        isDisplay: true,
      },
    ],
    displayComponent: {
      componentType: ColumnSettingComponentTypeEnum.tag,
      alignment: ColumnSettingAlignmentEnum.left,
      refValue: 'dropdownValue.value',
    },
    fieldType: ColumnSettingFieldTypeEnum.primary,
    filterType: ColumnSettingFilterTypeEnum.singleSelector,
    dropdownValue: [
      {
        value: true,
        label: 'ใช้งานได้',
      },
      {
        value: false,
        label: 'ถูกระงับ',
      },
    ],
    dropdownValueType: ColumnSettingDropdownValueTypeEnum.static,
  },
];

const learningPathColumnSettingList = [
  {
    key: ColumnSettingKeyEnum.LEARNING_PATH_CODE,
    name: ColumnSettingNameDefaultEnum.LEARNING_PATH_CODE,
    localeKey: 'learning_path.column_setting.code',
    module: ColumnSettingModuleEnum.LEARNING_PATH,
    groupFields: [
      {
        root: '',
        dataType: ColumnSettingDataTypeEnum.text,
        columnCode: 'code',
        relationalPath: null,
        isFilter: true,
        isDisplay: true,
      },
    ],
    fieldType: ColumnSettingFieldTypeEnum.primary,
    filterType: ColumnSettingFilterTypeEnum.text,
    dropdownValueType: ColumnSettingDropdownValueTypeEnum.static,
    dropdownValue: [],
  },
  {
    key: ColumnSettingKeyEnum.LEARNING_PATH_THUMBNAIL,
    name: ColumnSettingNameDefaultEnum.LEARNING_PATH_THUMBNAIL,
    localeKey: 'learning_path.column_setting.thumbnail',
    module: ColumnSettingModuleEnum.LEARNING_PATH,
    groupFields: [
      {
        root: '',
        dataType: ColumnSettingDataTypeEnum.imageUrl,
        columnCode: 'thumbnail',
        relationalPath: null,
        isFilter: false,
        isDisplay: true,
      },
    ],
    fieldType: ColumnSettingFieldTypeEnum.primary,
    filterType: ColumnSettingFilterTypeEnum.text,
    dropdownValueType: ColumnSettingDropdownValueTypeEnum.static,
    dropdownValue: [],
  },
  {
    key: ColumnSettingKeyEnum.LEARNING_PATH_NAME,
    name: ColumnSettingNameDefaultEnum.LEARNING_PATH_NAME,
    localeKey: 'learning_path.column_setting.name',
    module: ColumnSettingModuleEnum.LEARNING_PATH,
    groupFields: [
      {
        root: '',
        dataType: ColumnSettingDataTypeEnum.text,
        columnCode: 'name',
        relationalPath: 'learning-paths.id->learning-path-versions.learningPathId',
        isFilter: true,
        isDisplay: true,
      },
    ],
    fieldType: ColumnSettingFieldTypeEnum.primary,
    filterType: ColumnSettingFilterTypeEnum.text,
    dropdownValueType: ColumnSettingDropdownValueTypeEnum.static,
    dropdownValue: [],
  },
  {
    key: ColumnSettingKeyEnum.LEARNING_PATH_IS_CERTIFICATE_ENABLED,
    name: ColumnSettingNameDefaultEnum.LEARNING_PATH_CERTIFICATE,
    localeKey: 'learning_path.column_setting.is_certificate_enabled',
    module: ColumnSettingModuleEnum.LEARNING_PATH,
    groupFields: [
      {
        root: '',
        dataType: ColumnSettingDataTypeEnum.enum,
        columnCode: 'isCertificateEnabled',
        relationalPath: 'learning-paths.id->learning-path-versions.learningPathId',
        isFilter: true,
        isDisplay: true,
      },
    ],
    fieldType: ColumnSettingFieldTypeEnum.primary,
    filterType: ColumnSettingFilterTypeEnum.singleSelector,
    dropdownValueType: ColumnSettingDropdownValueTypeEnum.static,
    dropdownValue: [
      {
        label: 'มี',
        value: true,
      },
      {
        label: 'ไม่มี',
        value: false,
      },
    ],
  },
  {
    key: ColumnSettingKeyEnum.LEARNING_PATH_IS_ENABLED,
    name: ColumnSettingNameDefaultEnum.LEARNING_PATH_IS_ENABLED,
    localeKey: 'learning_path.column_setting.status',
    module: ColumnSettingModuleEnum.LEARNING_PATH,
    groupFields: [
      {
        root: '',
        dataType: ColumnSettingDataTypeEnum.flag,
        columnCode: 'isEnabled',
        relationalPath: null,
        isFilter: true,
        isDisplay: true,
      },
    ],
    fieldType: ColumnSettingFieldTypeEnum.primary,
    filterType: ColumnSettingFilterTypeEnum.singleSelector,
    dropdownValueType: ColumnSettingDropdownValueTypeEnum.static,
    dropdownValue: [
      {
        label: 'เปิดใช้งาน',
        value: true,
      },
      {
        label: 'ปิดใช้งาน',
        value: false,
      },
    ],
  },
  {
    key: ColumnSettingKeyEnum.LEARNING_PATH_NUMBER_OF_CONTENT,
    name: ColumnSettingNameDefaultEnum.LEARNING_PATH_NUMBER_OF_CONTENT,
    localeKey: 'learning_path.column_setting.number_of_content',
    module: ColumnSettingModuleEnum.LEARNING_PATH,
    groupFields: [
      {
        root: '',
        dataType: ColumnSettingDataTypeEnum.text,
        columnCode: 'numberOfContent',
        relationalPath: null,
        isFilter: false,
        isDisplay: true,
      },
    ],
    fieldType: ColumnSettingFieldTypeEnum.primary,
    filterType: ColumnSettingFilterTypeEnum.text,
    dropdownValueType: ColumnSettingDropdownValueTypeEnum.static,
    dropdownValue: [],
  },
  {
    key: ColumnSettingKeyEnum.LEARNING_PATH_ENROLL_TYPE,
    name: ColumnSettingNameDefaultEnum.LEARNING_PATH_ENROLL_TYPE,
    localeKey: 'learning_path.column_setting.enroll_type',
    module: ColumnSettingModuleEnum.LEARNING_PATH,
    groupFields: [
      {
        root: '',
        dataType: ColumnSettingDataTypeEnum.enum,
        columnCode: 'enrollType',
        relationalPath: '',
        isFilter: true,
        isDisplay: true,
      },
    ],
    fieldType: ColumnSettingFieldTypeEnum.primary,
    filterType: ColumnSettingFilterTypeEnum.singleSelector,
    dropdownValueType: ColumnSettingDropdownValueTypeEnum.static,
    dropdownValue: [
      {
        label: 'เริ่มตามรอบที่กำหนด',
        value: 'PRE_ENROLL',
      },
      {
        label: 'เริ่มทันทีที่มอบหมาย',
        value: 'IMMEDIATE',
      },
    ],
  },
  {
    key: ColumnSettingKeyEnum.LEARNING_PATH_UPDATED_AT,
    name: ColumnSettingNameDefaultEnum.LEARNING_PATH_UPDATED_AT,
    localeKey: 'learning_path.column_setting.updated_at',
    module: ColumnSettingModuleEnum.LEARNING_PATH,
    groupFields: [
      {
        root: '',
        dataType: ColumnSettingDataTypeEnum.date,
        columnCode: 'updatedAt',
        relationalPath: null,
        isFilter: true,
        isDisplay: true,
      },
    ],
    fieldType: ColumnSettingFieldTypeEnum.primary,
    filterType: ColumnSettingFilterTypeEnum.dateRange,
    dropdownValueType: ColumnSettingDropdownValueTypeEnum.static,
    dropdownValue: [],
  },
  {
    key: ColumnSettingKeyEnum.LEARNING_PATH_CREATED_AT,
    name: ColumnSettingNameDefaultEnum.LEARNING_PATH_CREATED_AT,
    localeKey: 'learning_path.column_setting.created_at',
    module: ColumnSettingModuleEnum.LEARNING_PATH,
    groupFields: [
      {
        root: '',
        dataType: ColumnSettingDataTypeEnum.date,
        columnCode: 'createdAt',
        relationalPath: null,
        isFilter: true,
        isDisplay: true,
      },
    ],
    fieldType: ColumnSettingFieldTypeEnum.primary,
    filterType: ColumnSettingFilterTypeEnum.dateRange,
    dropdownValueType: ColumnSettingDropdownValueTypeEnum.static,
    dropdownValue: [],
  },
  {
    key: ColumnSettingKeyEnum.LEARNING_PATH_EXPIRY_DAY,
    name: ColumnSettingNameDefaultEnum.LEARNING_PATH_EXPIRY_DAY,
    localeKey: 'learning_path.column_setting.expiry_day',
    module: ColumnSettingModuleEnum.LEARNING_PATH,
    groupFields: [
      {
        root: '',
        dataType: ColumnSettingDataTypeEnum.enum,
        columnCode: 'expiryDay',
        relationalPath: 'learning-paths.id->learning-path-versions.learningPathId',
        isFilter: true,
        isDisplay: true,
      },
    ],
    fieldType: ColumnSettingFieldTypeEnum.primary,
    filterType: ColumnSettingFilterTypeEnum.singleSelector,
    dropdownValueType: ColumnSettingDropdownValueTypeEnum.static,
    dropdownValue: [
      {
        label: 'เปิดใช้งาน',
        value: true,
      },
      {
        label: 'ปิดใช้งาน',
        value: false,
      },
    ],
  },
];

const enrollmentColumnSettingList = [
  {
    key: ColumnSettingKeyEnum.ENROLLMENT_COURSE_NAME,
    name: ColumnSettingNameDefaultEnum.COURSE_NAME,
    localeKey: 'd.learning.courses',
    module: ColumnSettingModuleEnum.ENROLLMENT,
    groupFields: [
      {
        root: '',
        dataType: ColumnSettingDataTypeEnum.text,
        columnCode: 'name',
        relationalPath: 'enrollments.courseVersionId->course-versions.id',
        isFilter: true,
        isDisplay: true,
      },
    ],
    fieldType: ColumnSettingFieldTypeEnum.primary,
    dropdownValue: [],
    filterType: ColumnSettingFilterTypeEnum.text,
    dropdownValueType: ColumnSettingDropdownValueTypeEnum.static,
  },
  {
    key: ColumnSettingKeyEnum.ENROLLMENT_CUSTOMER_CODE,
    name: ColumnSettingNameDefaultEnum.CUSTOMER_CODE,
    localeKey: 'd.learning.customer_code',
    module: ColumnSettingModuleEnum.ENROLLMENT,
    groupFields: [
      {
        root: '',
        dataType: ColumnSettingDataTypeEnum.text,
        columnCode: 'customerCode',
        relationalPath: '',
        isFilter: true,
        isDisplay: true,
      },
    ],
    fieldType: ColumnSettingFieldTypeEnum.primary,
    filterType: ColumnSettingFilterTypeEnum.multipleSelector,
    dropdownValue: [],
    dropdownValueType: ColumnSettingDropdownValueTypeEnum.static,
  },
  {
    key: ColumnSettingKeyEnum.ENROLLMENT_STARTED_AT,
    name: ColumnSettingNameDefaultEnum.LEARNING_STARTED_AT,
    localeKey: 'd.learning.started_at',
    module: ColumnSettingModuleEnum.ENROLLMENT,
    groupFields: [
      {
        root: '',
        dataType: ColumnSettingDataTypeEnum.date,
        columnCode: 'startedAt',
        relationalPath: null,
        isFilter: true,
        isDisplay: true,
      },
    ],
    fieldType: ColumnSettingFieldTypeEnum.primary,
    dropdownValue: [],
    filterType: ColumnSettingFilterTypeEnum.dateRange,
    dropdownValueType: ColumnSettingDropdownValueTypeEnum.static,
  },
  {
    key: ColumnSettingKeyEnum.ENROLLMENT_PERCENT_LEARNING_PROGRESS,
    name: ColumnSettingNameDefaultEnum.LEARNING_PROGRESS,
    localeKey: 'd.learning.learning_progress',
    module: ColumnSettingModuleEnum.ENROLLMENT,
    groupFields: [
      {
        root: '',
        dataType: ColumnSettingDataTypeEnum.text,
        columnCode: 'learningProgress',
        relationalPath: null,
        isFilter: false,
        isDisplay: true,
      },
    ],
    fieldType: ColumnSettingFieldTypeEnum.primary,
    filterType: ColumnSettingFilterTypeEnum.text,
    dropdownValueType: ColumnSettingDropdownValueTypeEnum.static,
    dropdownValue: [],
  },
  {
    key: ColumnSettingKeyEnum.ENROLLMENT_USER_FULLNAME,
    name: ColumnSettingNameDefaultEnum.USER_FULLNAME,
    localeKey: 'd.learning.full_name',
    module: ColumnSettingModuleEnum.ENROLLMENT,
    groupFields: [
      {
        root: 'profile',
        dataType: ColumnSettingDataTypeEnum.text,
        columnCode: 'salute',
        relationalPath: 'enrollments.userId->users.guid',
        isFilter: false,
        isDisplay: true,
      },
      {
        root: 'profile',
        dataType: ColumnSettingDataTypeEnum.text,
        columnCode: 'firstname',
        relationalPath: 'enrollments.userId->users.guid',
        isFilter: true,
        isDisplay: true,
      },
      {
        root: 'profile',
        dataType: ColumnSettingDataTypeEnum.text,
        columnCode: 'middlename',
        relationalPath: 'enrollments.userId->users.guid',
        isFilter: false,
        isDisplay: true,
      },
      {
        root: 'profile',
        dataType: ColumnSettingDataTypeEnum.text,
        columnCode: 'lastname',
        relationalPath: 'enrollments.userId->users.guid',
        isFilter: true,
        isDisplay: true,
      },
    ],
    fieldType: ColumnSettingFieldTypeEnum.primary,
    dropdownValue: [],
    filterType: ColumnSettingFilterTypeEnum.text,
    dropdownValueType: ColumnSettingDropdownValueTypeEnum.static,
  },
  {
    key: ColumnSettingKeyEnum.ENROLLMENT_USER_EMAIL,
    name: ColumnSettingNameDefaultEnum.USER_EMAIL,
    localeKey: 'd.learning.email',
    module: ColumnSettingModuleEnum.ENROLLMENT,
    groupFields: [
      {
        root: '',
        dataType: ColumnSettingDataTypeEnum.text,
        columnCode: 'email',
        relationalPath: 'enrollments.userId->users.guid',
        isFilter: true,
        isDisplay: true,
      },
    ],
    fieldType: ColumnSettingFieldTypeEnum.primary,
    filterType: ColumnSettingFilterTypeEnum.text,
    dropdownValueType: ColumnSettingDropdownValueTypeEnum.static,
    dropdownValue: [],
  },
  {
    key: ColumnSettingKeyEnum.ENROLLMENT_USER_CITIZEN_ID,
    name: ColumnSettingNameDefaultEnum.USER_CITIZEN_ID,
    localeKey: 'd.learning.citizen_id',
    module: ColumnSettingModuleEnum.ENROLLMENT,
    groupFields: [
      {
        root: '',
        dataType: ColumnSettingDataTypeEnum.citizenId,
        columnCode: 'citizenId',
        relationalPath: 'enrollments.userId->users.guid',
        isFilter: true,
        isDisplay: true,
      },
    ],
    fieldType: ColumnSettingFieldTypeEnum.primary,
    filterType: ColumnSettingFilterTypeEnum.text,
    dropdownValueType: ColumnSettingDropdownValueTypeEnum.static,
    dropdownValue: [],
  },
  {
    key: ColumnSettingKeyEnum.ENROLLMENT_EXPIRED_AT,
    name: ColumnSettingNameDefaultEnum.LEARNING_EXPIRED_AT,
    localeKey: 'd.learning.expired_at',
    module: ColumnSettingModuleEnum.ENROLLMENT,
    groupFields: [
      {
        root: '',
        dataType: ColumnSettingDataTypeEnum.date,
        columnCode: 'expiredAt',
        relationalPath: null,
        isFilter: true,
        isDisplay: true,
      },
    ],
    fieldType: ColumnSettingFieldTypeEnum.primary,
    dropdownValue: [],
    filterType: ColumnSettingFilterTypeEnum.dateRange,
    dropdownValueType: ColumnSettingDropdownValueTypeEnum.static,
  },
  {
    key: ColumnSettingKeyEnum.ENROLLMENT_LEARNING_STATUS,
    name: ColumnSettingNameDefaultEnum.ENROLLMENT_LEARNING_STATUS,
    localeKey: 'd.learning.status',
    module: ColumnSettingModuleEnum.ENROLLMENT,
    groupFields: [
      {
        root: '',
        dataType: ColumnSettingDataTypeEnum.enum,
        columnCode: 'status',
        relationalPath: null,
        isFilter: true,
        isDisplay: true,
      },
    ],
    fieldType: ColumnSettingFieldTypeEnum.primary,
    filterType: ColumnSettingFilterTypeEnum.singleSelector,
    dropdownValueType: ColumnSettingDropdownValueTypeEnum.static,
    dropdownValue: [
      {
        value: 'NOT_STARTED',
        label: 'พร้อมเรียน',
      },
      {
        value: 'IN_PROGRESS',
        label: 'กำลังเรียน',
      },
      {
        value: 'PENDING_RESULT',
        label: 'รอประเมินผลการเรียน',
      },
      {
        value: 'PASSED',
        label: 'เรียนจบแล้ว',
      },
      {
        value: 'COMPLETED',
        label: 'สำเร็จ',
      },
      {
        value: 'EXPIRED',
        label: 'หมดเวลาเรียน',
      },
      {
        value: 'CANCELED',
        label: 'ยกเลิกการลงอบรมแล้ว',
      },
    ],
  },
  {
    key: ColumnSettingKeyEnum.ENROLLMENT_APPROVAL_AT,
    name: ColumnSettingNameDefaultEnum.APPROVAL_DATE,
    localeKey: 'd.approval.approval_date',
    module: ColumnSettingModuleEnum.ENROLLMENT,
    groupFields: [
      {
        root: '',
        dataType: ColumnSettingDataTypeEnum.date,
        columnCode: 'approvalAt',
        relationalPath: null,
        isFilter: true,
        isDisplay: true,
      },
    ],
    fieldType: ColumnSettingFieldTypeEnum.primary,
    filterType: ColumnSettingFilterTypeEnum.dateRange,
    dropdownValueType: ColumnSettingDropdownValueTypeEnum.static,
    dropdownValue: [],
  },
  {
    key: ColumnSettingKeyEnum.ENROLLMENT_REQUESTED_APPROVAL_AT,
    name: ColumnSettingNameDefaultEnum.REQUESTED_APPROVAL_DATE,
    localeKey: 'd.approval.requested_approval',
    module: ColumnSettingModuleEnum.ENROLLMENT,
    groupFields: [
      {
        root: '',
        dataType: ColumnSettingDataTypeEnum.date,
        columnCode: 'requestedApprovalAt',
        relationalPath: null,
        isFilter: true,
        isDisplay: true,
      },
    ],
    fieldType: ColumnSettingFieldTypeEnum.primary,
    filterType: ColumnSettingFilterTypeEnum.dateRange,
    dropdownValueType: ColumnSettingDropdownValueTypeEnum.static,
    dropdownValue: [],
  },
  {
    key: ColumnSettingKeyEnum.ENROLLMENT_APPROVAL_STATUS,
    name: ColumnSettingNameDefaultEnum.ENROLLMENT_APPROVAL_STATUS,
    localeKey: 'd.approval.status',
    module: ColumnSettingModuleEnum.ENROLLMENT,
    groupFields: [
      {
        root: '',
        dataType: ColumnSettingDataTypeEnum.enum,
        columnCode: 'status',
        relationalPath: null,
        isFilter: true,
        isDisplay: true,
      },
    ],
    fieldType: ColumnSettingFieldTypeEnum.primary,
    filterType: ColumnSettingFilterTypeEnum.singleSelector,
    dropdownValueType: ColumnSettingDropdownValueTypeEnum.static,
    dropdownValue: [
      {
        value: 'PENDING_APPROVAL',
        label: 'รออนุมัติ',
      },
      {
        value: 'VERIFIED',
        label: 'ผ่านการตรวจสอบ',
      },
      {
        value: 'APPROVED',
        label: 'อนุมัติ',
      },
      {
        value: 'REJECTED',
        label: 'ไม่อนุมัติ',
      },
    ],
  },
  {
    key: ColumnSettingKeyEnum.ENROLLMENT_ENROLL_TYPE,
    name: ColumnSettingNameDefaultEnum.ENROLLMENT_ENROLL_TYPE,
    localeKey: 'd.learning.enroll_type',
    module: ColumnSettingModuleEnum.ENROLLMENT,
    groupFields: [
      {
        root: '',
        dataType: ColumnSettingDataTypeEnum.enum,
        columnCode: 'enrollType',
        relationalPath: null,
        isFilter: true,
        isDisplay: true,
      },
    ],
    fieldType: ColumnSettingFieldTypeEnum.primary,
    filterType: ColumnSettingFilterTypeEnum.singleSelector,
    dropdownValueType: ColumnSettingDropdownValueTypeEnum.static,
    dropdownValue: [
      {
        value: 'VOLUNTARY',
        label: 'หลักสูตรเรียนอิสระ',
      },
      {
        value: 'COMPULSORY',
        label: 'หลักสูตรบังคับเรียน',
      },
    ],
    columnBulkHeader: 'enroll_type',
    columnBulkCode: 'enrollType',
  },
];

const learningPathEnrollmentColumnSettingList = [
  {
    key: ColumnSettingKeyEnum.LEARNING_PATH_ENROLLMENT_FULL_NAME,
    name: ColumnSettingNameDefaultEnum.USER_FULLNAME,
    localeKey: 'learning_path_enrollment.full_name',
    module: ColumnSettingModuleEnum.LEARNING_PATH_ENROLLMENT,
    groupFields: [
      {
        root: 'profile',
        dataType: ColumnSettingDataTypeEnum.text,
        columnCode: 'salute',
        relationalPath: 'learning-path-enrollments.userId->users.guid',
        isFilter: true,
        isDisplay: true,
      },
      {
        root: 'profile',
        dataType: ColumnSettingDataTypeEnum.text,
        columnCode: 'firstname',
        relationalPath: 'learning-path-enrollments.userId->users.guid',
        isFilter: true,
        isDisplay: true,
      },
      {
        root: 'profile',
        dataType: ColumnSettingDataTypeEnum.text,
        columnCode: 'middlename',
        relationalPath: 'learning-path-enrollments.userId->users.guid',
        isFilter: false,
        isDisplay: true,
      },
      {
        root: 'profile',
        dataType: ColumnSettingDataTypeEnum.text,
        columnCode: 'lastname',
        relationalPath: 'learning-path-enrollments.userId->users.guid',
        isFilter: true,
        isDisplay: true,
      },
    ],
    fieldType: ColumnSettingFieldTypeEnum.primary,
    filterType: ColumnSettingFilterTypeEnum.text,
    dropdownValueType: ColumnSettingDropdownValueTypeEnum.static,
    dropdownValue: [],
  },
  {
    key: ColumnSettingKeyEnum.LEARNING_PATH_ENROLLMENT_EMAIL,
    name: ColumnSettingNameDefaultEnum.USER_EMAIL,
    localeKey: 'learning_path_enrollment.email',
    module: ColumnSettingModuleEnum.LEARNING_PATH_ENROLLMENT,
    groupFields: [
      {
        root: '',
        dataType: ColumnSettingDataTypeEnum.text,
        columnCode: 'email',
        relationalPath: 'learning-path-enrollments.userId->users.guid',
        isFilter: true,
        isDisplay: true,
      },
    ],
    fieldType: ColumnSettingFieldTypeEnum.primary,
    filterType: ColumnSettingFilterTypeEnum.text,
    dropdownValueType: ColumnSettingDropdownValueTypeEnum.static,
    dropdownValue: [],
  },
  {
    key: ColumnSettingKeyEnum.LEARNING_PATH_ENROLLMENT_LEARNING_PATH_NAME,
    name: ColumnSettingNameDefaultEnum.LEARNING_PATH_NAME,
    localeKey: 'learning_path_enrollment.learning_path_name',
    module: ColumnSettingModuleEnum.LEARNING_PATH_ENROLLMENT,
    groupFields: [
      {
        root: '',
        dataType: ColumnSettingDataTypeEnum.text,
        columnCode: 'learningPathId',
        relationalPath: null,
        isFilter: true,
        isDisplay: true,
      },
    ],
    fieldType: ColumnSettingFieldTypeEnum.primary,
    filterType: ColumnSettingFilterTypeEnum.multipleSelector,
    dropdownValueType: ColumnSettingDropdownValueTypeEnum.static,
    dropdownValue: [],
  },
  {
    key: ColumnSettingKeyEnum.LEARNING_PATH_ENROLLMENT_STARTED_DATE,
    name: ColumnSettingNameDefaultEnum.LEARNING_STARTED_AT,
    localeKey: 'learning_path_enrollment.started_date',
    module: ColumnSettingModuleEnum.LEARNING_PATH_ENROLLMENT,
    groupFields: [
      {
        root: '',
        dataType: ColumnSettingDataTypeEnum.date,
        columnCode: 'startedAt',
        relationalPath: null,
        isFilter: true,
        isDisplay: true,
      },
    ],
    fieldType: ColumnSettingFieldTypeEnum.primary,
    filterType: ColumnSettingFilterTypeEnum.dateRange,
    dropdownValueType: ColumnSettingDropdownValueTypeEnum.static,
    dropdownValue: [],
  },
  {
    key: ColumnSettingKeyEnum.LEARNING_PATH_ENROLLMENT_EXPIRED_DATE,
    name: ColumnSettingNameDefaultEnum.LEARNING_EXPIRED_AT,
    localeKey: 'learning_path_enrollment.expired_date',
    module: ColumnSettingModuleEnum.LEARNING_PATH_ENROLLMENT,
    groupFields: [
      {
        root: '',
        dataType: ColumnSettingDataTypeEnum.date,
        columnCode: 'expiredAt',
        relationalPath: null,
        isFilter: true,
        isDisplay: true,
      },
    ],
    fieldType: ColumnSettingFieldTypeEnum.primary,
    filterType: ColumnSettingFilterTypeEnum.dateRange,
    dropdownValueType: ColumnSettingDropdownValueTypeEnum.static,
    dropdownValue: [],
  },
  {
    key: ColumnSettingKeyEnum.LEARNING_PATH_ENROLLMENT_CONTENT_PROGRESS,
    name: ColumnSettingNameDefaultEnum.LEARNING_PROGRESS,
    localeKey: 'learning_path_enrollment.content_progress',
    module: ColumnSettingModuleEnum.LEARNING_PATH_ENROLLMENT,
    groupFields: [
      {
        root: '',
        dataType: ColumnSettingDataTypeEnum.text,
        columnCode: 'contentProgress',
        relationalPath: null,
        isFilter: false,
        isDisplay: true,
      },
    ],
    fieldType: ColumnSettingFieldTypeEnum.primary,
    filterType: ColumnSettingFilterTypeEnum.text,
    dropdownValueType: ColumnSettingDropdownValueTypeEnum.static,
    dropdownValue: [],
  },
  {
    key: ColumnSettingKeyEnum.LEARNING_PATH_ENROLLMENT_STATUS,
    name: ColumnSettingNameDefaultEnum.LEARNING_PATH_ENROLLMENT_STATUS,
    localeKey: 'learning_path_enrollment.status',
    module: ColumnSettingModuleEnum.LEARNING_PATH_ENROLLMENT,
    groupFields: [
      {
        root: '',
        dataType: ColumnSettingDataTypeEnum.enum,
        columnCode: 'status',
        relationalPath: null,
        isFilter: true,
        isDisplay: true,
      },
    ],
    fieldType: ColumnSettingFieldTypeEnum.primary,
    filterType: ColumnSettingFilterTypeEnum.singleSelector,
    dropdownValueType: ColumnSettingDropdownValueTypeEnum.static,
    dropdownValue: [],
  },
  {
    key: ColumnSettingKeyEnum.LEARNING_PATH_ENROLLMENT_ENROLL_BY,
    name: ColumnSettingNameDefaultEnum.LEARNING_PATH_ENROLLMENT_ENROLL_BY,
    localeKey: 'learning_path_enrollment.enroll_by',
    module: ColumnSettingModuleEnum.LEARNING_PATH_ENROLLMENT,
    groupFields: [
      {
        root: '',
        dataType: ColumnSettingDataTypeEnum.enum,
        columnCode: 'enrollBy',
        relationalPath: null,
        isFilter: true,
        isDisplay: true,
      },
    ],
    fieldType: ColumnSettingFieldTypeEnum.primary,
    filterType: ColumnSettingFilterTypeEnum.singleSelector,
    dropdownValueType: ColumnSettingDropdownValueTypeEnum.static,
    dropdownValue: [],
  },
];

const preEnrollmentColumnSettingList = [
  {
    key: ColumnSettingKeyEnum.PRE_ENROLLMENT_ID,
    name: ColumnSettingNameDefaultEnum.PRE_ENROLLMENT_ID,
    localeKey: 'd.pre_enrollment.id',
    module: ColumnSettingModuleEnum.PRE_ENROLLMENT,
    groupFields: [
      {
        root: '',
        dataType: ColumnSettingDataTypeEnum.text,
        columnCode: 'id',
        relationalPath: null,
        isFilter: true,
        isDisplay: true,
      },
    ],
    fieldType: ColumnSettingFieldTypeEnum.primary,
    filterType: ColumnSettingFilterTypeEnum.text,
    dropdownValueType: ColumnSettingDropdownValueTypeEnum.static,
    dropdownValue: [],
    columnBulkHeader: 'Pre-Enroll ID',
    columnBulkCode: 'id',
  },
  {
    key: ColumnSettingKeyEnum.PRE_ENROLLMENT_EID,
    name: ColumnSettingNameDefaultEnum.PRE_ENROLLMENT_EID,
    localeKey: 'd.pre_enrollment.eid',
    module: ColumnSettingModuleEnum.PRE_ENROLLMENT,
    groupFields: [
      {
        root: '',
        dataType: ColumnSettingDataTypeEnum.text,
        columnCode: 'id',
        relationalPath: 'pre-enrollment-transactions.id->enrollment-regulator-reports.preEnrollmentTransactionId',
        isFilter: true,
        isDisplay: true,
      },
    ],
    fieldType: ColumnSettingFieldTypeEnum.primary,
    dropdownValue: [],
    filterType: ColumnSettingFilterTypeEnum.text,
    columnBulkHeader: 'EID',
    columnBulkCode: 'eid',
    dropdownValueType: ColumnSettingDropdownValueTypeEnum.static,
  },
  {
    key: ColumnSettingKeyEnum.PRE_ENROLLMENT_FULL_NAME,
    name: ColumnSettingNameDefaultEnum.USER_SALUTE_FIRSTNAME_LASTNAME,
    localeKey: 'd.pre_enrollment.full_name',
    module: ColumnSettingModuleEnum.PRE_ENROLLMENT,
    groupFields: [
      {
        root: 'payload',
        dataType: ColumnSettingDataTypeEnum.text,
        columnCode: 'prefix',
        relationalPath: null,
        isFilter: true,
        isDisplay: true,
      },
      {
        root: 'payload',
        dataType: ColumnSettingDataTypeEnum.text,
        columnCode: 'firstname',
        relationalPath: null,
        isFilter: true,
        isDisplay: true,
      },
      {
        root: 'payload',
        dataType: ColumnSettingDataTypeEnum.text,
        columnCode: 'lastname',
        relationalPath: null,
        isFilter: true,
        isDisplay: true,
      },
    ],
    fieldType: ColumnSettingFieldTypeEnum.primary,
    dropdownValue: [],
    filterType: ColumnSettingFilterTypeEnum.text,
    columnBulkHeader: 'Full_name*',
    columnBulkCode: 'fullName',
    dropdownValueType: ColumnSettingDropdownValueTypeEnum.static,
  },
  {
    key: ColumnSettingKeyEnum.PRE_ENROLLMENT_EMAIL,
    name: ColumnSettingNameDefaultEnum.USER_EMAIL,
    localeKey: 'd.pre_enrollment.email',
    module: ColumnSettingModuleEnum.PRE_ENROLLMENT,
    groupFields: [
      {
        root: 'payload',
        dataType: ColumnSettingDataTypeEnum.text,
        columnCode: 'email',
        relationalPath: null,
        isFilter: true,
        isDisplay: true,
      },
    ],
    fieldType: ColumnSettingFieldTypeEnum.primary,
    dropdownValue: [],
    filterType: ColumnSettingFilterTypeEnum.text,
    columnBulkHeader: 'Email*',
    columnBulkCode: 'email',
    dropdownValueType: ColumnSettingDropdownValueTypeEnum.static,
  },
  {
    key: ColumnSettingKeyEnum.PRE_ENROLLMENT_CITIZEN_ID,
    name: ColumnSettingNameDefaultEnum.USER_CITIZEN_ID,
    localeKey: 'd.pre_enrollment.citizen_id',
    module: ColumnSettingModuleEnum.PRE_ENROLLMENT,
    groupFields: [
      {
        root: 'payload',
        dataType: ColumnSettingDataTypeEnum.citizenId,
        columnCode: 'citizenId',
        relationalPath: null,
        isFilter: true,
        isDisplay: true,
      },
    ],
    fieldType: ColumnSettingFieldTypeEnum.primary,
    dropdownValue: [],
    filterType: ColumnSettingFilterTypeEnum.text,
    columnBulkHeader: 'id card*',
    columnBulkCode: 'citizenId',
    dropdownValueType: ColumnSettingDropdownValueTypeEnum.static,
  },
  {
    key: ColumnSettingKeyEnum.PRE_ENROLLMENT_MOBILE_PHONE_NUMBER,
    name: ColumnSettingNameDefaultEnum.USER_MOBILE_PHONE_NUMBER,
    localeKey: 'd.pre_enrollment.mobile_phone',
    module: ColumnSettingModuleEnum.PRE_ENROLLMENT,
    groupFields: [
      {
        root: 'payload',
        dataType: ColumnSettingDataTypeEnum.phone,
        columnCode: 'mobile',
        relationalPath: null,
        isFilter: true,
        isDisplay: true,
      },
    ],
    fieldType: ColumnSettingFieldTypeEnum.primary,
    filterType: ColumnSettingFilterTypeEnum.text,
    dropdownValue: [],
    dropdownValueType: ColumnSettingDropdownValueTypeEnum.static,
    columnBulkCode: 'mobile',
    columnBulkHeader: 'phone_no',
  },
  {
    key: ColumnSettingKeyEnum.PRE_ENROLLMENT_BUSINESS_TYPE,
    name: ColumnSettingNameDefaultEnum.BUSINESS_TYPE,
    localeKey: 'd.pre_enrollment.business_type',
    module: ColumnSettingModuleEnum.PRE_ENROLLMENT,
    groupFields: [
      {
        root: '',
        dataType: ColumnSettingDataTypeEnum.text,
        columnCode: 'businessType',
        relationalPath: '',
        isFilter: true,
        isDisplay: true,
      },
    ],
    fieldType: ColumnSettingFieldTypeEnum.primary,
    filterType: ColumnSettingFilterTypeEnum.text,
    dropdownValue: [
      {
        value: 'B2B',
        label: 'B2B',
      },
      {
        value: 'B2C',
        label: 'B2C',
      },
    ],
    dropdownValueType: ColumnSettingDropdownValueTypeEnum.static,
    columnBulkCode: 'businessType',
    columnBulkHeader: 'business_type',
  },
  {
    key: ColumnSettingKeyEnum.PRE_ENROLLMENT_CUSTOMER_CODE,
    name: ColumnSettingNameDefaultEnum.CUSTOMER_CODE,
    localeKey: 'd.pre_enrollment.customer_code',
    module: ColumnSettingModuleEnum.PRE_ENROLLMENT,
    groupFields: [
      {
        root: '',
        dataType: ColumnSettingDataTypeEnum.text,
        columnCode: 'customerCode',
        relationalPath: 'pre-enrollment-transactions.preEnrollmentReservationId->pre-enrollment-reservations.id',
        isFilter: true,
        isDisplay: true,
      },
    ],
    fieldType: ColumnSettingFieldTypeEnum.primary,
    filterType: ColumnSettingFilterTypeEnum.multipleSelector,
    dropdownValue: [],
    dropdownValueType: ColumnSettingDropdownValueTypeEnum.static,
    columnBulkCode: 'customerCode',
    columnBulkHeader: 'customer_code',
  },
  {
    key: ColumnSettingKeyEnum.PRE_ENROLLMENT_CUSTOMER_PARTNER_CODE,
    name: ColumnSettingNameDefaultEnum.CUSTOMER_PARTNER_CODE,
    localeKey: 'd.pre_enrollment.customer_partner_code',
    module: ColumnSettingModuleEnum.PRE_ENROLLMENT,
    groupFields: [
      {
        root: '',
        dataType: ColumnSettingDataTypeEnum.text,
        columnCode: 'code',
        relationalPath: 'pre-enrollment-transactions.customerPartnerId->customer-partners.id',
        isFilter: false,
        isDisplay: true,
      },
      {
        root: '',
        dataType: ColumnSettingDataTypeEnum.text,
        columnCode: 'id',
        relationalPath: 'pre-enrollment-transactions.customerPartnerId->customer-partners.id',
        isFilter: true,
        isDisplay: false,
      },
    ],
    fieldType: ColumnSettingFieldTypeEnum.primary,
    filterType: ColumnSettingFilterTypeEnum.multipleSelector,
    dropdownValue: [],
    dropdownValueType: ColumnSettingDropdownValueTypeEnum.static,
    columnBulkCode: 'customerPartnerCode',
    columnBulkHeader: 'customer_partner_code',
  },
  {
    key: ColumnSettingKeyEnum.PRE_ENROLLMENT_TSI_LICENSE_NO,
    name: ColumnSettingNameDefaultEnum.USER_TSI_LICENSE_NO,
    localeKey: 'd.pre_enrollment.tsi_license_no',
    module: ColumnSettingModuleEnum.PRE_ENROLLMENT,
    groupFields: [
      {
        root: 'payload',
        dataType: ColumnSettingDataTypeEnum.text,
        columnCode: 'tsiLicenseNo',
        relationalPath: '',
        isFilter: true,
        isDisplay: true,
      },
    ],
    fieldType: ColumnSettingFieldTypeEnum.primary,
    filterType: ColumnSettingFilterTypeEnum.text,
    dropdownValueType: ColumnSettingDropdownValueTypeEnum.static,
    dropdownValue: [],
    columnBulkCode: 'tsiLicenseNo',
    columnBulkHeader: 'tsi_license_no',
  },
  {
    key: ColumnSettingKeyEnum.PRE_ENROLLMENT_TSI_LICENSE_TYPE,
    name: ColumnSettingNameDefaultEnum.USER_TSI_LICENSE_TYPE,
    localeKey: 'd.pre_enrollment.tsi_license_type',
    module: ColumnSettingModuleEnum.PRE_ENROLLMENT,
    groupFields: [
      {
        root: 'payload',
        dataType: ColumnSettingDataTypeEnum.enum,
        columnCode: 'tsiLicenseType',
        relationalPath: '',
        isFilter: true,
        isDisplay: true,
      },
    ],
    dropdownValueType: ColumnSettingDropdownValueTypeEnum.static,
    dropdownValue: [
      {
        value: 'ผู้แนะนำการลงทุน',
        label: 'ผู้แนะนำการลงทุน',
      },
      {
        value: 'ผู้วางแผนการลงทุน',
        label: 'ผู้วางแผนการลงทุน',
      },
      {
        value: 'นักวิเคราะห์การลงทุน',
        label: 'นักวิเคราะห์การลงทุน',
      },
    ],
    fieldType: ColumnSettingFieldTypeEnum.primary,
    filterType: ColumnSettingFilterTypeEnum.multipleSelector,
    columnBulkHeader: 'tsi_license_type',
    columnBulkCode: 'tsiLicenseType',
  },
  {
    key: ColumnSettingKeyEnum.PRE_ENROLLMENT_TSI_LICENSE_START_DATE,
    name: ColumnSettingNameDefaultEnum.USER_TSI_LICENSE_START_DATE,
    localeKey: 'd.pre_enrollment.tsi_start_date',
    module: ColumnSettingModuleEnum.PRE_ENROLLMENT,
    groupFields: [
      {
        root: 'payload',
        dataType: ColumnSettingDataTypeEnum.date,
        columnCode: 'tsiStartDate',
        relationalPath: '',
        isFilter: true,
        isDisplay: true,
      },
    ],
    fieldType: ColumnSettingFieldTypeEnum.primary,
    filterType: ColumnSettingFilterTypeEnum.dateRange,
    dropdownValueType: ColumnSettingDropdownValueTypeEnum.static,
    dropdownValue: [],
    columnBulkCode: 'tsiStartDate',
    columnBulkHeader: 'tsi_startdate_(DD/MM/YYYY)',
  },
  {
    key: ColumnSettingKeyEnum.PRE_ENROLLMENT_TSI_LICENSE_END_DATE,
    name: ColumnSettingNameDefaultEnum.USER_TSI_LICENSE_END_DATE,
    localeKey: 'd.pre_enrollment.tsi_end_date',
    module: ColumnSettingModuleEnum.PRE_ENROLLMENT,
    groupFields: [
      {
        root: 'payload',
        dataType: ColumnSettingDataTypeEnum.date,
        columnCode: 'tsiEnddate',
        relationalPath: '',
        isFilter: true,
        isDisplay: true,
      },
    ],
    fieldType: ColumnSettingFieldTypeEnum.primary,
    filterType: ColumnSettingFilterTypeEnum.dateRange,
    dropdownValueType: ColumnSettingDropdownValueTypeEnum.static,
    dropdownValue: [],
    columnBulkCode: 'tsiEnddate',
    columnBulkHeader: 'tsi_enddate_(DD/MM/YYYY)',
  },
  {
    key: ColumnSettingKeyEnum.PRE_ENROLLMENT_OIC_LICENSE_NON_LIFE_NO,
    name: ColumnSettingNameDefaultEnum.USER_OIC_LICENSE_NON_LIFE_NO,
    localeKey: 'd.pre_enrollment.oic_license_non_life_no',
    module: ColumnSettingModuleEnum.PRE_ENROLLMENT,
    groupFields: [
      {
        root: 'payload',
        dataType: ColumnSettingDataTypeEnum.text,
        columnCode: 'oicLicenseNonLifeNo',
        relationalPath: '',
        isFilter: true,
        isDisplay: true,
      },
    ],
    fieldType: ColumnSettingFieldTypeEnum.primary,
    filterType: ColumnSettingFilterTypeEnum.text,
    dropdownValueType: ColumnSettingDropdownValueTypeEnum.static,
    dropdownValue: [],
    columnBulkCode: 'oicLicenseNonLifeNo',
    columnBulkHeader: 'oic_license_non_life_no',
  },
  {
    key: ColumnSettingKeyEnum.PRE_ENROLLMENT_OIC_LICENSE_NON_LIFE_START_DATE,
    name: ColumnSettingNameDefaultEnum.USER_OIC_LICENSE_NON_LIFE_START_DATE,
    localeKey: 'd.pre_enrollment.oic_non_life_start_date',
    module: ColumnSettingModuleEnum.PRE_ENROLLMENT,
    groupFields: [
      {
        root: 'payload',
        dataType: ColumnSettingDataTypeEnum.date,
        columnCode: 'oicNonLifeStartDate',
        relationalPath: '',
        isFilter: true,
        isDisplay: true,
      },
    ],
    fieldType: ColumnSettingFieldTypeEnum.primary,
    filterType: ColumnSettingFilterTypeEnum.dateRange,
    dropdownValueType: ColumnSettingDropdownValueTypeEnum.static,
    dropdownValue: [],
    columnBulkCode: 'oicNonLifeStartDate',
    columnBulkHeader: 'oic_startdate_nonlife_(DD/MM/YYYY)',
  },
  {
    key: ColumnSettingKeyEnum.PRE_ENROLLMENT_OIC_LICENSE_NON_LIFE_END_DATE,
    name: ColumnSettingNameDefaultEnum.USER_OIC_LICENSE_NON_LIFE_END_DATE,
    localeKey: 'd.pre_enrollment.oic_non_life_end_date',
    module: ColumnSettingModuleEnum.PRE_ENROLLMENT,
    groupFields: [
      {
        root: 'payload',
        dataType: ColumnSettingDataTypeEnum.date,
        columnCode: 'oicNonLifeEndDate',
        relationalPath: '',
        isFilter: true,
        isDisplay: true,
      },
    ],
    fieldType: ColumnSettingFieldTypeEnum.primary,
    filterType: ColumnSettingFilterTypeEnum.dateRange,
    dropdownValueType: ColumnSettingDropdownValueTypeEnum.static,
    dropdownValue: [],
    columnBulkCode: 'oicNonLifeEndDate',
    columnBulkHeader: 'oic_enddate_nonlife_(DD/MM/YYYY)',
  },
  {
    key: ColumnSettingKeyEnum.PRE_ENROLLMENT_OIC_LICENSE_LIFE_NO,
    name: ColumnSettingNameDefaultEnum.USER_OIC_LICENSE_LIFE_NO,
    localeKey: 'd.pre_enrollment.oic_license_life_no',
    module: ColumnSettingModuleEnum.PRE_ENROLLMENT,
    groupFields: [
      {
        root: 'payload',
        dataType: ColumnSettingDataTypeEnum.text,
        columnCode: 'oicLicenseLifeNo',
        relationalPath: '',
        isFilter: true,
        isDisplay: true,
      },
    ],
    fieldType: ColumnSettingFieldTypeEnum.primary,
    filterType: ColumnSettingFilterTypeEnum.text,
    dropdownValueType: ColumnSettingDropdownValueTypeEnum.static,
    dropdownValue: [],
    columnBulkCode: 'oicLicenseLifeNo',
    columnBulkHeader: 'oic_license_life_no',
  },
  {
    key: ColumnSettingKeyEnum.PRE_ENROLLMENT_OIC_LICENSE_LIFE_START_DATE,
    name: ColumnSettingNameDefaultEnum.USER_OIC_LICENSE_LIFE_START_DATE,
    localeKey: 'd.pre_enrollment.oic_life_start_date',
    module: ColumnSettingModuleEnum.PRE_ENROLLMENT,
    groupFields: [
      {
        root: 'payload',
        dataType: ColumnSettingDataTypeEnum.date,
        columnCode: 'oicLifeStartDate',
        relationalPath: '',
        isFilter: true,
        isDisplay: true,
      },
    ],
    fieldType: ColumnSettingFieldTypeEnum.primary,
    filterType: ColumnSettingFilterTypeEnum.dateRange,
    dropdownValueType: ColumnSettingDropdownValueTypeEnum.static,
    dropdownValue: [],
    columnBulkCode: 'oicLifeStartDate',
    columnBulkHeader: 'oic_startdate_life_(DD/MM/YYYY)',
  },
  {
    key: ColumnSettingKeyEnum.PRE_ENROLLMENT_OIC_LICENSE_LIFE_END_DATE,
    name: ColumnSettingNameDefaultEnum.USER_OIC_LICENSE_LIFE_END_DATE,
    localeKey: 'd.pre_enrollment.oic_life_end_date',
    module: ColumnSettingModuleEnum.PRE_ENROLLMENT,
    groupFields: [
      {
        root: 'payload',
        dataType: ColumnSettingDataTypeEnum.date,
        columnCode: 'oicLifeEndDate',
        relationalPath: '',
        isFilter: true,
        isDisplay: true,
      },
    ],
    fieldType: ColumnSettingFieldTypeEnum.primary,
    filterType: ColumnSettingFilterTypeEnum.dateRange,
    dropdownValueType: ColumnSettingDropdownValueTypeEnum.static,
    dropdownValue: [],
    columnBulkCode: 'oicLifeEndDate',
    columnBulkHeader: 'oic_enddate_life_(DD/MM/YYYY)',
  },
  {
    key: ColumnSettingKeyEnum.PRE_ENROLLMENT_COURSES,
    name: ColumnSettingNameDefaultEnum.COURSES,
    localeKey: 'd.pre_enrollment.courses',
    module: ColumnSettingModuleEnum.PRE_ENROLLMENT,
    groupFields: [
      {
        root: '',
        dataType: ColumnSettingDataTypeEnum.text,
        columnCode: 'name',
        relationalPath:
          'pre-enrollment-transactions.payload.courseCode->courses.code,courses.id->course-versions.courseId',
        isFilter: false,
        isDisplay: true,
      },
      {
        root: '',
        dataType: ColumnSettingDataTypeEnum.text,
        columnCode: 'code',
        relationalPath: null, //custom relationalPath
        isFilter: true,
        isDisplay: false,
      },
    ],
    fieldType: ColumnSettingFieldTypeEnum.primary,
    filterType: ColumnSettingFilterTypeEnum.multipleSelector,
    dropdownValueType: ColumnSettingDropdownValueTypeEnum.static,
    dropdownValue: [],
    columnBulkHeader: 'courses',
    columnBulkCode: 'courses',
  },
  {
    key: ColumnSettingKeyEnum.PRE_ENROLLMENT_COURSE_GROUP,
    name: ColumnSettingNameDefaultEnum.COURSE_PRODUCT_SKU,
    localeKey: 'd.pre_enrollment.course_group',
    module: ColumnSettingModuleEnum.PRE_ENROLLMENT,
    groupFields: [
      {
        root: '',
        dataType: ColumnSettingDataTypeEnum.text,
        columnCode: 'name',
        relationalPath:
          'pre-enrollment-transactions.payload.courseCode->courses.code,courses.id->course-versions.courseId',
        isFilter: false,
        isDisplay: true,
      },
      {
        root: '',
        dataType: ColumnSettingDataTypeEnum.text,
        columnCode: 'name',
        relationalPath:
          'pre-enrollment-transactions.payload.productSKUCode->product-skus.code,product-skus.id->product-sku-bundles.productSKUId',
        isFilter: false,
        isDisplay: true,
      },
      {
        root: '',
        dataType: ColumnSettingDataTypeEnum.text,
        columnCode: 'code',
        relationalPath: null, //custom relationalPath
        isFilter: true,
        isDisplay: false,
      },
    ],
    fieldType: ColumnSettingFieldTypeEnum.primary,
    filterType: ColumnSettingFilterTypeEnum.multipleSelector,
    dropdownValueType: ColumnSettingDropdownValueTypeEnum.static,
    dropdownValue: [],
    columnBulkHeader: 'courses',
    columnBulkCode: 'courses',
  },
  {
    key: ColumnSettingKeyEnum.PRE_ENROLLMENT_PRODUCT_SKU_CODE,
    name: ColumnSettingNameDefaultEnum.COURSE_PRODUCT_SKU_CODE,
    localeKey: 'd.pre_enrollment.productSKU_code',
    module: ColumnSettingModuleEnum.PRE_ENROLLMENT,
    groupFields: [
      {
        root: 'payload',
        dataType: ColumnSettingDataTypeEnum.text,
        columnCode: 'productSKUCode',
        relationalPath: null,
        isFilter: false,
        isDisplay: true,
      },
    ],
    fieldType: ColumnSettingFieldTypeEnum.primary,
    filterType: ColumnSettingFilterTypeEnum.multipleSelector,
    dropdownValue: [],
    dropdownValueType: ColumnSettingDropdownValueTypeEnum.static,
    columnBulkHeader: 'productSKU_code',
    columnBulkCode: 'productSKUCode',
  },
  {
    key: ColumnSettingKeyEnum.PRE_ENROLLMENT_PRODUCT_SKU_ID,
    name: ColumnSettingNameDefaultEnum.COURSE_PRODUCT_SKU_ID,
    localeKey: 'd.pre_enrollment.productSKU_id',
    module: ColumnSettingModuleEnum.PRE_ENROLLMENT,
    groupFields: [
      {
        root: '',
        dataType: ColumnSettingDataTypeEnum.text,
        columnCode: 'id',
        relationalPath: 'pre-enrollment-transactions.payload.productSKUCode->product-skus.code', // custom relationalPath
        isFilter: false,
        isDisplay: true,
      },
    ],
    fieldType: ColumnSettingFieldTypeEnum.primary,
    filterType: ColumnSettingFilterTypeEnum.multipleSelector,
    dropdownValue: [],
    dropdownValueType: ColumnSettingDropdownValueTypeEnum.static,
    columnBulkHeader: 'productSKUId',
    columnBulkCode: 'productSKUId',
  },
  {
    key: ColumnSettingKeyEnum.PRE_ENROLLMENT_COURSE_CODE,
    name: ColumnSettingNameDefaultEnum.COURSE_CODE,
    localeKey: 'd.pre_enrollment.course_code',
    module: ColumnSettingModuleEnum.PRE_ENROLLMENT,
    groupFields: [
      {
        root: 'payload',
        dataType: ColumnSettingDataTypeEnum.text,
        columnCode: 'courseCode',
        relationalPath: '',
        isFilter: false,
        isDisplay: true,
      },
    ],
    fieldType: ColumnSettingFieldTypeEnum.primary,
    filterType: ColumnSettingFilterTypeEnum.text,
    dropdownValue: [],
    dropdownValueType: ColumnSettingDropdownValueTypeEnum.static,
    columnBulkHeader: 'course_code',
    columnBulkCode: 'courseCode',
  },
  {
    key: ColumnSettingKeyEnum.PRE_ENROLLMENT_REGULATOR,
    name: ColumnSettingNameDefaultEnum.PRE_ENROLLMENT_REGULATOR,
    localeKey: 'd.pre_enrollment.regulator',
    module: ColumnSettingModuleEnum.PRE_ENROLLMENT,
    groupFields: [
      {
        root: 'regulatorInfo',
        dataType: ColumnSettingDataTypeEnum.text,
        columnCode: 'regulator',
        relationalPath:
          'pre-enrollment-transactions.id->enrollment-regulator-reports.preEnrollmentTransactionId,enrollment-regulator-reports.courseId->courses.id',
        isFilter: true,
        isDisplay: true,
      },
    ],
    fieldType: ColumnSettingFieldTypeEnum.primary,
    filterType: ColumnSettingFilterTypeEnum.singleSelector,
    dropdownValue: [
      {
        value: 'OIC',
        label: 'OIC',
      },
      {
        value: 'TSI',
        label: 'TSI',
      },
    ],
    dropdownValueType: ColumnSettingDropdownValueTypeEnum.static,
    columnBulkHeader: 'regulator',
    columnBulkCode: 'regulator',
  },
  {
    key: ColumnSettingKeyEnum.PRE_ENROLLMENT_TRAINING_CENTER,
    name: ColumnSettingNameDefaultEnum.COURSE_TRAINING_CENTER,
    localeKey: 'd.pre_enrollment.training_center',
    module: ColumnSettingModuleEnum.PRE_ENROLLMENT,
    groupFields: [
      {
        root: 'regulatorInfo',
        dataType: ColumnSettingDataTypeEnum.text,
        columnCode: 'trainingCenter',
        relationalPath:
          'pre-enrollment-transactions.id->enrollment-regulator-reports.preEnrollmentTransactionId,enrollment-regulator-reports.courseId->courses.id',
        isFilter: true,
        isDisplay: true,
      },
    ],
    fieldType: ColumnSettingFieldTypeEnum.primary,
    filterType: ColumnSettingFilterTypeEnum.multipleSelector,
    dropdownValue: [],
    dropdownValueType: ColumnSettingDropdownValueTypeEnum.static,
    columnBulkHeader: 'training_center',
    columnBulkCode: 'trainingCenter',
  },
  {
    key: ColumnSettingKeyEnum.PRE_ENROLLMENT_ROUND_DATE,
    name: ColumnSettingNameDefaultEnum.LEARNING_STARTED_AT,
    localeKey: 'd.pre_enrollment.round_date',
    module: ColumnSettingModuleEnum.PRE_ENROLLMENT,
    groupFields: [
      {
        root: '',
        dataType: ColumnSettingDataTypeEnum.date,
        columnCode: 'roundDate',
        relationalPath: 'pre-enrollment-transactions.roundId->rounds.id',
        isFilter: true,
        isDisplay: true,
      },
    ],
    fieldType: ColumnSettingFieldTypeEnum.primary,
    filterType: ColumnSettingFilterTypeEnum.dateRange,
    dropdownValue: [],
    dropdownValueType: ColumnSettingDropdownValueTypeEnum.static,
    columnBulkHeader: 'Training_round',
    columnBulkCode: 'roundDate',
  },
  {
    key: ColumnSettingKeyEnum.PRE_ENROLLMENT_STATUS,
    name: ColumnSettingNameDefaultEnum.PRE_ENROLLMENT_STATUS,
    localeKey: 'd.pre_enrollment.status',
    module: ColumnSettingModuleEnum.PRE_ENROLLMENT,
    groupFields: [
      {
        root: '',
        dataType: ColumnSettingDataTypeEnum.enum,
        columnCode: 'status',
        relationalPath: null,
        isFilter: true,
        isDisplay: true,
      },
    ],
    fieldType: ColumnSettingFieldTypeEnum.primary,
    filterType: ColumnSettingFilterTypeEnum.singleSelector,
    dropdownValue: [
      {
        value: 'PASSED',
        label: 'พร้อมลงทะเบียน',
      },
      {
        value: 'WAITING_VERIFY',
        label: 'รอการแก้ไข',
      },
      {
        value: 'EDITED_AFTER_VERIFY',
        label: 'แก้ไขหลังตรวจสอบ',
      },
      {
        value: 'APPLIED',
        label: 'ลงทะเบียนสำเร็จ',
      },
      {
        value: 'REJECTED',
        label: 'ยกเลิกการลงทะเบียน',
      },
      {
        value: 'FAILED_TO_APPLY',
        label: 'พบข้อผิดพลาด',
      },
    ],
    dropdownValueType: ColumnSettingDropdownValueTypeEnum.static,
    columnBulkHeader: 'status',
    columnBulkCode: 'status',
  },
  {
    key: ColumnSettingKeyEnum.PRE_ENROLLMENT_IS_CHECK_REGULATOR,
    name: ColumnSettingNameDefaultEnum.PRE_ENROLLMENT_IS_CHECK_REGULATOR,
    localeKey: 'd.pre_enrollment.is_check_regulator',
    module: ColumnSettingModuleEnum.PRE_ENROLLMENT,
    groupFields: [
      {
        root: '',
        dataType: ColumnSettingDataTypeEnum.enum,
        columnCode: 'isCheckRegulator',
        relationalPath: null,
        isFilter: true,
        isDisplay: true,
      },
    ],
    fieldType: ColumnSettingFieldTypeEnum.primary,
    filterType: ColumnSettingFilterTypeEnum.singleSelector,
    dropdownValue: [
      {
        value: false,
        label: 'ไม่ได้รับการตรวจสอบ',
      },
      {
        value: true,
        label: 'ได้รับการตรวจสอบ',
      },
    ],
    dropdownValueType: ColumnSettingDropdownValueTypeEnum.static,
    columnBulkHeader: 'is_check_regulator',
    columnBulkCode: 'isCheckRegulator',
  },
  {
    key: ColumnSettingKeyEnum.PRE_ENROLLMENT_USER_STATUS,
    name: ColumnSettingNameDefaultEnum.PRE_ENROLLMENT_USER_STATUS,
    localeKey: 'd.pre_enrollment.user_status',
    module: ColumnSettingModuleEnum.PRE_ENROLLMENT,
    groupFields: [
      {
        root: '',
        dataType: ColumnSettingDataTypeEnum.enum,
        columnCode: 'userId',
        relationalPath: null,
        isFilter: true,
        isDisplay: true,
      },
    ],
    fieldType: ColumnSettingFieldTypeEnum.primary,
    filterType: ColumnSettingFilterTypeEnum.singleSelector,
    dropdownValue: [
      {
        value: true,
        label: 'ลูกค้าเก่า',
      },
      {
        value: false,
        label: 'ลูกค้าใหม่',
      },
    ],
    dropdownValueType: ColumnSettingDropdownValueTypeEnum.static,
    columnBulkHeader: 'user_status',
    columnBulkCode: 'userId',
  },
  {
    key: ColumnSettingKeyEnum.PRE_ENROLLMENT_ENROLL_TYPE,
    name: ColumnSettingNameDefaultEnum.ENROLLMENT_ENROLL_TYPE,
    localeKey: 'd.pre_enrollment.enroll_type', //ยังไม่คิด
    module: ColumnSettingModuleEnum.PRE_ENROLLMENT,
    groupFields: [
      {
        root: '',
        dataType: ColumnSettingDataTypeEnum.enum,
        columnCode: 'enrollType',
        relationalPath: null,
        isFilter: true,
        isDisplay: true,
      },
    ],
    fieldType: ColumnSettingFieldTypeEnum.primary,
    filterType: ColumnSettingFilterTypeEnum.singleSelector,
    dropdownValueType: ColumnSettingDropdownValueTypeEnum.static,
    dropdownValue: [
      {
        value: 'VOLUNTARY',
        label: 'หลักสูตรเรียนอิสระ',
      },
      {
        value: 'COMPULSORY',
        label: 'หลักสูตรบังคับเรียน',
      },
    ],
    columnBulkHeader: 'enroll_type',
    columnBulkCode: 'enrollType',
  },
];

const enrollmentAttachmentColumnSettingList = [
  {
    key: ColumnSettingKeyEnum.ENROLLMENT_ATTACHMENT_REQUEST_NO,
    name: ColumnSettingNameDefaultEnum.ENROLLMENT_ATTACHMENT_DOC_ID,
    localeKey: 'd.additional_doc.doc_id',
    module: ColumnSettingModuleEnum.ENROLLMENT_ATTACHMENT,
    groupFields: [
      {
        root: '',
        dataType: ColumnSettingDataTypeEnum.text,
        columnCode: 'requestNo',
        relationalPath: '',
        isFilter: true,
        isDisplay: true,
      },
    ],
    fieldType: ColumnSettingFieldTypeEnum.primary,
    dropdownValue: [],
    filterType: ColumnSettingFilterTypeEnum.text,
    dropdownValueType: ColumnSettingDropdownValueTypeEnum.static,
  },
  {
    key: ColumnSettingKeyEnum.ENROLLMENT_ATTACHMENT_USER_FULLNAME,
    name: ColumnSettingNameDefaultEnum.USER_FULLNAME,
    localeKey: 'd.additional_doc.full_name',
    module: ColumnSettingModuleEnum.ENROLLMENT_ATTACHMENT,
    groupFields: [
      {
        root: 'profile',
        dataType: ColumnSettingDataTypeEnum.text,
        columnCode: 'salute',
        relationalPath: 'enrollment-attachments.enrollmentId->enrollments.id,enrollments.userId->users.guid',
        isFilter: false,
        isDisplay: true,
      },
      {
        root: 'profile',
        dataType: ColumnSettingDataTypeEnum.text,
        columnCode: 'firstname',
        relationalPath: 'enrollment-attachments.enrollmentId->enrollments.id,enrollments.userId->users.guid',
        isFilter: true,
        isDisplay: true,
      },
      {
        root: 'profile',
        dataType: ColumnSettingDataTypeEnum.text,
        columnCode: 'middlename',
        relationalPath: 'enrollment-attachments.enrollmentId->enrollments.id,enrollments.userId->users.guid',
        isFilter: false,
        isDisplay: true,
      },
      {
        root: 'profile',
        dataType: ColumnSettingDataTypeEnum.text,
        columnCode: 'lastname',
        relationalPath: 'enrollment-attachments.enrollmentId->enrollments.id,enrollments.userId->users.guid',
        isFilter: true,
        isDisplay: true,
      },
    ],
    fieldType: ColumnSettingFieldTypeEnum.primary,
    dropdownValue: [],
    filterType: ColumnSettingFilterTypeEnum.text,
    dropdownValueType: ColumnSettingDropdownValueTypeEnum.static,
  },
  {
    key: ColumnSettingKeyEnum.ENROLLMENT_ATTACHMENT_USER_EMAIL,
    name: ColumnSettingNameDefaultEnum.USER_EMAIL,
    localeKey: 'd.additional_doc.email',
    module: ColumnSettingModuleEnum.ENROLLMENT_ATTACHMENT,
    groupFields: [
      {
        root: '',
        dataType: ColumnSettingDataTypeEnum.text,
        columnCode: 'email',
        relationalPath: 'enrollment-attachments.enrollmentId->enrollments.id,enrollments.userId->users.guid',
        isFilter: true,
        isDisplay: true,
      },
    ],
    fieldType: ColumnSettingFieldTypeEnum.primary,
    filterType: ColumnSettingFilterTypeEnum.text,
    dropdownValue: [],
    dropdownValueType: ColumnSettingDropdownValueTypeEnum.static,
  },
  {
    key: ColumnSettingKeyEnum.ENROLLMENT_ATTACHMENT_USER_CITIZEN_ID,
    name: ColumnSettingNameDefaultEnum.USER_CITIZEN_ID,
    localeKey: 'd.additional_doc.citizen_id',
    module: ColumnSettingModuleEnum.ENROLLMENT_ATTACHMENT,
    groupFields: [
      {
        root: '',
        dataType: ColumnSettingDataTypeEnum.citizenId,
        columnCode: 'citizenId',
        relationalPath: 'enrollment-attachments.enrollmentId->enrollments.id,enrollments.userId->users.guid',
        isFilter: true,
        isDisplay: true,
      },
    ],
    fieldType: ColumnSettingFieldTypeEnum.primary,
    filterType: ColumnSettingFilterTypeEnum.text,
    dropdownValue: [],
    dropdownValueType: ColumnSettingDropdownValueTypeEnum.static,
  },
  {
    key: ColumnSettingKeyEnum.ENROLLMENT_ATTACHMENT_COURSE_NAME,
    name: ColumnSettingNameDefaultEnum.COURSE_NAME,
    localeKey: 'd.additional_doc.courses',
    module: ColumnSettingModuleEnum.ENROLLMENT_ATTACHMENT,
    groupFields: [
      {
        root: '',
        dataType: ColumnSettingDataTypeEnum.text,
        columnCode: 'name',
        relationalPath:
          'enrollment-attachments.enrollmentId->enrollments.id,enrollments.courseVersionId->course-versions.id',
        isFilter: true,
        isDisplay: true,
      },
    ],
    fieldType: ColumnSettingFieldTypeEnum.primary,
    dropdownValue: [],
    filterType: ColumnSettingFilterTypeEnum.text,
    dropdownValueType: ColumnSettingDropdownValueTypeEnum.static,
  },
  {
    key: ColumnSettingKeyEnum.ENROLLMENT_ATTACHMENT_CREATED_AT,
    name: ColumnSettingNameDefaultEnum.ENROLLMENT_ATTACHMENT_DOC_REQUEST_DATE,
    localeKey: 'd.additional_doc.request_date',
    module: ColumnSettingModuleEnum.ENROLLMENT_ATTACHMENT,
    groupFields: [
      {
        root: '',
        dataType: ColumnSettingDataTypeEnum.date,
        columnCode: 'createdAt',
        relationalPath: '',
        isFilter: true,
        isDisplay: true,
      },
    ],
    fieldType: ColumnSettingFieldTypeEnum.primary,
    dropdownValue: [],
    filterType: ColumnSettingFilterTypeEnum.dateRange,
    dropdownValueType: ColumnSettingDropdownValueTypeEnum.static,
  },
  {
    key: ColumnSettingKeyEnum.ENROLLMENT_ATTACHMENT_EXPIRED_AT,
    name: ColumnSettingNameDefaultEnum.ENROLLMENT_ATTACHMENT_DOC_DUE_DATE,
    localeKey: 'd.additional_doc.due_date',
    module: ColumnSettingModuleEnum.ENROLLMENT_ATTACHMENT,
    groupFields: [
      {
        root: 'payload',
        dataType: ColumnSettingDataTypeEnum.date,
        columnCode: 'expiredAt',
        relationalPath: '',
        isFilter: true,
        isDisplay: true,
      },
    ],
    fieldType: ColumnSettingFieldTypeEnum.primary,
    dropdownValue: [],
    filterType: ColumnSettingFilterTypeEnum.dateRange,
    dropdownValueType: ColumnSettingDropdownValueTypeEnum.static,
  },
  {
    key: ColumnSettingKeyEnum.ENROLLMENT_ATTACHMENT_ADDITIONAL_DOC_STATUS,
    name: ColumnSettingNameDefaultEnum.ENROLLMENT_ATTACHMENT_ADDITIONAL_DOC_STATUS,
    localeKey: 'd.additional_doc.status',
    module: ColumnSettingModuleEnum.ENROLLMENT_ATTACHMENT,
    groupFields: [
      {
        root: '',
        dataType: ColumnSettingDataTypeEnum.enum,
        columnCode: 'status',
        relationalPath: '',
        isFilter: true,
        isDisplay: true,
      },
    ],
    fieldType: ColumnSettingFieldTypeEnum.primary,
    filterType: ColumnSettingFilterTypeEnum.text,
    dropdownValueType: ColumnSettingDropdownValueTypeEnum.static,
    dropdownValue: [
      {
        value: 'NOT_SENT',
        label: 'ยังไม่ส่งเอกสาร',
      },
      {
        value: 'PENDING_APPROVAL',
        label: 'รอตรวจสอบ',
      },
      {
        value: 'APPROVED',
        label: 'อนุมัติเอกสาร',
      },
      {
        value: 'REJECTED',
        label: 'ไม่อนุมัติเอกสาร',
      },
      {
        value: 'CANCELED',
        label: 'ยกเลิกเอกสาร',
      },
    ],
  },
  {
    key: ColumnSettingKeyEnum.ENROLLMENT_ATTACHMENT_ENROLLMENT_CUSTOMER_CODE,
    name: ColumnSettingNameDefaultEnum.CUSTOMER_CODE,
    localeKey: 'd.deduct_doc.customer_code',
    module: ColumnSettingModuleEnum.ENROLLMENT_ATTACHMENT,
    groupFields: [
      {
        root: '',
        dataType: ColumnSettingDataTypeEnum.text,
        columnCode: 'customerCode',
        relationalPath: 'enrollment-attachments.enrollmentId->enrollments.id',
        isFilter: true,
        isDisplay: true,
      },
    ],
    fieldType: ColumnSettingFieldTypeEnum.primary,
    dropdownValue: [],
    filterType: ColumnSettingFilterTypeEnum.text,
    dropdownValueType: ColumnSettingDropdownValueTypeEnum.static,
  },
  {
    key: ColumnSettingKeyEnum.ENROLLMENT_ATTACHMENT_APPROVAL_DATE,
    name: ColumnSettingNameDefaultEnum.ENROLLMENT_ATTACHMENT_DOC_APPROVAL_DATE,
    localeKey: 'd.deduct_doc.approval_date',
    module: ColumnSettingModuleEnum.ENROLLMENT_ATTACHMENT,
    groupFields: [
      {
        root: '',
        dataType: ColumnSettingDataTypeEnum.date,
        columnCode: 'approvalDate',
        relationalPath: '',
        isFilter: true,
        isDisplay: true,
      },
    ],
    fieldType: ColumnSettingFieldTypeEnum.primary,
    filterType: ColumnSettingFilterTypeEnum.dateRange,
    dropdownValueType: ColumnSettingDropdownValueTypeEnum.static,
    dropdownValue: [],
  },
  {
    key: ColumnSettingKeyEnum.ENROLLMENT_ATTACHMENT_STARTED_AT,
    name: ColumnSettingNameDefaultEnum.LEARNING_STARTED_AT,
    localeKey: 'd.deduct_doc.started_at',
    module: ColumnSettingModuleEnum.ENROLLMENT_ATTACHMENT,
    groupFields: [
      {
        root: '',
        dataType: ColumnSettingDataTypeEnum.date,
        columnCode: 'startedAt',
        relationalPath: 'enrollment-attachments.enrollmentId->enrollments.id',
        isFilter: true,
        isDisplay: true,
      },
    ],
    fieldType: ColumnSettingFieldTypeEnum.primary,
    filterType: ColumnSettingFilterTypeEnum.dateRange,
    dropdownValueType: ColumnSettingDropdownValueTypeEnum.static,
    dropdownValue: [],
  },
  {
    key: ColumnSettingKeyEnum.ENROLLMENT_ATTACHMENT_LICENSE_OIC_LICENSE_LIFE_NO,
    name: ColumnSettingNameDefaultEnum.USER_OIC_LICENSE_LIFE_NO,
    localeKey: 'd.deduct_doc.oic_license_life_no',
    module: ColumnSettingModuleEnum.ENROLLMENT_ATTACHMENT,
    groupFields: [
      {
        root: '',
        dataType: ColumnSettingDataTypeEnum.text,
        columnCode: 'licenseNo',
        relationalPath: 'enrollment-attachments.enrollmentId->enrollments.id,enrollments.userId->licenses.userId',
        isFilter: true,
        isDisplay: true,
        displayConditions: [
          {
            columnCode: 'licenseTypeCode',
            value: 'OIC-002',
          },
        ],
      },
      {
        root: '',
        dataType: ColumnSettingDataTypeEnum.text,
        columnCode: 'licenseTypeCode',
        relationalPath: 'enrollment-attachments.enrollmentId->enrollments.id,enrollments.userId->licenses.userId',
        isDefault: true,
        defaultValue: 'OIC-002',
        isFilter: true,
        isDisplay: false,
        filterType: ColumnSettingFilterTypeEnum.text,
      },
    ],
    fieldType: ColumnSettingFieldTypeEnum.primary,
    filterType: ColumnSettingFilterTypeEnum.text,
    dropdownValueType: ColumnSettingDropdownValueTypeEnum.static,
    dropdownValue: [],
  },
  {
    key: ColumnSettingKeyEnum.ENROLLMENT_ATTACHMENT_LICENSE_OIC_LICENSE_NON_LIFE_NO,
    name: ColumnSettingNameDefaultEnum.USER_OIC_LICENSE_NON_LIFE_NO,
    localeKey: 'd.deduct_doc.oic_license_non_life_no',
    module: ColumnSettingModuleEnum.ENROLLMENT_ATTACHMENT,
    groupFields: [
      {
        root: '',
        dataType: ColumnSettingDataTypeEnum.text,
        columnCode: 'licenseNo',
        relationalPath: 'enrollment-attachments.enrollmentId->enrollments.id,enrollments.userId->licenses.userId',
        displayConditions: [
          {
            columnCode: 'licenseTypeCode',
            value: 'OIC-001',
          },
        ],
        isFilter: true,
        isDisplay: true,
      },
      {
        root: '',
        dataType: ColumnSettingDataTypeEnum.text,
        columnCode: 'licenseTypeCode',
        relationalPath: 'enrollment-attachments.enrollmentId->enrollments.id,enrollments.userId->licenses.userId',
        isDefault: true,
        defaultValue: 'OIC-001',
        isFilter: true,
        isDisplay: false,
        filterType: ColumnSettingFilterTypeEnum.text,
      },
    ],
    fieldType: ColumnSettingFieldTypeEnum.primary,
    filterType: ColumnSettingFilterTypeEnum.text,
    dropdownValueType: ColumnSettingDropdownValueTypeEnum.static,
    dropdownValue: [],
  },
  {
    key: ColumnSettingKeyEnum.ENROLLMENT_ATTACHMENT_SENT_DATE,
    name: ColumnSettingNameDefaultEnum.ENROLLMENT_ATTACHMENT_DOC_SUBMISSION_DATE,
    localeKey: 'd.deduct_doc.submission_date',
    module: ColumnSettingModuleEnum.ENROLLMENT_ATTACHMENT,
    groupFields: [
      {
        root: '',
        dataType: ColumnSettingDataTypeEnum.date,
        columnCode: 'sentDate',
        relationalPath: '',
        isFilter: true,
        isDisplay: true,
      },
    ],
    fieldType: ColumnSettingFieldTypeEnum.primary,
    filterType: ColumnSettingFilterTypeEnum.dateRange,
    dropdownValueType: ColumnSettingDropdownValueTypeEnum.static,
    dropdownValue: [],
  },
  {
    key: ColumnSettingKeyEnum.ENROLLMENT_ATTACHMENT_DEDUCT_DOC_STATUS,
    name: ColumnSettingNameDefaultEnum.ENROLLMENT_ATTACHMENT_DEDUCT_DOC_STATUS,
    localeKey: 'd.deduct_doc.status',
    module: ColumnSettingModuleEnum.ENROLLMENT_ATTACHMENT,
    groupFields: [
      {
        root: '',
        dataType: ColumnSettingDataTypeEnum.enum,
        columnCode: 'status',
        relationalPath: '',
        isFilter: true,
        isDisplay: true,
      },
    ],
    fieldType: ColumnSettingFieldTypeEnum.primary,
    filterType: ColumnSettingFilterTypeEnum.text,
    dropdownValueType: ColumnSettingDropdownValueTypeEnum.static,
    dropdownValue: [
      {
        value: 'NOT_SENT',
        label: 'ยังไม่ส่งเอกสาร',
      },
      {
        value: 'PENDING_APPROVAL',
        label: 'รอตรวจสอบ',
      },
      {
        value: 'APPROVED',
        label: 'อนุมัติเอกสาร',
      },
      {
        value: 'REJECTED',
        label: 'ไม่อนุมัติเอกสาร',
      },
    ],
  },
];

const knowledgeContentItemColumnSettingList = [
  {
    key: ColumnSettingKeyEnum.KNOWLEDGE_CONTENT_ITEM_CODE,
    name: ColumnSettingNameDefaultEnum.KNOWLEDGE_CONTENT_ITEM_CODE,
    localeKey: 'd.knowledge_content_item.code',
    module: ColumnSettingModuleEnum.KNOWLEDGE_CONTENT_ITEM,
    groupFields: [
      {
        root: '',
        dataType: ColumnSettingDataTypeEnum.text,
        columnCode: 'code',
        relationalPath: null,
        isFilter: true,
        isDisplay: true,
      },
    ],
    fieldType: ColumnSettingFieldTypeEnum.primary,
    filterType: ColumnSettingFilterTypeEnum.text,
    dropdownValue: [],
    dropdownValueType: ColumnSettingDropdownValueTypeEnum.sync,
  },
  {
    key: ColumnSettingKeyEnum.KNOWLEDGE_CONTENT_ITEM_THUMBNAIL,
    name: ColumnSettingNameDefaultEnum.KNOWLEDGE_CONTENT_ITEM_THUMBNAIL,
    localeKey: 'd.knowledge_content_item.thumbnail',
    module: ColumnSettingModuleEnum.KNOWLEDGE_CONTENT_ITEM,
    groupFields: [
      {
        root: '',
        dataType: ColumnSettingDataTypeEnum.imageUrl,
        columnCode: 'thumbnailUrl',
        relationalPath: null,
        isFilter: false,
        isDisplay: true,
      },
    ],
    fieldType: ColumnSettingFieldTypeEnum.primary,
    dropdownValue: [],
    filterType: ColumnSettingFilterTypeEnum.text,
    dropdownValueType: ColumnSettingDropdownValueTypeEnum.static,
  },
  {
    key: ColumnSettingKeyEnum.KNOWLEDGE_CONTENT_ITEM_TITLE,
    name: ColumnSettingNameDefaultEnum.KNOWLEDGE_CONTENT_ITEM_TITLE,
    localeKey: 'd.knowledge_content_item.title',
    module: ColumnSettingModuleEnum.KNOWLEDGE_CONTENT_ITEM,
    groupFields: [
      {
        root: '',
        dataType: ColumnSettingDataTypeEnum.text,
        columnCode: 'title',
        relationalPath: null,
        isFilter: true,
        isDisplay: true,
      },
    ],
    fieldType: ColumnSettingFieldTypeEnum.primary,
    dropdownValue: [],
    filterType: ColumnSettingFilterTypeEnum.text,
    dropdownValueType: ColumnSettingDropdownValueTypeEnum.static,
  },
  {
    key: ColumnSettingKeyEnum.KNOWLEDGE_CONTENT_ITEM_TYPE,
    name: ColumnSettingNameDefaultEnum.KNOWLEDGE_CONTENT_ITEM_TYPE,
    localeKey: 'd.knowledge_content_item.type',
    module: ColumnSettingModuleEnum.KNOWLEDGE_CONTENT_ITEM,
    groupFields: [
      {
        root: '',
        dataType: ColumnSettingDataTypeEnum.enum,
        columnCode: 'type',
        relationalPath: null,
        isFilter: true,
        isDisplay: true,
      },
    ],
    fieldType: ColumnSettingFieldTypeEnum.primary,
    dropdownValue: [
      {
        value: 'video',
        label: 'วิดีโอ',
      },
      {
        value: 'podcast',
        label: 'พอดแคสต์',
      },
      {
        value: 'article',
        label: 'บทความ',
      },
      {
        value: 'ebook',
        label: 'อีบุ๊ก',
      },
    ],
    filterType: ColumnSettingFilterTypeEnum.multipleSelector,
    dropdownValueType: ColumnSettingDropdownValueTypeEnum.static,
  },
  {
    key: ColumnSettingKeyEnum.KNOWLEDGE_CONTENT_ITEM_CATEGORY_NAME,
    name: ColumnSettingNameDefaultEnum.KNOWLEDGE_CONTENT_ITEM_CATEGORY_NAME,
    localeKey: 'd.knowledge_content_item.category_name',
    module: ColumnSettingModuleEnum.KNOWLEDGE_CONTENT_ITEM,
    groupFields: [
      {
        root: '',
        dataType: ColumnSettingDataTypeEnum.text,
        columnCode: 'title',
        relationalPath: 'knowledge-content-items.id->knowledge-content-categories.knowledgeContentItemIds',
        isFilter: false,
        isDisplay: true,
      },
      {
        root: '',
        dataType: ColumnSettingDataTypeEnum.text,
        columnCode: 'id',
        relationalPath: 'knowledge-content-items.id->knowledge-content-categories.knowledgeContentItemIds',
        isFilter: true,
        isDisplay: false,
      },
    ],
    fieldType: ColumnSettingFieldTypeEnum.primary,
    dropdownValue: [],
    filterType: ColumnSettingFilterTypeEnum.multipleSelector,
    dropdownValueType: ColumnSettingDropdownValueTypeEnum.static,
  },
  {
    key: ColumnSettingKeyEnum.KNOWLEDGE_CONTENT_ITEM_INSTRUCTOR_NAME,
    name: ColumnSettingNameDefaultEnum.INSTRUCTOR,
    localeKey: 'd.knowledge_content_item.instructor',
    module: ColumnSettingModuleEnum.KNOWLEDGE_CONTENT_ITEM,
    groupFields: [
      {
        root: '',
        dataType: ColumnSettingDataTypeEnum.text,
        columnCode: 'firstname',
        relationalPath: 'knowledge-content-items.instructorIds->instructors.id',
        isFilter: false,
        isDisplay: true,
      },
      {
        root: '',
        dataType: ColumnSettingDataTypeEnum.text,
        columnCode: 'lastname',
        relationalPath: 'knowledge-content-items.instructorIds->instructors.id',
        isFilter: false,
        isDisplay: true,
      },
      {
        root: '',
        dataType: ColumnSettingDataTypeEnum.text,
        columnCode: 'id',
        relationalPath: 'knowledge-content-items.instructorIds->instructors.id',
        isFilter: true,
        isDisplay: false,
      },
    ],
    fieldType: ColumnSettingFieldTypeEnum.primary,
    dropdownValue: [],
    filterType: ColumnSettingFilterTypeEnum.multipleSelector,
    dropdownValueType: ColumnSettingDropdownValueTypeEnum.sync,
  },
  {
    key: ColumnSettingKeyEnum.KNOWLEDGE_CONTENT_ITEM_STATUS,
    name: ColumnSettingNameDefaultEnum.KNOWLEDGE_CONTENT_ITEM_STATUS,
    localeKey: 'd.knowledge_content_item.status',
    module: ColumnSettingModuleEnum.KNOWLEDGE_CONTENT_ITEM,
    groupFields: [
      {
        root: '',
        dataType: ColumnSettingDataTypeEnum.enum,
        columnCode: 'status',
        relationalPath: null,
        isFilter: true,
        isDisplay: true,
      },
    ],
    fieldType: ColumnSettingFieldTypeEnum.primary,
    dropdownValue: [
      {
        value: 'DRAFT',
        label: 'แบบร่าง',
      },
      {
        value: 'SCHEDULED',
        label: 'รอการเผยแพร่',
      },
      {
        value: 'PUBLISHED',
        label: 'เผยแพร่',
      },
      {
        value: 'UNPUBLISHED',
        label: 'ระงับการเผยแพร่',
      },
      {
        value: 'EXPIRED',
        label: 'หมดอายุ',
      },
    ],
    filterType: ColumnSettingFilterTypeEnum.singleSelector,
    dropdownValueType: ColumnSettingDropdownValueTypeEnum.static,
  },
  {
    key: ColumnSettingKeyEnum.KNOWLEDGE_CONTENT_ITEM_UPDATED_AT,
    name: ColumnSettingNameDefaultEnum.KNOWLEDGE_CONTENT_ITEM_UPDATED_AT,
    localeKey: 'd.knowledge_content_item.updatedAt',
    module: ColumnSettingModuleEnum.KNOWLEDGE_CONTENT_ITEM,
    groupFields: [
      {
        root: '',
        dataType: ColumnSettingDataTypeEnum.date,
        columnCode: 'updatedAt',
        relationalPath: null,
        isFilter: true,
        isDisplay: true,
      },
    ],
    fieldType: ColumnSettingFieldTypeEnum.primary,
    dropdownValue: [],
    filterType: ColumnSettingFilterTypeEnum.dateRange,
    dropdownValueType: ColumnSettingDropdownValueTypeEnum.static,
  },
  {
    key: ColumnSettingKeyEnum.KNOWLEDGE_CONTENT_ITEM_PUBLISHED_START_AT,
    name: ColumnSettingNameDefaultEnum.KNOWLEDGE_CONTENT_ITEM_PUBLISHED_START_AT,
    localeKey: 'd.knowledge_content_item.publishedStartAt',
    module: ColumnSettingModuleEnum.KNOWLEDGE_CONTENT_ITEM,
    groupFields: [
      {
        root: '',
        dataType: ColumnSettingDataTypeEnum.date,
        columnCode: 'publishedStartAt',
        relationalPath: null,
        isFilter: true,
        isDisplay: true,
      },
    ],
    fieldType: ColumnSettingFieldTypeEnum.primary,
    dropdownValue: [],
    filterType: ColumnSettingFilterTypeEnum.dateRange,
    dropdownValueType: ColumnSettingDropdownValueTypeEnum.static,
  },
  {
    key: ColumnSettingKeyEnum.KNOWLEDGE_CONTENT_ITEM_PUBLISHED_END_AT,
    name: ColumnSettingNameDefaultEnum.KNOWLEDGE_CONTENT_ITEM_PUBLISHED_END_AT,
    localeKey: 'd.knowledge_content_item.publishedEndAt',
    module: ColumnSettingModuleEnum.KNOWLEDGE_CONTENT_ITEM,
    groupFields: [
      {
        root: '',
        dataType: ColumnSettingDataTypeEnum.date,
        columnCode: 'publishedEndAt',
        relationalPath: null,
        isFilter: true,
        isDisplay: true,
      },
    ],
    fieldType: ColumnSettingFieldTypeEnum.primary,
    dropdownValue: [],
    filterType: ColumnSettingFilterTypeEnum.dateRange,
    dropdownValueType: ColumnSettingDropdownValueTypeEnum.static,
  },
  {
    key: ColumnSettingKeyEnum.KNOWLEDGE_CONTENT_ITEM_IS_DOWNLOAD_ENABLED,
    name: ColumnSettingNameDefaultEnum.KNOWLEDGE_CONTENT_ITEM_IS_DOWNLOAD_ENABLED,
    localeKey: 'd.knowledge_content_item.isDownloadEnabled',
    module: ColumnSettingModuleEnum.KNOWLEDGE_CONTENT_ITEM,
    groupFields: [
      {
        root: '',
        dataType: ColumnSettingDataTypeEnum.flag,
        columnCode: 'isDownloadEnabled',
        relationalPath: null,
        isFilter: true,
        isDisplay: true,
      },
    ],
    fieldType: ColumnSettingFieldTypeEnum.primary,
    dropdownValue: [
      {
        value: true,
        label: 'เปิดใช้งาน',
      },
      {
        value: false,
        label: 'ปิดใช้งาน',
      },
    ],
    filterType: ColumnSettingFilterTypeEnum.singleSelector,
    dropdownValueType: ColumnSettingDropdownValueTypeEnum.static,
  },
  {
    key: ColumnSettingKeyEnum.KNOWLEDGE_CONTENT_ITEM_DASHBOARD_TITLE,
    name: ColumnSettingNameDefaultEnum.KNOWLEDGE_CONTENT_ITEM_TITLE,
    localeKey: 'd.knowledge_content_item.title',
    module: ColumnSettingModuleEnum.KNOWLEDGE_CONTENT_ITEM,
    groupFields: [
      {
        root: '',
        dataType: ColumnSettingDataTypeEnum.text,
        columnCode: 'id',
        relationalPath: null,
        isFilter: true,
        isDisplay: false,
      },
    ],
    dropdownValue: [],
    dropdownValueType: ColumnSettingDropdownValueTypeEnum.sync,
    fieldType: ColumnSettingFieldTypeEnum.primary,
    filterType: ColumnSettingFilterTypeEnum.multipleSelector,
  },
];

const classroomColumnSettingList = [
  {
    key: ColumnSettingKeyEnum.CLASSROOM_TRANSFER_ENROLLMENT_STATUS,
    name: ColumnSettingNameDefaultEnum.CLASSROOM_ENROLLMENT_STATUS,
    localeKey: 'd.classroom.enrollment_status',
    module: ColumnSettingModuleEnum.CLASSROOM_ENROLLMENT,
    groupFields: [
      {
        root: '',
        dataType: ColumnSettingDataTypeEnum.text,
        columnCode: 'startedAt',
        relationalPath:
          'users.guid->classroom-location-enrollments.userId,classroom-location-enrollments.classroomRoundId->classroom-rounds.id',
        isFilter: false,
        isDisplay: false,
      },
      {
        root: '',
        dataType: ColumnSettingDataTypeEnum.text,
        columnCode: 'expiredAt',
        relationalPath:
          'users.guid->classroom-location-enrollments.userId,classroom-location-enrollments.classroomRoundId->classroom-rounds.id',
        isFilter: false,
        isDisplay: false,
      },
      {
        root: '',
        dataType: ColumnSettingDataTypeEnum.enum,
        columnCode: 'status',
        relationalPath: 'users.guid->classroom-location-enrollments.userId',
        isFilter: false,
        isDisplay: true,
      },
    ],
    fieldType: ColumnSettingFieldTypeEnum.primary,
    filterType: ColumnSettingFilterTypeEnum.singleSelector,
    dropdownValue: [
      {
        value: 'NOT_REGISTERED',
        label: 'ยังไม่ได้สมัคร',
      },
      {
        value: 'PRE_ENROLL',
        label: 'รอเริ่มรอบการเรียน',
      },
      {
        value: 'WAITING_PRE_ENROLL',
        label: 'สำรองรายชื่อผู้รอเรียน',
      },
      {
        value: 'NOT_PASS',
        label: 'ไม่ผ่าน',
      },
      {
        value: 'CANCELED',
        label: 'ยกเลิกการเรียน',
      },
    ],
    dropdownValueType: ColumnSettingDropdownValueTypeEnum.static,
  },
  {
    key: ColumnSettingKeyEnum.CLASSROOM_ROUND_DATE,
    name: ColumnSettingNameDefaultEnum.CLASSROOM_ROUND_DATE,
    localeKey: 'd.classroom.round_date',
    module: ColumnSettingModuleEnum.CLASSROOM_ENROLLMENT,
    groupFields: [
      {
        root: '',
        dataType: ColumnSettingDataTypeEnum.text,
        columnCode: 'roundDate',
        relationalPath:
          'users.guid->classroom-location-enrollments.userId,classroom-location-enrollments.classroomRoundId->classroom-rounds.id',
        isFilter: false,
        isDisplay: true,
      },
    ],
    fieldType: ColumnSettingFieldTypeEnum.primary,
    filterType: ColumnSettingFilterTypeEnum.text,
    dropdownValue: [],
    dropdownValueType: ColumnSettingDropdownValueTypeEnum.static,
  },
  {
    key: ColumnSettingKeyEnum.CLASSROOM_LOCATION,
    name: ColumnSettingNameDefaultEnum.CLASSROOM_LOCATION,
    localeKey: 'd.classroom.location',
    module: ColumnSettingModuleEnum.CLASSROOM_ENROLLMENT,
    groupFields: [
      {
        root: '',
        dataType: ColumnSettingDataTypeEnum.text,
        columnCode: 'location',
        relationalPath:
          'users.guid->classroom-location-enrollments.userId,classroom-location-enrollments.classroomLocationId->classroom-locations.id',
        isFilter: false,
        isDisplay: true,
      },
    ],
    fieldType: ColumnSettingFieldTypeEnum.primary,
    filterType: ColumnSettingFilterTypeEnum.text,
    dropdownValue: [],
    dropdownValueType: ColumnSettingDropdownValueTypeEnum.static,
  },
  {
    key: ColumnSettingKeyEnum.CLASSROOM_ENROLLMENT_STATUS,
    name: ColumnSettingNameDefaultEnum.CLASSROOM_ENROLLMENT_STATUS,
    localeKey: 'd.classroom.enrollment_status',
    module: ColumnSettingModuleEnum.CLASSROOM_ENROLLMENT,
    groupFields: [
      {
        root: '',
        dataType: ColumnSettingDataTypeEnum.enum,
        columnCode: 'status',
        relationalPath: 'users.guid->classroom-location-enrollments.userId',
        isFilter: false,
        isDisplay: true,
      },
      {
        root: '',
        dataType: ColumnSettingDataTypeEnum.text,
        columnCode: 'startedAt',
        relationalPath:
          'users.guid->classroom-location-enrollments.userId,classroom-location-enrollments.classroomRoundId->classroom-rounds.id',
        isFilter: false,
        isDisplay: false,
        isDefault: false,
        defaultValue: null,
        displayConditions: [],
        filterType: null,
      },
      {
        root: '',
        dataType: ColumnSettingDataTypeEnum.text,
        columnCode: 'expiredAt',
        relationalPath:
          'users.guid->classroom-location-enrollments.userId,classroom-location-enrollments.classroomRoundId->classroom-rounds.id',
        isFilter: false,
        isDisplay: false,
        isDefault: false,
        defaultValue: null,
        displayConditions: [],
        filterType: null,
      },
    ],
    fieldType: ColumnSettingFieldTypeEnum.primary,
    filterType: ColumnSettingFilterTypeEnum.singleSelector,
    dropdownValue: [
      {
        value: 'PRE_ENROLL',
        label: 'รอเริ่มรอบการเรียน',
      },
      {
        value: 'WAITING_PRE_ENROLL',
        label: 'สำรองรายชื่อผู้รอเรียน',
      },
      {
        value: 'IN_PROGRESS',
        label: 'กำลังเรียน',
      },
      {
        value: 'PENDING_RESULT',
        label: 'รอประเมินผลการเรียน',
      },
      {
        value: 'PASSED',
        label: 'ผ่าน',
      },
      {
        value: 'NOT_PASS',
        label: 'ไม่ผ่าน',
      },
      {
        value: 'CANCELED',
        label: 'ยกเลิกการเรียน',
      },
    ],
    dropdownValueType: ColumnSettingDropdownValueTypeEnum.static,
    columnBulkCode: 'learning_status',
    columnBulkHeader: 'learning_status',
  },
  {
    key: ColumnSettingKeyEnum.CLASSROOM_TOTAL_ATTENDED,
    name: ColumnSettingNameDefaultEnum.CLASSROOM_TOTAL_ATTENDED,
    localeKey: 'd.classroom.total_attended',
    module: ColumnSettingModuleEnum.CLASSROOM_ENROLLMENT,
    groupFields: [
      {
        root: '',
        dataType: ColumnSettingDataTypeEnum.text,
        columnCode: 'totalAttended',
        relationalPath: 'users.guid->classroom-location-enrollments.userId',
        isFilter: false,
        isDisplay: true,
      },
    ],
    fieldType: ColumnSettingFieldTypeEnum.primary,
    filterType: ColumnSettingFilterTypeEnum.text,
    dropdownValue: [],
    dropdownValueType: ColumnSettingDropdownValueTypeEnum.static,
    columnBulkCode: 'total_attended',
    columnBulkHeader: 'total_attended',
  },
  {
    key: ColumnSettingKeyEnum.CLASSROOM_SCORE_HOMEWORK,
    name: ColumnSettingNameDefaultEnum.CLASSROOM_SCOR_EHOMEWORK,
    localeKey: 'd.classroom.score_homework',
    module: ColumnSettingModuleEnum.CLASSROOM_ENROLLMENT,
    groupFields: [
      {
        root: '',
        dataType: ColumnSettingDataTypeEnum.text,
        columnCode: 'scoreHomework',
        relationalPath: 'users.guid->classroom-location-enrollments.userId',
        isFilter: false,
        isDisplay: true,
      },
    ],
    fieldType: ColumnSettingFieldTypeEnum.primary,
    filterType: ColumnSettingFilterTypeEnum.text,
    dropdownValue: [],
    dropdownValueType: ColumnSettingDropdownValueTypeEnum.static,
    columnBulkCode: 'homework_score',
    columnBulkHeader: 'homework_score',
  },
  {
    key: ColumnSettingKeyEnum.CLASSROOM_REMARK,
    name: ColumnSettingNameDefaultEnum.CLASSROOM_REMARK,
    localeKey: 'd.classroom.remark',
    module: ColumnSettingModuleEnum.CLASSROOM_ENROLLMENT,
    groupFields: [
      {
        root: '',
        dataType: ColumnSettingDataTypeEnum.text,
        columnCode: 'remark',
        relationalPath: 'users.guid->classroom-location-enrollments.userId',
        isFilter: false,
        isDisplay: true,
      },
    ],
    fieldType: ColumnSettingFieldTypeEnum.primary,
    filterType: ColumnSettingFilterTypeEnum.text,
    dropdownValue: [],
    dropdownValueType: ColumnSettingDropdownValueTypeEnum.static,
    columnBulkCode: 'remark',
    columnBulkHeader: 'remark',
  },
];

const preAssingnContentColumnSettingList = [
  {
    key: ColumnSettingKeyEnum.JOB_TRANSACTION_STATUS,
    name: ColumnSettingNameDefaultEnum.JOB_TRANSACTION_STATUS,
    localeKey: 'job_transaction.status',
    module: ColumnSettingModuleEnum.JOB_TRANSACTION,
    groupFields: [
      {
        root: '',
        dataType: ColumnSettingDataTypeEnum.enum,
        columnCode: 'status',
        relationalPath: null,
        isFilter: true,
        isDisplay: true,
      },
    ],
    fieldType: ColumnSettingFieldTypeEnum.primary,
    dropdownValue: [
      {
        value: 'PASSED',
        label: 'ผ่าน',
      },
      {
        value: 'ERROR',
        label: 'ไม่ผ่าน',
      },
      {
        value: 'CANCELED',
        label: 'ยกเลิก',
      },
    ],
    dropdownValueType: ColumnSettingDropdownValueTypeEnum.static,
    filterType: ColumnSettingFilterTypeEnum.singleSelector,
  },
  {
    key: ColumnSettingKeyEnum.JOB_TRANSACTION_ERROR_MESSAGES,
    name: ColumnSettingNameDefaultEnum.JOB_TRANSACTION_ERROR_MESSAGES,
    localeKey: 'job_transaction.errorMessages',
    module: ColumnSettingModuleEnum.JOB_TRANSACTION,
    groupFields: [
      {
        root: '',
        dataType: ColumnSettingDataTypeEnum.text,
        columnCode: 'errorMessages',
        relationalPath: null,
        isFilter: false,
        isDisplay: true,
      },
    ],
    fieldType: ColumnSettingFieldTypeEnum.primary,
    dropdownValue: [],
    dropdownValueType: ColumnSettingDropdownValueTypeEnum.static,
    filterType: ColumnSettingFilterTypeEnum.text,
  },
  {
    key: ColumnSettingKeyEnum.JOB_TRANSACTION_USER_AVATAR,
    name: ColumnSettingNameDefaultEnum.USER_AVATAR,
    localeKey: 'user.avatar',
    module: ColumnSettingModuleEnum.JOB_TRANSACTION,
    groupFields: [
      {
        root: 'payload.user.profile',
        dataType: ColumnSettingDataTypeEnum.imageAvatarUrl,
        columnCode: 'avatar',
        relationalPath: null,
        isFilter: false,
        isDisplay: true,
      },
    ],
    fieldType: ColumnSettingFieldTypeEnum.primary,
    dropdownValue: [],
    dropdownValueType: ColumnSettingDropdownValueTypeEnum.static,
    filterType: ColumnSettingFilterTypeEnum.text,
  },
  {
    key: ColumnSettingKeyEnum.JOB_TRANSACTION_USER_FULLNAME,
    name: ColumnSettingNameDefaultEnum.USER_FULLNAME,
    localeKey: 'user.fullname',
    module: ColumnSettingModuleEnum.JOB_TRANSACTION,
    groupFields: [
      {
        root: 'payload.user.profile',
        dataType: ColumnSettingDataTypeEnum.text,
        columnCode: 'salute',
        relationalPath: null,
        isFilter: false,
        isDisplay: true,
      },
      {
        root: 'payload.user.profile',
        dataType: ColumnSettingDataTypeEnum.text,
        columnCode: 'firstname',
        relationalPath: null,
        isFilter: true,
        isDisplay: true,
      },
      {
        root: 'payload.user.profile',
        dataType: ColumnSettingDataTypeEnum.text,
        columnCode: 'middlename',
        relationalPath: null,
        isFilter: false,
        isDisplay: true,
      },
      {
        root: 'payload.user.profile',
        dataType: ColumnSettingDataTypeEnum.text,
        columnCode: 'lastname',
        relationalPath: null,
        isFilter: true,
        isDisplay: true,
      },
    ],
    fieldType: ColumnSettingFieldTypeEnum.primary,
    dropdownValue: [],
    dropdownValueType: ColumnSettingDropdownValueTypeEnum.static,
    filterType: ColumnSettingFilterTypeEnum.text,
  },
  {
    key: ColumnSettingKeyEnum.JOB_TRANSACTION_USER_EMAIL,
    name: ColumnSettingNameDefaultEnum.USER_EMAIL,
    localeKey: 'user.email',
    module: ColumnSettingModuleEnum.JOB_TRANSACTION,
    groupFields: [
      {
        root: 'payload.user',
        dataType: ColumnSettingDataTypeEnum.text,
        columnCode: 'email',
        relationalPath: null,
        isFilter: true,
        isDisplay: true,
      },
    ],
    fieldType: ColumnSettingFieldTypeEnum.primary,
    dropdownValue: [],
    dropdownValueType: ColumnSettingDropdownValueTypeEnum.static,
    filterType: ColumnSettingFilterTypeEnum.text,
  },
  {
    key: ColumnSettingKeyEnum.JOB_TRANSACTION_USER_MOBILE_PHONE_NUMBER,
    name: ColumnSettingNameDefaultEnum.USER_MOBILE_PHONE_NUMBER,
    localeKey: 'user.mobilePhoneNumber',
    module: ColumnSettingModuleEnum.JOB_TRANSACTION,
    groupFields: [
      {
        root: 'payload.user.profile',
        dataType: ColumnSettingDataTypeEnum.phone,
        columnCode: 'mobilePhoneNumber',
        relationalPath: null,
        isFilter: true,
        isDisplay: true,
      },
    ],
    fieldType: ColumnSettingFieldTypeEnum.primary,
    dropdownValue: [],
    dropdownValueType: ColumnSettingDropdownValueTypeEnum.static,
    filterType: ColumnSettingFilterTypeEnum.text,
  },
  {
    key: ColumnSettingKeyEnum.JOB_TRANSACTION_USER_GENDER,
    name: ColumnSettingNameDefaultEnum.USER_GENDER,
    localeKey: 'user.gender',
    module: ColumnSettingModuleEnum.JOB_TRANSACTION,
    groupFields: [
      {
        root: 'payload.user.profile',
        dataType: ColumnSettingDataTypeEnum.enum,
        columnCode: 'gender',
        relationalPath: null,
        isFilter: true,
        isDisplay: true,
      },
    ],
    fieldType: ColumnSettingFieldTypeEnum.primary,
    dropdownValue: [
      {
        value: 'MALE',
        label: 'ชาย',
      },
      {
        value: 'FEMALE',
        label: 'หญิง',
      },
      {
        value: 'UNSPECIFIC',
        label: 'ไม่ระบุ',
      },
    ],
    dropdownValueType: ColumnSettingDropdownValueTypeEnum.static,
    filterType: ColumnSettingFilterTypeEnum.multipleSelector,
  },
  {
    key: ColumnSettingKeyEnum.JOB_TRANSACTION_USER_DATE_OF_BIRTH,
    name: ColumnSettingNameDefaultEnum.USER_DATE_OF_BIRTH,
    localeKey: 'user.dateOfBirth',
    module: ColumnSettingModuleEnum.JOB_TRANSACTION,
    groupFields: [
      {
        root: 'payload.user.profile',
        dataType: ColumnSettingDataTypeEnum.date,
        columnCode: 'dateOfBirth',
        relationalPath: null,
        isFilter: true,
        isDisplay: true,
      },
    ],
    fieldType: ColumnSettingFieldTypeEnum.primary,
    dropdownValue: [],
    dropdownValueType: ColumnSettingDropdownValueTypeEnum.static,
    filterType: ColumnSettingFilterTypeEnum.dateRange,
  },
  {
    key: ColumnSettingKeyEnum.JOB_TRANSACTION_USER_EMPLOYEE_ID,
    name: ColumnSettingNameDefaultEnum.USER_EMPLOYEE_ID,
    localeKey: 'user.employeeId',
    module: ColumnSettingModuleEnum.JOB_TRANSACTION,
    groupFields: [
      {
        root: 'payload.user',
        dataType: ColumnSettingDataTypeEnum.text,
        columnCode: 'employeeId',
        relationalPath: null,
        isFilter: true,
        isDisplay: true,
      },
    ],
    fieldType: ColumnSettingFieldTypeEnum.primary,
    dropdownValue: [],
    dropdownValueType: ColumnSettingDropdownValueTypeEnum.static,
    filterType: ColumnSettingFilterTypeEnum.text,
  },
  {
    key: ColumnSettingKeyEnum.JOB_TRANSACTION_USER_POSITION,
    name: ColumnSettingNameDefaultEnum.USER_POSITION,
    localeKey: 'user.position',
    module: ColumnSettingModuleEnum.JOB_TRANSACTION,
    groupFields: [
      {
        root: 'payload.user',
        dataType: ColumnSettingDataTypeEnum.text,
        columnCode: 'position',
        relationalPath: null,
        isFilter: true,
        isDisplay: true,
      },
    ],
    fieldType: ColumnSettingFieldTypeEnum.primary,
    dropdownValue: [],
    dropdownValueType: ColumnSettingDropdownValueTypeEnum.static,
    filterType: ColumnSettingFilterTypeEnum.text,
  },
  {
    key: ColumnSettingKeyEnum.JOB_TRANSACTION_USER_DEPARTMENT,
    name: ColumnSettingNameDefaultEnum.USER_DEPARTMENT,
    localeKey: 'user.department',
    module: ColumnSettingModuleEnum.JOB_TRANSACTION,
    groupFields: [
      {
        root: 'payload',
        dataType: ColumnSettingDataTypeEnum.text,
        columnCode: 'departmentName',
        relationalPath: null,
        isFilter: true,
        isDisplay: true,
      },
    ],
    fieldType: ColumnSettingFieldTypeEnum.primary,
    filterType: ColumnSettingFilterTypeEnum.multipleSelector,
    dropdownValue: [],
    dropdownValueType: ColumnSettingDropdownValueTypeEnum.static,
  },
  {
    key: ColumnSettingKeyEnum.JOB_TRANSACTION_USER_SUPERVISOR,
    name: ColumnSettingNameDefaultEnum.USER_SUPERVISOR,
    localeKey: 'user.supervisor',
    module: ColumnSettingModuleEnum.JOB_TRANSACTION,
    groupFields: [
      {
        root: 'payload',
        dataType: ColumnSettingDataTypeEnum.text,
        columnCode: 'supervisorName',
        relationalPath: null,
        isFilter: true,
        isDisplay: true,
      },
    ],
    fieldType: ColumnSettingFieldTypeEnum.primary,
    filterType: ColumnSettingFilterTypeEnum.text,
    dropdownValue: [],
    dropdownValueType: ColumnSettingDropdownValueTypeEnum.static,
  },
  {
    key: ColumnSettingKeyEnum.JOB_TRANSACTION_USER_ACTIVE,
    name: ColumnSettingNameDefaultEnum.USER_ACTIVE,
    localeKey: 'user.active',
    module: ColumnSettingModuleEnum.JOB_TRANSACTION,
    groupFields: [
      {
        root: 'payload.user',
        columnCode: 'active',
        dataType: ColumnSettingDataTypeEnum.flag,
        relationalPath: null,
        isFilter: true,
        isDisplay: true,
      },
    ],
    fieldType: ColumnSettingFieldTypeEnum.primary,
    filterType: ColumnSettingFilterTypeEnum.singleSelector,
    dropdownValue: [
      {
        value: true,
        label: 'เปิดใช้งาน',
      },
      {
        value: false,
        label: 'ปิดใช้งาน',
      },
    ],
    dropdownValueType: ColumnSettingDropdownValueTypeEnum.static,
  },
  {
    key: ColumnSettingKeyEnum.JOB_TRANSACTION_USER_IS_TERMINATED,
    name: ColumnSettingNameDefaultEnum.USER_IS_TERMINATED,
    localeKey: 'user.isTerminated',
    module: ColumnSettingModuleEnum.JOB_TRANSACTION,
    groupFields: [
      {
        root: 'payload.user',
        columnCode: 'isTerminated',
        dataType: ColumnSettingDataTypeEnum.enum,
        relationalPath: null,
        isFilter: true,
        isDisplay: true,
      },
    ],
    fieldType: ColumnSettingFieldTypeEnum.primary,
    filterType: ColumnSettingFilterTypeEnum.singleSelector,
    dropdownValue: [
      {
        value: false,
        label: 'ปฏิบัติงาน',
      },
      {
        value: true,
        label: 'สิ้นสุดการปฏิบัติงาน',
      },
    ],
    dropdownValueType: ColumnSettingDropdownValueTypeEnum.static,
  },
  {
    key: ColumnSettingKeyEnum.JOB_TRANSACTION_USER_IS_PASSED_UL_SALE_QUALIFY,
    name: ColumnSettingNameDefaultEnum.USER_IS_PASSED_UL_SALE_QUALIFY,
    localeKey: 'user.isPassedUlSaleQualify',
    module: ColumnSettingModuleEnum.JOB_TRANSACTION,
    groupFields: [
      {
        root: 'payload.user',
        columnCode: 'isPassedUlSaleQualify',
        dataType: ColumnSettingDataTypeEnum.enum,
        relationalPath: null,
        isFilter: true,
        isDisplay: true,
      },
    ],
    fieldType: ColumnSettingFieldTypeEnum.primary,
    filterType: ColumnSettingFilterTypeEnum.singleSelector,
    dropdownValue: [
      {
        value: true,
        label: 'ผ่าน',
      },
      {
        value: false,
        label: 'ไม่ผ่าน',
      },
    ],
    dropdownValueType: ColumnSettingDropdownValueTypeEnum.static,
  },
  {
    key: ColumnSettingKeyEnum.JOB_TRANSACTION_USER_OIC_LICENSE_LIFE_NO,
    name: ColumnSettingNameDefaultEnum.USER_OIC_LICENSE_LIFE_NO,
    localeKey: 'user.oicLicenseLifeNo',
    module: ColumnSettingModuleEnum.JOB_TRANSACTION,
    groupFields: [
      {
        root: 'payload.userLicenses',
        dataType: ColumnSettingDataTypeEnum.text,
        columnCode: 'licenseNo',
        relationalPath: null,
        isFilter: true,
        isDisplay: true,
        displayConditions: [
          {
            columnCode: 'licenseTypeCode',
            value: 'OIC-002',
          },
        ],
      },
      {
        root: 'payload.userLicenses',
        dataType: ColumnSettingDataTypeEnum.text,
        columnCode: 'licenseTypeCode',
        relationalPath: null,
        isDefault: true,
        defaultValue: 'OIC-002',
        isFilter: true,
        isDisplay: false,
        filterType: ColumnSettingFilterTypeEnum.text,
      },
    ],
    fieldType: ColumnSettingFieldTypeEnum.primary,
    dropdownValue: [],
    dropdownValueType: ColumnSettingDropdownValueTypeEnum.static,
    filterType: ColumnSettingFilterTypeEnum.text,
  },
  {
    key: ColumnSettingKeyEnum.JOB_TRANSACTION_USER_OIC_LICENSE_LIFE_START_DATE,
    name: ColumnSettingNameDefaultEnum.USER_OIC_LICENSE_LIFE_START_DATE,
    localeKey: 'user.oicStartDateLife',
    module: ColumnSettingModuleEnum.JOB_TRANSACTION,
    groupFields: [
      {
        root: 'payload.userLicenses',
        dataType: ColumnSettingDataTypeEnum.date,
        columnCode: 'startedAt',
        relationalPath: null,
        isFilter: true,
        isDisplay: true,
        displayConditions: [
          {
            columnCode: 'licenseTypeCode',
            value: 'OIC-002',
          },
        ],
      },
      {
        root: 'payload.userLicenses',
        dataType: ColumnSettingDataTypeEnum.text,
        columnCode: 'licenseTypeCode',
        relationalPath: null,
        isDefault: true,
        defaultValue: 'OIC-002',
        isFilter: true,
        isDisplay: false,
        filterType: ColumnSettingFilterTypeEnum.text,
      },
    ],
    fieldType: ColumnSettingFieldTypeEnum.primary,
    dropdownValue: [],
    dropdownValueType: ColumnSettingDropdownValueTypeEnum.static,
    filterType: ColumnSettingFilterTypeEnum.dateRange,
  },
  {
    key: ColumnSettingKeyEnum.JOB_TRANSACTION_USER_OIC_LICENSE_LIFE_END_DATE,
    name: ColumnSettingNameDefaultEnum.USER_OIC_LICENSE_LIFE_END_DATE,
    localeKey: 'user.oicEndDateLife',
    module: ColumnSettingModuleEnum.JOB_TRANSACTION,
    groupFields: [
      {
        root: 'payload.userLicenses',
        dataType: ColumnSettingDataTypeEnum.date,
        columnCode: 'expiredAt',
        relationalPath: null,
        isFilter: true,
        isDisplay: true,
        displayConditions: [
          {
            columnCode: 'licenseTypeCode',
            value: 'OIC-002',
          },
        ],
      },
      {
        root: 'payload.userLicenses',
        dataType: ColumnSettingDataTypeEnum.text,
        columnCode: 'licenseTypeCode',
        relationalPath: null,
        isDefault: true,
        defaultValue: 'OIC-002',
        filterType: ColumnSettingFilterTypeEnum.text,
        isFilter: true,
        isDisplay: false,
      },
    ],
    fieldType: ColumnSettingFieldTypeEnum.primary,
    dropdownValue: [],
    dropdownValueType: ColumnSettingDropdownValueTypeEnum.static,
    filterType: ColumnSettingFilterTypeEnum.dateRange,
  },
  {
    key: ColumnSettingKeyEnum.JOB_TRANSACTION_USER_OIC_LICENSE_NON_LIFE_NO,
    name: ColumnSettingNameDefaultEnum.USER_OIC_LICENSE_NON_LIFE_NO,
    localeKey: 'user.oicLicenseNonLife',
    module: ColumnSettingModuleEnum.JOB_TRANSACTION,
    groupFields: [
      {
        root: 'payload.userLicenses',
        dataType: ColumnSettingDataTypeEnum.text,
        columnCode: 'licenseNo',
        relationalPath: null,
        isFilter: true,
        isDisplay: true,
        displayConditions: [
          {
            columnCode: 'licenseTypeCode',
            value: 'OIC-001',
          },
        ],
      },
      {
        root: 'payload.userLicenses',
        dataType: ColumnSettingDataTypeEnum.text,
        columnCode: 'licenseTypeCode',
        relationalPath: null,
        isDefault: true,
        defaultValue: 'OIC-001',
        isFilter: true,
        isDisplay: false,
        filterType: ColumnSettingFilterTypeEnum.text,
      },
    ],
    fieldType: ColumnSettingFieldTypeEnum.primary,
    dropdownValue: [],
    dropdownValueType: ColumnSettingDropdownValueTypeEnum.static,
    filterType: ColumnSettingFilterTypeEnum.text,
  },
  {
    key: ColumnSettingKeyEnum.JOB_TRANSACTION_USER_OIC_LICENSE_NON_LIFE_START_DATE,
    name: ColumnSettingNameDefaultEnum.USER_OIC_LICENSE_NON_LIFE_START_DATE,
    localeKey: 'user.oicStartDateNonLife',
    module: ColumnSettingModuleEnum.JOB_TRANSACTION,
    groupFields: [
      {
        root: 'payload.userLicenses',
        dataType: ColumnSettingDataTypeEnum.date,
        columnCode: 'startedAt',
        relationalPath: null,
        isFilter: true,
        isDisplay: true,
        displayConditions: [
          {
            columnCode: 'licenseTypeCode',
            value: 'OIC-001',
          },
        ],
      },
      {
        root: 'payload.userLicenses',
        dataType: ColumnSettingDataTypeEnum.text,
        columnCode: 'licenseTypeCode',
        relationalPath: null,
        isDefault: true,
        defaultValue: 'OIC-001',
        isFilter: true,
        isDisplay: false,
        filterType: ColumnSettingFilterTypeEnum.text,
      },
    ],
    fieldType: ColumnSettingFieldTypeEnum.primary,
    dropdownValue: [],
    dropdownValueType: ColumnSettingDropdownValueTypeEnum.static,
    filterType: ColumnSettingFilterTypeEnum.dateRange,
  },
  {
    key: ColumnSettingKeyEnum.JOB_TRANSACTION_USER_OIC_LICENSE_NON_LIFE_END_DATE,
    name: ColumnSettingNameDefaultEnum.USER_OIC_LICENSE_NON_LIFE_END_DATE,
    localeKey: 'user.oicEndDateNonLife',
    module: ColumnSettingModuleEnum.JOB_TRANSACTION,
    groupFields: [
      {
        root: 'payload.userLicenses',
        dataType: ColumnSettingDataTypeEnum.date,
        columnCode: 'expiredAt',
        relationalPath: null,
        isFilter: true,
        isDisplay: true,
        displayConditions: [
          {
            columnCode: 'licenseTypeCode',
            value: 'OIC-001',
          },
        ],
      },
      {
        root: 'payload.userLicenses',
        dataType: ColumnSettingDataTypeEnum.text,
        columnCode: 'licenseTypeCode',
        relationalPath: null,
        isDefault: true,
        defaultValue: 'OIC-001',
        filterType: ColumnSettingFilterTypeEnum.text,
        isFilter: true,
        isDisplay: false,
      },
    ],
    fieldType: ColumnSettingFieldTypeEnum.primary,
    dropdownValue: [],
    dropdownValueType: ColumnSettingDropdownValueTypeEnum.static,
    filterType: ColumnSettingFilterTypeEnum.dateRange,
  },
  {
    key: ColumnSettingKeyEnum.JOB_TRANSACTION_USER_TSI_LICENSE_NO,
    name: ColumnSettingNameDefaultEnum.USER_TSI_LICENSE_NO,
    localeKey: 'user.tsiLicenseNo',
    module: ColumnSettingModuleEnum.JOB_TRANSACTION,
    groupFields: [
      {
        root: 'payload.userLicenses',
        dataType: ColumnSettingDataTypeEnum.text,
        columnCode: 'licenseNo',
        relationalPath: null,
        isFilter: true,
        isDisplay: true,
        displayConditions: [
          {
            columnCode: 'licenseTypeCode',
            value: 'TSI-001',
          },
        ],
      },
      {
        root: 'payload.userLicenses',
        dataType: ColumnSettingDataTypeEnum.text,
        columnCode: 'licenseTypeCode',
        relationalPath: null,
        isDefault: true,
        defaultValue: 'TSI-001',
        isFilter: true,
        isDisplay: false,
        filterType: ColumnSettingFilterTypeEnum.text,
      },
    ],
    fieldType: ColumnSettingFieldTypeEnum.primary,
    dropdownValue: [],
    dropdownValueType: ColumnSettingDropdownValueTypeEnum.static,
    filterType: ColumnSettingFilterTypeEnum.text,
  },
  {
    key: ColumnSettingKeyEnum.JOB_TRANSACTION_USER_TSI_LICENSE_TYPE,
    name: ColumnSettingNameDefaultEnum.USER_TSI_LICENSE_TYPE,
    localeKey: 'user.tsiLicenseType',
    module: ColumnSettingModuleEnum.JOB_TRANSACTION,
    groupFields: [
      {
        root: 'payload.userLicenses',
        dataType: ColumnSettingDataTypeEnum.text,
        columnCode: 'type',
        relationalPath: null,
        isFilter: true,
        isDisplay: true,
        displayConditions: [
          {
            columnCode: 'licenseTypeCode',
            value: 'TSI-001',
          },
        ],
      },
      {
        root: 'payload.userLicenses',
        dataType: ColumnSettingDataTypeEnum.text,
        columnCode: 'licenseTypeCode',
        relationalPath: null,
        isDefault: true,
        defaultValue: 'TSI-001',
        isFilter: true,
        isDisplay: false,
        filterType: ColumnSettingFilterTypeEnum.text,
      },
    ],
    fieldType: ColumnSettingFieldTypeEnum.primary,
    dropdownValue: [
      {
        value: 'ผู้แนะนำการลงทุน',
        label: 'ผู้แนะนำการลงทุน',
      },
      {
        value: 'ผู้วางแผนการลงทุน',
        label: 'ผู้วางแผนการลงทุน',
      },
      {
        value: 'นักวิเคราะห์การลงทุน',
        label: 'นักวิเคราะห์การลงทุน',
      },
    ],
    dropdownValueType: ColumnSettingDropdownValueTypeEnum.static,
    filterType: ColumnSettingFilterTypeEnum.multipleSelector,
  },
  {
    key: ColumnSettingKeyEnum.JOB_TRANSACTION_USER_TSI_LICENSE_START_DATE,
    name: ColumnSettingNameDefaultEnum.USER_TSI_LICENSE_START_DATE,
    localeKey: 'user.tsiStartDate',
    module: ColumnSettingModuleEnum.JOB_TRANSACTION,
    groupFields: [
      {
        root: 'payload.userLicenses',
        dataType: ColumnSettingDataTypeEnum.date,
        columnCode: 'startedAt',
        relationalPath: null,
        isFilter: true,
        isDisplay: true,
        displayConditions: [
          {
            columnCode: 'licenseTypeCode',
            value: 'TSI-001',
          },
        ],
      },
      {
        root: 'payload.userLicenses',
        dataType: ColumnSettingDataTypeEnum.text,
        columnCode: 'licenseTypeCode',
        relationalPath: null,
        isDefault: true,
        defaultValue: 'TSI-001',
        isFilter: true,
        isDisplay: false,
        filterType: ColumnSettingFilterTypeEnum.text,
      },
    ],
    fieldType: ColumnSettingFieldTypeEnum.primary,
    dropdownValue: [],
    dropdownValueType: ColumnSettingDropdownValueTypeEnum.static,
    filterType: ColumnSettingFilterTypeEnum.dateRange,
  },
  {
    key: ColumnSettingKeyEnum.JOB_TRANSACTION_USER_TSI_LICENSE_END_DATE,
    name: ColumnSettingNameDefaultEnum.USER_TSI_LICENSE_END_DATE,
    localeKey: 'user.tsiEndDate',
    module: ColumnSettingModuleEnum.JOB_TRANSACTION,
    groupFields: [
      {
        root: 'payload.userLicenses',
        dataType: ColumnSettingDataTypeEnum.date,
        columnCode: 'expiredAt',
        relationalPath: null,
        isFilter: true,
        isDisplay: true,
        displayConditions: [
          {
            columnCode: 'licenseTypeCode',
            value: 'TSI-001',
          },
        ],
      },
      {
        root: 'payload.userLicenses',
        dataType: ColumnSettingDataTypeEnum.text,
        columnCode: 'licenseTypeCode',
        relationalPath: null,
        isDefault: true,
        defaultValue: 'TSI-001',
        isFilter: true,
        isDisplay: false,
        filterType: ColumnSettingFilterTypeEnum.text,
      },
    ],
    fieldType: ColumnSettingFieldTypeEnum.primary,
    dropdownValue: [],
    dropdownValueType: ColumnSettingDropdownValueTypeEnum.static,
    filterType: ColumnSettingFilterTypeEnum.dateRange,
  },
];

const userGroupColumnSettingList = [
  {
    key: ColumnSettingKeyEnum.USER_GROUP_USER_GROUP_PARTICIPATION,
    name: ColumnSettingNameDefaultEnum.USER_GROUP_PARTICIPATION,
    localeKey: 'user.user_group_participation',
    module: ColumnSettingModuleEnum.USER_GROUP,
    groupFields: [
      {
        root: '',
        dataType: ColumnSettingDataTypeEnum.enum,
        columnCode: 'participationType',
        relationalPath: '',
        isFilter: false,
        isDisplay: true,
      },
    ],
    fieldType: ColumnSettingFieldTypeEnum.primary,
    filterType: ColumnSettingFilterTypeEnum.singleSelector,
    dropdownValue: [
      {
        label: 'กำหนดเอง',
        value: 'manual',
      },
      {
        label: 'กรองตามเงื่อนไข',
        value: 'auto',
      },
    ],
    dropdownValueType: ColumnSettingDropdownValueTypeEnum.static,
  },
  {
    key: ColumnSettingKeyEnum.USER_GROUP_DEPARTMENT,
    name: ColumnSettingNameDefaultEnum.USER_DEPARTMENT,
    localeKey: 'user.department',
    module: ColumnSettingModuleEnum.DEPARTMENT,
    groupFields: [
      {
        root: '',
        dataType: ColumnSettingDataTypeEnum.text,
        columnCode: 'id',
        relationalPath: 'users.guid->departments.userIds',
        isFilter: true,
        isDisplay: false,
      },
      {
        root: '',
        dataType: ColumnSettingDataTypeEnum.text,
        columnCode: 'name',
        relationalPath: 'users.guid->departments.userIds',
        isFilter: false,
        isDisplay: true,
      },
    ],
    fieldType: ColumnSettingFieldTypeEnum.primary,
    filterType: ColumnSettingFilterTypeEnum.multipleSelector,
    dropdownValue: [],
    dropdownValueType: ColumnSettingDropdownValueTypeEnum.static,
  },
  {
    key: ColumnSettingKeyEnum.USER_GROUP_SUPERVISOR,
    name: ColumnSettingNameDefaultEnum.USER_SUPERVISOR,
    localeKey: 'user.supervisor',
    module: ColumnSettingModuleEnum.USER_DIRECT_REPORT,
    groupFields: [
      {
        root: '',
        dataType: ColumnSettingDataTypeEnum.text,
        columnCode: 'directReportUserId',
        relationalPath: 'users.guid->user-direct-reports.directReportUserId',
        isDefault: true,
        isFilter: true,
        isDisplay: false,
      },
    ],
    fieldType: ColumnSettingFieldTypeEnum.primary,
    filterType: ColumnSettingFilterTypeEnum.multipleSelector,
    dropdownValue: [],
    dropdownValueType: ColumnSettingDropdownValueTypeEnum.static,
  },
  {
    key: ColumnSettingKeyEnum.USER_GROUP_OIC_LICENSE_LIFE_NO,
    name: ColumnSettingNameDefaultEnum.USER_OIC_LICENSE_LIFE_NO,
    localeKey: 'user.oicLicenseLifeNo',
    module: ColumnSettingModuleEnum.LICENSE,
    groupFields: [
      {
        root: '',
        dataType: ColumnSettingDataTypeEnum.text,
        columnCode: 'licenseNo',
        relationalPath: '',
        isFilter: true,
        isDisplay: true,
      },
      {
        root: '',
        dataType: ColumnSettingDataTypeEnum.text,
        columnCode: 'licenseTypeCode',
        relationalPath: '',
        isDefault: true,
        defaultValue: 'OIC-002',
        isFilter: true,
        isDisplay: false,
        filterType: ColumnSettingFilterTypeEnum.text,
      },
    ],
    fieldType: ColumnSettingFieldTypeEnum.primary,
    filterType: ColumnSettingFilterTypeEnum.text,
    dropdownValue: [],
    dropdownValueType: ColumnSettingDropdownValueTypeEnum.static,
  },
  {
    key: ColumnSettingKeyEnum.USER_GROUP_OIC_LICENSE_LIFE_START_DATE,
    name: ColumnSettingNameDefaultEnum.USER_OIC_LICENSE_LIFE_START_DATE,
    localeKey: 'user.oicStartDateLife',
    module: ColumnSettingModuleEnum.LICENSE,
    groupFields: [
      {
        root: '',
        dataType: ColumnSettingDataTypeEnum.date,
        columnCode: 'startedAt',
        relationalPath: '',
        isFilter: true,
        isDisplay: true,
      },
      {
        root: '',
        dataType: ColumnSettingDataTypeEnum.text,
        columnCode: 'licenseTypeCode',
        relationalPath: '',
        isDefault: true,
        defaultValue: 'OIC-002',
        isFilter: true,
        isDisplay: false,
        filterType: ColumnSettingFilterTypeEnum.text,
      },
    ],
    fieldType: ColumnSettingFieldTypeEnum.primary,
    filterType: ColumnSettingFilterTypeEnum.dateRange,
    dropdownValue: [],
    dropdownValueType: ColumnSettingDropdownValueTypeEnum.static,
  },
  {
    key: ColumnSettingKeyEnum.USER_GROUP_OIC_LICENSE_LIFE_END_DATE,
    name: ColumnSettingNameDefaultEnum.USER_OIC_LICENSE_LIFE_END_DATE,
    localeKey: 'user.oicEndDateLife',
    module: ColumnSettingModuleEnum.LICENSE,
    groupFields: [
      {
        root: '',
        dataType: ColumnSettingDataTypeEnum.date,
        columnCode: 'expiredAt',
        relationalPath: '',
        isFilter: true,
        isDisplay: true,
      },
      {
        root: '',
        dataType: ColumnSettingDataTypeEnum.text,
        columnCode: 'licenseTypeCode',
        relationalPath: '',
        isDefault: true,
        defaultValue: 'OIC-002',
        isFilter: true,
        isDisplay: false,
        filterType: ColumnSettingFilterTypeEnum.text,
      },
    ],
    fieldType: ColumnSettingFieldTypeEnum.primary,
    filterType: ColumnSettingFilterTypeEnum.dateRange,
    dropdownValue: [],
    dropdownValueType: ColumnSettingDropdownValueTypeEnum.static,
  },
  {
    key: ColumnSettingKeyEnum.USER_GROUP_OIC_LICENSE_NON_LIFE_NO,
    name: ColumnSettingNameDefaultEnum.USER_OIC_LICENSE_NON_LIFE_NO,
    localeKey: 'user.oicLicenseNonLife',
    module: ColumnSettingModuleEnum.LICENSE,
    groupFields: [
      {
        root: '',
        dataType: ColumnSettingDataTypeEnum.text,
        columnCode: 'licenseNo',
        relationalPath: '',
        isFilter: true,
        isDisplay: true,
      },
      {
        root: '',
        dataType: ColumnSettingDataTypeEnum.text,
        columnCode: 'licenseTypeCode',
        relationalPath: '',
        isDefault: true,
        defaultValue: 'OIC-001',
        isFilter: true,
        isDisplay: false,
        filterType: ColumnSettingFilterTypeEnum.text,
      },
    ],
    fieldType: ColumnSettingFieldTypeEnum.primary,
    filterType: ColumnSettingFilterTypeEnum.text,
    dropdownValue: [],
    dropdownValueType: ColumnSettingDropdownValueTypeEnum.static,
  },
  {
    key: ColumnSettingKeyEnum.USER_GROUP_OIC_LICENSE_NON_LIFE_START_DATE,
    name: ColumnSettingNameDefaultEnum.USER_OIC_LICENSE_NON_LIFE_START_DATE,
    localeKey: 'user.oicStartDateNonLife',
    module: ColumnSettingModuleEnum.LICENSE,
    groupFields: [
      {
        root: '',
        dataType: ColumnSettingDataTypeEnum.date,
        columnCode: 'startedAt',
        relationalPath: '',
        isFilter: true,
        isDisplay: true,
      },
      {
        root: '',
        dataType: ColumnSettingDataTypeEnum.text,
        columnCode: 'licenseTypeCode',
        relationalPath: '',
        isDefault: true,
        defaultValue: 'OIC-001',
        isFilter: true,
        isDisplay: false,
        filterType: ColumnSettingFilterTypeEnum.text,
      },
    ],
    fieldType: ColumnSettingFieldTypeEnum.primary,
    filterType: ColumnSettingFilterTypeEnum.text,
    dropdownValue: [],
    dropdownValueType: ColumnSettingDropdownValueTypeEnum.static,
  },
  {
    key: ColumnSettingKeyEnum.USER_GROUP_OIC_LICENSE_NON_LIFE_END_DATE,
    name: ColumnSettingNameDefaultEnum.USER_OIC_LICENSE_NON_LIFE_END_DATE,
    localeKey: 'user.oicEndDateNonLife',
    module: ColumnSettingModuleEnum.LICENSE,
    groupFields: [
      {
        root: '',
        dataType: ColumnSettingDataTypeEnum.date,
        columnCode: 'expiredAt',
        relationalPath: '',
        isFilter: true,
        isDisplay: true,
      },
      {
        root: '',
        dataType: ColumnSettingDataTypeEnum.text,
        columnCode: 'licenseTypeCode',
        relationalPath: '',
        isDefault: true,
        defaultValue: 'OIC-001',
        isFilter: true,
        isDisplay: false,
        filterType: ColumnSettingFilterTypeEnum.text,
      },
    ],
    fieldType: ColumnSettingFieldTypeEnum.primary,
    filterType: ColumnSettingFilterTypeEnum.dateRange,
    dropdownValue: [],
    dropdownValueType: ColumnSettingDropdownValueTypeEnum.static,
  },
  {
    key: ColumnSettingKeyEnum.USER_GROUP_TSI_LICENSE_NO,
    name: ColumnSettingNameDefaultEnum.USER_TSI_LICENSE_NO,
    localeKey: 'user.tsiLicenseNo',
    module: ColumnSettingModuleEnum.LICENSE,
    groupFields: [
      {
        root: '',
        dataType: ColumnSettingDataTypeEnum.text,
        columnCode: 'licenseNo',
        relationalPath: 'users.guid->licenses.userId',
        isFilter: true,
        isDisplay: true,
        displayConditions: [
          {
            columnCode: 'licenseTypeCode',
            value: 'TSI-001',
          },
        ],
      },
      {
        root: '',
        dataType: ColumnSettingDataTypeEnum.text,
        columnCode: 'licenseTypeCode',
        relationalPath: 'users.guid->licenses.userId',
        isDefault: true,
        defaultValue: 'TSI-001',
        isFilter: true,
        isDisplay: false,
        filterType: ColumnSettingFilterTypeEnum.text,
      },
    ],
    fieldType: ColumnSettingFieldTypeEnum.primary,
    filterType: ColumnSettingFilterTypeEnum.text,
    dropdownValue: [],
    dropdownValueType: ColumnSettingDropdownValueTypeEnum.static,
  },
  {
    key: ColumnSettingKeyEnum.USER_GROUP_TSI_LICENSE_TYPE,
    name: ColumnSettingNameDefaultEnum.USER_TSI_LICENSE_TYPE,
    localeKey: 'user.tsiLicenseType',
    module: ColumnSettingModuleEnum.LICENSE,
    groupFields: [
      {
        root: '',
        dataType: ColumnSettingDataTypeEnum.text,
        columnCode: 'type',
        relationalPath: 'users.guid->licenses.userId',
        isFilter: true,
        isDisplay: false,
      },
      {
        root: '',
        dataType: ColumnSettingDataTypeEnum.text,
        columnCode: 'licenseTypeCode',
        relationalPath: 'users.guid->licenses.userId',
        isDefault: true,
        defaultValue: 'TSI-001',
        isFilter: true,
        isDisplay: false,
        filterType: ColumnSettingFilterTypeEnum.text,
      },
    ],
    fieldType: ColumnSettingFieldTypeEnum.primary,
    filterType: ColumnSettingFilterTypeEnum.text,
    dropdownValue: [
      {
        value: 'ผู้แนะนำการลงทุน',
        label: 'ผู้แนะนำการลงทุน',
      },
      {
        value: 'ผู้วางแผนการลงทุน',
        label: 'ผู้วางแผนการลงทุน',
      },
      {
        value: 'นักวิเคราะห์การลงทุน',
        label: 'นักวิเคราะห์การลงทุน',
      },
    ],
    dropdownValueType: ColumnSettingDropdownValueTypeEnum.static,
  },
  {
    key: ColumnSettingKeyEnum.USER_GROUP_TSI_LICENSE_START_DATE,
    name: ColumnSettingNameDefaultEnum.USER_TSI_LICENSE_START_DATE,
    localeKey: 'user.tsiStartDate',
    module: ColumnSettingModuleEnum.LICENSE,
    groupFields: [
      {
        root: '',
        dataType: ColumnSettingDataTypeEnum.date,
        columnCode: 'startedAt',
        relationalPath: 'users.guid->licenses.userId',
        isFilter: true,
        isDisplay: true,
      },
      {
        root: '',
        dataType: ColumnSettingDataTypeEnum.text,
        columnCode: 'licenseTypeCode',
        relationalPath: '',
        isDefault: true,
        defaultValue: 'TSI-001',
        isFilter: true,
        isDisplay: false,
        filterType: ColumnSettingFilterTypeEnum.text,
      },
    ],
    fieldType: ColumnSettingFieldTypeEnum.primary,
    filterType: ColumnSettingFilterTypeEnum.dateRange,
    dropdownValue: [],
    dropdownValueType: ColumnSettingDropdownValueTypeEnum.static,
  },
  {
    key: ColumnSettingKeyEnum.USER_GROUP_TSI_LICENSE_END_DATE,
    name: ColumnSettingNameDefaultEnum.USER_TSI_LICENSE_END_DATE,
    localeKey: 'user.tsiEndDate',
    module: ColumnSettingModuleEnum.LICENSE,
    groupFields: [
      {
        root: '',
        dataType: ColumnSettingDataTypeEnum.date,
        columnCode: 'expiredAt',
        relationalPath: 'users.guid->licenses.userId',
        isFilter: true,
        isDisplay: true,
      },
      {
        root: '',
        dataType: ColumnSettingDataTypeEnum.text,
        columnCode: 'licenseTypeCode',
        relationalPath: '',
        isDefault: true,
        defaultValue: 'TSI-001',
        isFilter: true,
        isDisplay: false,
        filterType: ColumnSettingFilterTypeEnum.text,
      },
    ],
    fieldType: ColumnSettingFieldTypeEnum.primary,
    filterType: ColumnSettingFilterTypeEnum.dateRange,
    dropdownValue: [],
    dropdownValueType: ColumnSettingDropdownValueTypeEnum.static,
  },
  {
    key: ColumnSettingKeyEnum.USER_GROUP_COURSE,
    name: ColumnSettingNameDefaultEnum.COURSES,
    localeKey: 'd.user_group.course',
    module: ColumnSettingModuleEnum.COURSE,
    groupFields: [
      {
        root: '',
        dataType: ColumnSettingDataTypeEnum.text,
        columnCode: 'id',
        relationalPath: null,
        isFilter: true,
        isDisplay: false,
      },
      {
        root: '',
        dataType: ColumnSettingDataTypeEnum.text,
        columnCode: 'name',
        relationalPath: null,
        isFilter: false,
        isDisplay: true,
      },
    ],
    fieldType: ColumnSettingFieldTypeEnum.primary,
    filterType: ColumnSettingFilterTypeEnum.multipleEnrollmentSelector,
    dropdownValue: [],
    dropdownValueType: ColumnSettingDropdownValueTypeEnum.static,
  },
];

const achievementColumnSettingList = [
  {
    key: ColumnSettingKeyEnum.ACHIEVEMENT_NAME,
    name: ColumnSettingNameDefaultEnum.ACHIEVEMENT_NAME,
    localeKey: 'achievement.name',
    module: ColumnSettingModuleEnum.ACHIEVEMENT,
    groupFields: [
      {
        root: '',
        dataType: ColumnSettingDataTypeEnum.text,
        columnCode: 'name',
        relationalPath: '',
        isFilter: true,
        isDisplay: true,
      },
    ],
    fieldType: ColumnSettingFieldTypeEnum.primary,
    filterType: ColumnSettingFilterTypeEnum.text,
    dropdownValue: [],
    dropdownValueType: ColumnSettingDropdownValueTypeEnum.static,
  },
];

const predefinedCreateColumnSettingList = [
  ...userColumnSettingList,
  ...courseColumnSettingList,
  ...learningPathColumnSettingList,
  ...enrollmentColumnSettingList,
  ...learningPathEnrollmentColumnSettingList,
  ...preEnrollmentColumnSettingList,
  ...enrollmentAttachmentColumnSettingList,
  ...knowledgeContentItemColumnSettingList,
  ...preAssingnContentColumnSettingList,
  ...classroomColumnSettingList,
  ...userGroupColumnSettingList,
  ...achievementColumnSettingList,
];

const formatListResult = (insertedTotal, total) => {
  return `${insertedTotal}/${total}`;
};

const seeder = async () => {
  console.log(`\x1b[36m\n\nColumn Setting Seeder\x1b[0m`);
  console.log(`\x1b[30m---------------------------------------\x1b[0m`);

  const client = await connectToDb(config.DB_HOST);

  try {
    const preview = () => {
      const allKey = predefinedCreateColumnSettingList.map((val) => {
        return {
          key: val.key,
        };
      });
      console.table(allKey);
    };
    const confirm = await askToConfirm(config, preview);
    if (!confirm) {
      console.log('> Cancelled!');
      return;
    }

    const columnSettingList = predefinedCreateColumnSettingList;
    const columnSettingModels = [];
    for (const item of columnSettingList) {
      const columnSettingModel = await ColumnSetting.new(item);
      columnSettingModels.push({
        id: columnSettingModel.id,
        key: columnSettingModel.key,
        name: columnSettingModel.name,
        localeKey: columnSettingModel.localeKey,
        module: columnSettingModel.module,
        groupFields: columnSettingModel.groupFields.map((val) => {
          return {
            root: val.root,
            dataType: val.dataType,
            columnCode: val.columnCode,
            relationalPath: val.relationalPath,
            isFilter: val.isFilter,
            isDisplay: val.isDisplay,
            isDefault: val.isDefault,
            defaultValue: val.defaultValue,
            displayConditions: val.displayConditions.map((con) => {
              return {
                columnCode: con.columnCode,
                value: con.value,
              };
            }),
            filterType: val.filterType,
          };
        }),
        fieldType: columnSettingModel.fieldType,
        displayComponent: {
          componentType: columnSettingModel.displayComponent?.componentType,
          refValue: columnSettingModel.displayComponent?.refValue,
          alignment: columnSettingModel.displayComponent?.alignment,
          width: columnSettingModel.displayComponent?.width,
        },
        dropdownValue: columnSettingModel.dropdownValue,
        dropdownValueType: columnSettingModel.dropdownValueType,
        filterType: columnSettingModel.filterType,
        columnBulkCode: columnSettingModel.columnBulkCode,
        columnBulkHeader: columnSettingModel.columnBulkHeader,
        dataType: columnSettingModel.dataType,
        isUnique: columnSettingModel.isUnique,
        isEditableFilter: columnSettingModel.groupFields?.some((field) => field.isFilter),
        createdAt: columnSettingModel.createdAt,
        updatedAt: columnSettingModel.updatedAt,
        deletedAt: columnSettingModel.deletedAt,
      });
    }

    const db = client.db(config.DB_NAME);
    const columnSettingCollection = db.collection('column-settings');
    await columnSettingCollection.deleteMany({});
    await columnSettingCollection.insertMany(columnSettingModels);

    console.log(`Column setting have been created \n >  ${predefinedCreateColumnSettingList.length} records.`);
  } catch (error) {
    console.error(error);
  } finally {
    client.close();
  }
};

seeder();
