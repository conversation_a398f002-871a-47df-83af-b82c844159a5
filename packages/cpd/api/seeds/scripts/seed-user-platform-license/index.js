#!/usr/bin/env node

const { Collection, CollectionBulkWriteOptions } = require('mongodb');
const process = require('process');
const chalk = require('chalk');

const keyBy = require('lodash/keyBy');
const { instanceToPlain } = require('class-transformer');

const { DBCollectionEnum } = require('@iso/lms/enums/dbCollection.enum');
const { PlanPackageLicense } = require('@iso/lms/models/planPackageLicense.model');
const { PackageTypeEnum } = require('@iso/lms/enums/packages.enum');
const { SaleOrderStatusEnum } = require('@iso/lms/enums/plan.enum');
const { config } = require('../../../migrations/helper/utility');
const { connectToDb } = require('../../utils/db');
const { askForOrganizations } = require('../../utils/askOrganizationScript');
const { askToConfirm } = require('../../utils/askConfirmScript');
const delay = require('../../utils/delay');
const ProcessLogCreatePlanPackageLicense = require('./log/process.log');

const main = async () => {
  const connection = await connectToDb(config.DB_HOST);
  const db = connection.db(config.DB_NAME);

  const organizationCollection = db.collection(DBCollectionEnum.ORGANIZATIONS);
  const planCollection = db.collection(DBCollectionEnum.PLANS);
  const planPackageCollection = db.collection(DBCollectionEnum.PLAN_PACKAGES);
  const planPackageLicenseCollection = db.collection(DBCollectionEnum.PLAN_PACKAGE_LICENSES);
  const userCollection = db.collection(DBCollectionEnum.USERS);

  const organizations = await organizationCollection
    .find({ deletedAt: null }, { projection: { _id: 0, id: 1, domain: 1 }, sort: { domain: 1 } })
    .toArray();

  // ask organization
  const organizationDomain = organizations.map((organization) => organization.domain);
  const selection = await askForOrganizations(organizationDomain);

  if (selection === null) {
    stopLogMessage('Cancelled!', 'None of the above');
    process.exit(0);
  }
  const organization = organizations.find((organization) => organization.domain === selection);

  const plans = await planCollection
    .find(
      { organizationId: organization.id, saleOrderStatus: SaleOrderStatusEnum.PENDING, deletedAt: null },
      { projection: { _id: 0, id: 1, name: 1 } },
    )
    .toArray();

  if (plans.length === 0) {
    stopLogMessage('!Have no available plan ', organization.domain);
    process.exit(0);
  }

  const planById = keyBy(plans, 'id');
  const planIds = plans.map((plan) => plan.id);

  const planPackagePlatforms = await findPlanPackagePlatform(planPackageCollection, planIds);

  const [{ allPackagePlans, nonApproveSOPlanPackages }] = planPackagePlatforms;

  if (!nonApproveSOPlanPackages || nonApproveSOPlanPackages?.length === 0) {
    stopLogMessage('!!Have no none SO approved plan package platform', organization.domain);
    process.exit(0);
  }

  const planPackageIds = allPackagePlans[0].ids;
  const userPackageLicenses = await planPackageLicenseCollection
    .aggregate([
      {
        $match: {
          planPackageId: { $in: planPackageIds },
          userId: { $ne: null },
          deletedAt: null,
        },
      },
      {
        $group: {
          _id: '$planPackageId',
          userIds: { $push: '$userId' },
        },
      },
    ])
    .toArray();

  const userPackageLicensesByPlanPackageId = keyBy(userPackageLicenses, '_id');
  const userIdsPackageLicenses = userPackageLicenses.flatMap((userPackageLicense) => userPackageLicense.userIds);

  const totalUnAssignLicenseUser = await userCollection.countDocuments({
    guid: { $nin: userIdsPackageLicenses },
    organizationId: organization.id,
    active: { $ne: false },
  });

  const nonApproveSORemainUserPlanPackage = nonApproveSOPlanPackages
    .map((planPackage) => {
      const userPackageLicense = userPackageLicensesByPlanPackageId[planPackage.id];
      const plan = planById[planPackage.planId];

      return {
        ...planPackage,
        planName: plan?.name ?? '',
        usageLicense: userPackageLicense ? userPackageLicense.userIds.length : 0,
        totalUnAssignLicenseUser,
      };
    })
    .filter((planPackage) => planPackage.usageLicense < planPackage.totalLicense);

  if (nonApproveSORemainUserPlanPackage.length === 0) {
    stopLogMessage('All none so approved plan package platform totally used', organization.domain);
    process.exit(0);
  }

  if (totalUnAssignLicenseUser < 1) {
    stopLogMessage('No unassigned license user', organization.domain);
    process.exit(0);
  }

  showLogPlanPackage(nonApproveSORemainUserPlanPackage);

  console.log('\n');

  const confirm = await askToConfirm(config);
  if (!confirm) {
    stopLogMessage('Cancelled!', organization.domain);
    process.exit(0);
  }

  let totalCreateLicense = 0;

  for (const [index, planPackage] of nonApproveSORemainUserPlanPackage.entries()) {
    let number = 0;
    const totalRemainderLicense = planPackage.totalLicense - planPackage.usageLicense;
    const limit = totalRemainderLicense;

    const processLogCreatePlanPackageLicense = new ProcessLogCreatePlanPackageLicense(
      index + 1,
      planPackage,
      totalRemainderLicense,
      totalUnAssignLicenseUser,
    );

    processLogCreatePlanPackageLicense.start();

    const props = {
      userIds: userIdsPackageLicenses,
      organizationId: organization.id,
      skip: totalCreateLicense,
      limit,
    };

    await streamCreateUserPackageUserLicenseList(userCollection, props, async (userIds) => {
      const _planPackageLicenses = await Promise.all(
        userIds.map((userId) =>
          PlanPackageLicense.new({
            planPackageId: planPackage.id,
            userId,
          }),
        ),
      );

      const size = _planPackageLicenses.length;
      totalCreateLicense += size;
      number += size;

      processLogCreatePlanPackageLicense.update(number);

      await insertManyPlanPackageLicenses(planPackageLicenseCollection, _planPackageLicenses);
      await planPackageCollection.updateOne({ id: planPackage.id }, [
        {
          $set: {
            'content.remainLicense': {
              $max: [
                0,
                {
                  $subtract: ['$content.remainLicense', size],
                },
              ],
            },
          },
        },
      ]);
      await delay(100);
    });

    processLogCreatePlanPackageLicense.complete(number);
    await delay(100);
  }

  process.exit(0);
};

const showLogPlanPackage = (nonApproveSORemainUserPlanPackage) => {
  console.log(chalk.bgCyan('All Non Approved SSO Plan Package'));

  for (const [index, planPackage] of nonApproveSORemainUserPlanPackage.entries()) {
    console.log(chalk.green(`${index + 1}) Plan Package`));
    console.log(chalk.yellow(`   Plan Name: `), planPackage.planName);
    console.log(chalk.yellow(`   Package Name: `), planPackage.name);
    console.log(chalk.yellow(`   Total Unassigned: `), Number(planPackage.totalUnAssignLicenseUser).toLocaleString());
    console.log(
      chalk.yellow(
        `   Total Remaining License: ${chalk.whiteBright(Number(planPackage.totalLicense - planPackage.usageLicense).toLocaleString())}`,
      ),
    );
    if (index !== nonApproveSORemainUserPlanPackage.length - 1) {
      console.log(chalk.gray('==============================================================='));
    }
  }
};

/**
 *
 * @param {string} message
 * @param {string} domain
 */
const stopLogMessage = (message, domain) => {
  console.log(chalk.bgRedBright(' Stop: '));
  console.log(chalk.yellow('organization: '), domain);
  console.log(chalk.yellow('message: '), message);
};

/**
 *
 * @param {Collection} planPackageCollection
 * @param {Array<string>} planIds
 * @returns {Promise<any>}
 */

const findPlanPackagePlatform = async (planPackageCollection, planIds) => {
  const dateNow = new Date();
  const pipeline = [
    {
      $match: {
        type: PackageTypeEnum.PLATFORM,
        planId: { $in: planIds },
        deletedAt: null,
      },
    },
    {
      $facet: {
        allPackagePlans: [
          {
            $group: {
              _id: null,
              ids: { $push: '$id' },
            },
          },
        ],
        nonApproveSOPlanPackages: [
          {
            $match: {
              startDate: {
                $lte: dateNow,
              },
              endDate: {
                $gte: dateNow,
              },
            },
          },
          {
            $sort: {
              createdAt: 1,
            },
          },
          {
            $project: {
              _id: 0,
              id: 1,
              name: 1,
              planId: 1,
              totalLicense: '$content.totalLicense',
            },
          },
        ],
      },
    },
  ];

  return planPackageCollection.aggregate(pipeline).toArray();
};

/**
 * @typedef {Object} StreamUserProps
 * @property {Array<string>} userIds
 * @property {string} organizationId
 * @property {number} skip
 * @property {number} limit
 *
 * @param {Collection} userCollection
 * @param {StreamUserProps} props
 * @param {{(userId: Array<string>): Promise<void>}} callback
 */
const streamCreateUserPackageUserLicenseList = async (userCollection, props, callback) => {
  const { userIds, organizationId, skip, limit } = props;
  const batchSize = 1000;

  const cursor = userCollection
    .find({ guid: { $nin: userIds }, organizationId, active: { $ne: false } })
    .project({ _id: 0, guid: 1 })
    .limit(limit)
    .skip(skip);

  const _userIds = [];
  while (!cursor.isClosed()) {
    const user = await cursor.next();

    if (!user) {
      await cursor.close();
      break;
    }

    _userIds.push(user.guid);

    if (_userIds.length === batchSize) {
      await callback(_userIds);
      _userIds.splice(0, _userIds.length);
    }
  }

  if (_userIds.length > 0) {
    await callback(_userIds);
  }
};

/**
 * @param {Collection} planPackageLicenseCollection
 * @param {Array<PlanPackageLicense>} planPackageLicenses
 * @param {CollectionBulkWriteOptions} opts
 */
async function insertManyPlanPackageLicenses(planPackageLicenseCollection, planPackageLicenses, opts) {
  const entities = planPackageLicenses.map((planPackageLicense) =>
    instanceToPlain(planPackageLicense, { excludePrefixes: ['_'] }),
  );

  const insertPlanPackageLicenseOperations = entities.map((entity) => ({
    insertOne: {
      document: entity,
    },
  }));

  await planPackageLicenseCollection.bulkWrite(insertPlanPackageLicenseOperations, opts);
}

main();
