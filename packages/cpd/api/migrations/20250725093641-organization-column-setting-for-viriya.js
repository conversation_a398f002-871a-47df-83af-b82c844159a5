const { v4 } = require('uuid');
const { printHeader, runBackupCollection, printFinish } = require('./helper/utility');
const serviceName = '20250725093641-organization-column-setting-for-viriya';
const { Db, MongoClient } = require('mongodb');
const { map } = require('lodash');
const organizationCollectionName = 'organizations';
const columnSettingCollectionName = 'column-settings';
const organizationColumnSettingCollectionName = 'organization-column-settings';
const templateColumnSettingCollectionName = 'template-column-settings';

const domain = 'lms-web-dg80-viriya-pre-prod'; // เปลี่ยน domain ในแต่ละ env
const columnSettings = [
  {
    key: 'user.partnerName',
    name: 'หุ้นส่วนทางธุรกิจ',
    module: 'user',
    localeKey: 'user.partnerName',
    groupFields: [
      {
        root: 'additionalField',
        dataType: 'text',
        columnCode: 'partnerName',
        relationalPath: null,
        isFilter: true,
        isDisplay: true,
      },
    ],
    fieldType: 'additionalField',
    dropdownValue: [],
    filterType: 'text',
    columnBulkHeader: 'partner_name',
    dropdownValueType: 'static',
    dataType: 'text',
    isUnique: true,
    columnBulkCode: 'partnerName',
    isEditableFilter: true,
  },
  {
    key: 'user.onboardDate',
    name: 'วันที่เริ่มทำงาน',
    module: 'user',
    localeKey: 'user.onboardDate',
    groupFields: [
      {
        root: 'additionalField',
        dataType: 'date',
        columnCode: 'onboardDate',
        relationalPath: null,
        isFilter: true,
        isDisplay: true,
      },
    ],
    fieldType: 'additionalField',
    dropdownValue: [],
    filterType: 'dateRange',
    columnBulkHeader: 'onboard_date',
    createdAt: '2023-05-30T10:15:50.642Z',
    updatedAt: '2023-05-30T10:15:50.642Z',
    dropdownValueType: 'static',
    dataType: 'date',
    isUnique: false,
    columnBulkCode: 'onboardDate',
  },
  {
    key: 'user.salesId',
    name: 'เลขประจำตัวพนักงานขาย',
    module: 'user',
    localeKey: 'user.salesId',
    groupFields: [
      {
        root: 'additionalField',
        dataType: 'text',
        columnCode: 'salesId',
        relationalPath: null,
        isFilter: true,
        isDisplay: true,
      },
    ],
    fieldType: 'additionalField',
    dropdownValue: [],
    filterType: 'text',
    columnBulkHeader: 'sales_id',
    dropdownValueType: 'static',
    dataType: 'text',
    isUnique: false,
    columnBulkCode: 'salesId',
    isEditableFilter: true,
  },
  {
    key: 'user.regionDescription',
    name: 'ชื่อภาค',
    module: 'user',
    localeKey: 'user.regionDescription',
    dropdownValue: [],
    fieldType: 'additionalField',
    filterType: 'text',
    groupFields: [
      {
        columnCode: 'regionDescription',
        root: 'additionalField',
        dataType: 'text',
        isFilter: true,
        isDisplay: true,
      },
    ],
    dropdownValueType: 'static',
    columnBulkHeader: 'region_description',
    dataType: 'text',
    isUnique: false,
    columnBulkCode: 'regionDescription',
    isEditableFilter: true,
  },
  {
    key: 'user.branchName',
    name: ' ชื่อสาขา',
    module: 'user',

    localeKey: 'user.branchName',
    groupFields: [
      {
        root: 'additionalField',
        dataType: 'text',
        columnCode: 'branchName',
        relationalPath: null,
        isFilter: true,
        isDisplay: true,
      },
    ],
    fieldType: 'additionalField',
    dropdownValue: [],
    filterType: 'text',
    columnBulkHeader: 'branch_name',
    dropdownValueType: 'static',
    dataType: 'text',
    isUnique: false,
    columnBulkCode: 'branchName',
    isEditableFilter: true,
  },
  {
    key: 'user.branchCode',
    module: 'user',
    localeKey: 'user.branchCode',
    groupFields: [
      {
        root: 'additionalField',
        dataType: 'text',
        columnCode: 'branchCode',
        relationalPath: null,
        isFilter: true,
        isDisplay: true,
      },
    ],
    fieldType: 'additionalField',
    dropdownValue: [],
    filterType: 'text',
    columnBulkHeader: 'branch_code',
    dropdownValueType: 'static',
    dataType: 'text',
    isUnique: false,
    name: ' รหัสสาขา',
    columnBulkCode: 'branchCode',
    isEditableFilter: true,
  },
  {
    module: 'user',
    localeKey: 'user.qualification1',
    groupFields: [
      {
        root: 'additionalField',
        dataType: 'text',
        columnCode: 'qualification1',
        relationalPath: null,
        isFilter: true,
        isDisplay: true,
      },
    ],
    fieldType: 'additionalField',
    dropdownValue: [],
    filterType: 'text',
    columnBulkHeader: 'qualification1',

    dropdownValueType: 'static',
    key: 'user.qualification1',
    dataType: 'text',
    isUnique: false,
    name: 'คุณวุฒิที่ 1',
    columnBulkCode: 'qualification1',
    isEditableFilter: true,
  },
  {
    key: 'user.qualification2',
    name: 'คุณวุฒิที่ 2',
    module: 'user',
    localeKey: 'user.qualification2',
    groupFields: [
      {
        root: 'additionalField',
        dataType: 'text',
        columnCode: 'qualification2',
        relationalPath: null,
        isFilter: true,
        isDisplay: true,
      },
    ],
    fieldType: 'additionalField',
    dropdownValue: [],
    filterType: 'text',
    columnBulkHeader: 'qualification2',
    dropdownValueType: 'static',
    dataType: 'text',
    isUnique: false,
    columnBulkCode: 'qualification2',
    isEditableFilter: true,
  },
  {
    key: 'user.educationalQualification',
    name: 'วุฒิการศึกษา',
    module: 'user',
    localeKey: 'user.educationalQualification',
    groupFields: [
      {
        root: 'additionalField',
        dataType: 'text',
        columnCode: 'educationalQualification',
        relationalPath: null,
        isFilter: true,
        isDisplay: true,
      },
    ],
    fieldType: 'additionalField',
    dropdownValue: [],
    filterType: 'text',
    columnBulkHeader: 'educational_qualification',
    dropdownValueType: 'static',
    dataType: 'text',
    isUnique: false,
    columnBulkCode: 'educationalQualification',
    isEditableFilter: true,
  },
  {
    key: 'user.remark1',
    module: 'user',
    localeKey: 'user.remark1',
    groupFields: [
      {
        root: 'additionalField',
        dataType: 'text',
        columnCode: 'remark1',
        relationalPath: null,
        isFilter: true,
        isDisplay: true,
      },
    ],
    fieldType: 'additionalField',
    dropdownValue: [],
    filterType: 'text',
    columnBulkHeader: 'remark1',
    dropdownValueType: 'static',
    dataType: 'text',
    isUnique: false,
    name: 'หมายเหตุ 1',
    columnBulkCode: 'remark1',
    isEditableFilter: true,
  },
  {
    key: 'user.remark2',
    module: 'user',
    name: 'หมายเหตุ 2',
    localeKey: 'user.remark2',
    groupFields: [
      {
        root: 'additionalField',
        dataType: 'text',
        columnCode: 'remark2',
        relationalPath: null,
        isFilter: true,
        isDisplay: true,
      },
    ],
    fieldType: 'additionalField',
    dropdownValue: [],
    filterType: 'text',
    columnBulkHeader: 'remark2',
    dropdownValueType: 'static',
    dataType: 'text',
    isUnique: false,
    columnBulkCode: 'remark2',
    isEditableFilter: true,
  },
  {
    key: 'user.employmentGroup',
    name: 'กลุ่มพนักงาน',
    module: 'user',
    localeKey: 'user.employmentGroup',
    dropdownValue: [],
    fieldType: 'additionalField',
    filterType: 'text',
    groupFields: [
      {
        columnCode: 'employmentGroup',
        root: 'additionalField',
        dataType: 'text',
        relationalPath: null,
        isFilter: true,
        isDisplay: true,
      },
    ],
    dropdownValueType: 'sync',
    columnBulkHeader: 'employment_group',
    dataType: 'text',
    isUnique: false,
    columnBulkCode: 'employmentGroup',
    isEditableFilter: true,
  },
  {
    key: 'user.nextRenewalTime',
    name: 'ต่อใบอนุญาตครั้งที่',
    module: 'user',
    localeKey: 'user.nextRenewalTime',
    fieldType: 'additionalField',
    filterType: 'multipleSelector',
    groupFields: [
      {
        columnCode: 'nextRenewalTime',
        root: 'additionalField',
        dataType: 'enum',
        relationalPath: null,
        isFilter: true,
        isDisplay: true,
      },
    ],
    dropdownValueType: 'static',
    columnBulkHeader: 'next_renewal_time',
    dataType: 'text',
    isUnique: false,
    columnBulkCode: 'nextRenewalTime',
    isEditableFilter: true,
    dropdownValue: [
      {
        value: '0',
        label: 'ขอรับใบอนุญาต',
      },
      {
        value: '1',
        label: 'ต่ออายุครั้งที่ 1',
      },
      {
        value: '2',
        label: 'ต่ออายุครั้งที่ 2',
      },
      {
        value: '3',
        label: 'ต่ออายุครั้งที่ 3',
      },
      {
        value: '4',
        label: 'ต่ออายุครั้งที่ 4',
      },
    ],
  },
  {
    module: 'user',
    name: 'สิทธิลดหย่อน',
    key: 'user.oicDeduct',
    localeKey: 'user.oicDeduct',
    dropdownValue: [],
    fieldType: 'additionalField',
    filterType: 'text',
    groupFields: [
      {
        columnCode: 'oicDeduct',
        root: 'additionalField',
        dataType: 'text',
        relationalPath: null,
        isFilter: true,
        isDisplay: true,
      },
    ],
    dropdownValueType: 'static',
    columnBulkHeader: 'oic_deduct',
    dataType: 'text',
    isUnique: false,
    columnBulkCode: 'oicDeduct',
    isEditableFilter: true,
  },
  {
    key: 'user.upline',
    name: 'Upline',
    module: 'user',
    localeKey: 'user.upline',
    dropdownValue: [],
    fieldType: 'additionalField',
    filterType: 'text',
    groupFields: [
      {
        columnCode: 'upline',
        root: 'additionalField',
        dataType: 'text',
        relationalPath: null,
        isFilter: true,
        isDisplay: true,
      },
    ],
    dropdownValueType: 'static',
    columnBulkHeader: 'upline',
    dataType: 'text',
    isUnique: false,
    columnBulkCode: 'upline',
    isEditableFilter: true,
  },
];

const jobTransactionColumnSettings = [
  {
    key: 'job_transaction.user.partnerName',
    name: 'หุ้นส่วนทางธุรกิจ',
    module: 'jobTransaction',
    localeKey: 'user.partnerName',
    groupFields: [
      {
        root: 'payload.user.additionalField',
        dataType: 'text',
        columnCode: 'partnerName',
        relationalPath: null,
        isFilter: true,
        isDisplay: true,
      },
    ],
    fieldType: 'additionalField',
    dropdownValue: [],
    filterType: 'text',
    dropdownValueType: 'static',
    dataType: 'text',
    isUnique: true,
    columnBulkHeader: '',
    columnBulkCode: '',
    isEditableFilter: true,
  },
  {
    key: 'job_transaction.user.onboardDate',
    name: 'วันที่เริ่มทำงาน',
    module: 'jobTransaction',
    localeKey: 'user.onboardDate',
    groupFields: [
      {
        root: 'payload.user.additionalField',
        dataType: 'date',
        columnCode: 'onboardDate',
        relationalPath: null,
        isFilter: true,
        isDisplay: true,
      },
    ],
    fieldType: 'additionalField',
    dropdownValue: [],
    filterType: 'dateRange',
    createdAt: '2023-05-30T10:15:50.642Z',
    updatedAt: '2023-05-30T10:15:50.642Z',
    dropdownValueType: 'static',
    dataType: 'date',
    isUnique: false,
    columnBulkHeader: '',
    columnBulkCode: '',
  },
  {
    key: 'job_transaction.user.salesId',
    name: 'เลขประจำตัวพนักงานขาย',
    module: 'jobTransaction',
    localeKey: 'user.salesId',
    groupFields: [
      {
        root: 'payload.user.additionalField',
        dataType: 'text',
        columnCode: 'salesId',
        relationalPath: null,
        isFilter: true,
        isDisplay: true,
      },
    ],
    fieldType: 'additionalField',
    dropdownValue: [],
    filterType: 'text',
    columnBulkHeader: 'sales_id',
    dropdownValueType: 'static',
    dataType: 'text',
    isUnique: false,
    columnBulkCode: 'salesId',
    isEditableFilter: true,
  },
  {
    key: 'job_transaction.user.regionDescription',
    name: 'ชื่อภาค',
    module: 'jobTransaction',
    localeKey: 'user.regionDescription',
    dropdownValue: [],
    fieldType: 'additionalField',
    filterType: 'text',
    groupFields: [
      {
        root: 'payload.user.additionalField',
        columnCode: 'regionDescription',
        dataType: 'text',
        isFilter: true,
        isDisplay: true,
      },
    ],
    dropdownValueType: 'static',
    dataType: 'text',
    isUnique: false,
    columnBulkCode: '',
    columnBulkHeader: '',
    isEditableFilter: true,
  },
  {
    key: 'job_transaction.user.branchName',
    name: ' ชื่อสาขา',
    module: 'jobTransaction',
    localeKey: 'user.branchName',
    groupFields: [
      {
        root: 'payload.user.additionalField',
        dataType: 'text',
        columnCode: 'branchName',
        relationalPath: null,
        isFilter: true,
        isDisplay: true,
      },
    ],
    fieldType: 'additionalField',
    dropdownValue: [],
    filterType: 'text',
    dropdownValueType: 'static',
    dataType: 'text',
    isUnique: false,
    columnBulkHeader: '',
    columnBulkCode: '',
    isEditableFilter: true,
  },
  {
    key: 'job_transaction.user.branchCode',
    module: 'jobTransaction',
    name: ' รหัสสาขา',
    localeKey: 'user.branchCode',
    groupFields: [
      {
        root: 'payload.user.additionalField',
        dataType: 'text',
        columnCode: 'branchCode',
        relationalPath: null,
        isFilter: true,
        isDisplay: true,
      },
    ],
    fieldType: 'additionalField',
    dropdownValue: [],
    filterType: 'text',

    dropdownValueType: 'static',
    dataType: 'text',
    isUnique: false,
    columnBulkCode: '',
    columnBulkHeader: '',
    isEditableFilter: true,
  },
  {
    module: 'jobTransaction',
    name: 'คุณวุฒิที่ 1',
    key: 'job_transaction.user.qualification1',
    localeKey: 'user.qualification1',
    groupFields: [
      {
        root: 'payload.user.additionalField',
        dataType: 'text',
        columnCode: 'qualification1',
        relationalPath: null,
        isFilter: true,
        isDisplay: true,
      },
    ],
    fieldType: 'additionalField',
    dropdownValue: [],
    filterType: 'text',
    dropdownValueType: 'static',
    dataType: 'text',
    isUnique: false,
    columnBulkHeader: '',
    columnBulkCode: '',
    isEditableFilter: true,
  },
  {
    key: 'job_transaction.user.qualification2',
    name: 'คุณวุฒิที่ 2',
    module: 'jobTransaction',
    localeKey: 'user.qualification2',
    groupFields: [
      {
        root: 'payload.user.additionalField',
        dataType: 'text',
        columnCode: 'qualification2',
        relationalPath: null,
        isFilter: true,
        isDisplay: true,
      },
    ],
    fieldType: 'additionalField',
    dropdownValue: [],
    filterType: 'text',
    dropdownValueType: 'static',
    dataType: 'text',
    isUnique: false,
    columnBulkHeader: '',
    columnBulkCode: '',
    isEditableFilter: true,
  },
  {
    key: 'job_transaction.user.educationalQualification',
    name: 'วุฒิการศึกษา',
    module: 'jobTransaction',
    localeKey: 'user.educationalQualification',
    groupFields: [
      {
        root: 'payload.user.additionalField',
        dataType: 'text',
        columnCode: 'educationalQualification',
        relationalPath: null,
        isFilter: true,
        isDisplay: true,
      },
    ],
    fieldType: 'additionalField',
    dropdownValue: [],
    filterType: 'text',

    dropdownValueType: 'static',
    dataType: 'text',
    isUnique: false,
    columnBulkHeader: '',
    columnBulkCode: '',
    isEditableFilter: true,
  },
  {
    key: 'job_transaction.user.remark1',
    module: 'jobTransaction',
    localeKey: 'user.remark1',
    groupFields: [
      {
        root: 'payload.user.additionalField',
        dataType: 'text',
        columnCode: 'remark1',
        relationalPath: null,
        isFilter: true,
        isDisplay: true,
      },
    ],
    fieldType: 'additionalField',
    dropdownValue: [],
    filterType: 'text',
    dropdownValueType: 'static',
    dataType: 'text',
    isUnique: false,
    name: 'หมายเหตุ 1',
    columnBulkHeader: '',
    columnBulkCode: '',
    isEditableFilter: true,
  },
  {
    key: 'job_transaction.user.remark2',
    module: 'jobTransaction',
    name: 'หมายเหตุ 2',
    localeKey: 'user.remark2',
    groupFields: [
      {
        root: 'payload.user.additionalField',
        dataType: 'text',
        columnCode: 'remark2',
        relationalPath: null,
        isFilter: true,
        isDisplay: true,
      },
    ],
    fieldType: 'additionalField',
    dropdownValue: [],
    filterType: 'text',
    dropdownValueType: 'static',
    dataType: 'text',
    isUnique: false,
    columnBulkHeader: '',
    columnBulkCode: '',
    isEditableFilter: true,
  },
  {
    key: 'job_transaction.user.employmentGroup',
    name: 'กลุ่มพนักงาน',
    module: 'jobTransaction',
    localeKey: 'user.employmentGroup',
    dropdownValue: [],
    fieldType: 'additionalField',
    filterType: 'text',
    groupFields: [
      {
        root: 'payload.user.additionalField',
        columnCode: 'employmentGroup',
        dataType: 'text',
        relationalPath: null,
        isFilter: true,
        isDisplay: true,
      },
    ],
    dropdownValueType: 'sync',
    dataType: 'text',
    isUnique: false,
    columnBulkHeader: '',
    columnBulkCode: '',
    isEditableFilter: true,
  },
  {
    key: 'job_transaction.user.nextRenewalTime',
    name: 'ต่อใบอนุญาตครั้งที่',
    module: 'jobTransaction',
    localeKey: 'user.nextRenewalTime',
    fieldType: 'additionalField',
    filterType: 'multipleSelector',
    groupFields: [
      {
        root: 'payload.user.additionalField',
        columnCode: 'nextRenewalTime',
        dataType: 'enum',
        relationalPath: null,
        isFilter: true,
        isDisplay: true,
      },
    ],
    dropdownValueType: 'static',
    dataType: 'text',
    isUnique: false,
    columnBulkHeader: '',
    columnBulkCode: '',
    isEditableFilter: true,
    dropdownValue: [
      {
        value: '0',
        label: 'ขอรับใบอนุญาต',
      },
      {
        value: '1',
        label: 'ต่ออายุครั้งที่ 1',
      },
      {
        value: '2',
        label: 'ต่ออายุครั้งที่ 2',
      },
      {
        value: '3',
        label: 'ต่ออายุครั้งที่ 3',
      },
      {
        value: '4',
        label: 'ต่ออายุครั้งที่ 4',
      },
    ],
  },
  {
    module: 'jobTransaction',
    name: 'สิทธิลดหย่อน',
    key: 'job_transaction.user.oicDeduct',
    localeKey: 'user.oicDeduct',
    dropdownValue: [],
    fieldType: 'additionalField',
    filterType: 'text',
    groupFields: [
      {
        root: 'payload.user.additionalField',
        columnCode: 'oicDeduct',
        dataType: 'text',
        relationalPath: null,
        isFilter: true,
        isDisplay: true,
      },
    ],
    dropdownValueType: 'static',
    dataType: 'text',
    isUnique: false,
    columnBulkCode: '',
    columnBulkHeader: '',
    isEditableFilter: true,
  },
  {
    key: 'job_transaction.user.upline',
    name: 'Upline',
    module: 'jobTransaction',
    localeKey: 'user.upline',
    dropdownValue: [],
    fieldType: 'additionalField',
    filterType: 'text',
    groupFields: [
      {
        root: 'payload.user.additionalField',
        columnCode: 'upline',
        dataType: 'text',
        relationalPath: null,
        isFilter: true,
        isDisplay: true,
      },
    ],
    dropdownValueType: 'static',
    dataType: 'text',
    isUnique: false,
    columnBulkHeader: '',
    columnBulkCode: '',
    isEditableFilter: true,
  },
];

const columnSettingInTemplates = [
  {
    key: 'user.partnerName',
    name: 'หุ้นส่วนทางธุรกิจ',
    isDefault: false,
    isFilter: true,
    isRequired: false,
    isActive: true,
    isEditableFilter: true,
  },
  {
    key: 'user.onboardDate',
    name: 'วันที่เริ่มทำงาน',
    isDefault: false,
    isFilter: true,
    isRequired: false,
    isActive: true,
    isEditableFilter: true,
  },
  {
    key: 'user.salesId',
    name: 'เลขประจำตัวพนักงานขาย',
    isDefault: false,
    isFilter: true,
    isRequired: false,
    isActive: true,
    isEditableFilter: true,
  },
  {
    key: 'user.regionDescription',
    name: 'ชื่อภาค',
    isDefault: false,
    isFilter: true,
    isRequired: false,
    isActive: true,
    isEditableFilter: true,
  },
  {
    key: 'user.branchName',
    name: 'ชื่อสาขา',
    isDefault: false,
    isFilter: true,
    isRequired: false,
    isActive: true,
    isEditableFilter: true,
  },
  {
    key: 'user.branchCode',
    name: 'รหัสสาขา',
    isDefault: false,
    isFilter: true,
    isRequired: false,
    isActive: true,
    isEditableFilter: true,
  },
  {
    key: 'user.qualification1',
    name: 'คุณวุฒิที่ 1',
    isDefault: false,
    isFilter: true,
    isRequired: false,
    isActive: true,
    isEditableFilter: true,
  },
  {
    key: 'user.qualification2',
    name: 'คุณวุฒิที่ 2',
    isDefault: false,
    isFilter: true,
    isRequired: false,
    isActive: true,
    isEditableFilter: true,
  },
  {
    key: 'user.educationalQualification',
    name: 'วุฒิการศึกษา',
    isDefault: false,
    isFilter: true,
    isRequired: false,
    isActive: true,
    isEditableFilter: true,
  },
  {
    key: 'user.remark1',
    name: 'หมายเหตุ 1',
    isDefault: false,
    isFilter: true,
    isRequired: false,
    isActive: true,
    isEditableFilter: true,
  },
  {
    key: 'user.remark2',
    name: 'หมายเหตุ 2',
    isDefault: false,
    isFilter: true,
    isRequired: false,
    isActive: true,
    isEditableFilter: true,
  },
  {
    key: 'user.employmentGroup',
    name: 'กลุ่มพนักงาน',
    isDefault: false,
    isFilter: true,
    isRequired: false,
    isActive: true,
    isEditableFilter: true,
  },
  {
    key: 'user.nextRenewalTime',
    name: 'ต่อใบอนุญาตครั้งที่',
    isDefault: false,
    isFilter: true,
    isRequired: false,
    isActive: true,
    isEditableFilter: true,
  },
  {
    key: 'user.oicDeduct',
    name: 'สิทธิลดหย่อน',
    isDefault: false,
    isFilter: true,
    isRequired: false,
    isActive: true,
    isEditableFilter: true,
  },
  {
    key: 'user.upline',
    name: 'upline',
    isDefault: false,
    isFilter: true,
    isRequired: false,
    isActive: true,
    isEditableFilter: true,
  },
];

const jobTransactionColumnSettingInTemplates = [
  {
    key: 'job_transaction.user.partnerName',
    name: 'หุ้นส่วนทางธุรกิจ',
    isDefault: false,
    isFilter: true,
    isRequired: false,
    isActive: true,
    isEditableFilter: true,
  },
  {
    key: 'job_transaction.user.onboardDate',
    name: 'วันที่เริ่มทำงาน',
    isDefault: false,
    isFilter: true,
    isRequired: false,
    isActive: true,
    isEditableFilter: true,
  },
  {
    key: 'job_transaction.user.salesId',
    name: 'เลขประจำตัวพนักงานขาย',
    isDefault: false,
    isFilter: true,
    isRequired: false,
    isActive: true,
    isEditableFilter: true,
  },
  {
    key: 'job_transaction.user.regionDescription',
    name: 'ชื่อภาค',
    isDefault: false,
    isFilter: true,
    isRequired: false,
    isActive: true,
    isEditableFilter: true,
  },
  {
    key: 'job_transaction.user.branchName',
    name: 'ชื่อสาขา',
    isDefault: false,
    isFilter: true,
    isRequired: false,
    isActive: true,
    isEditableFilter: true,
  },
  {
    key: 'job_transaction.user.branchCode',
    name: 'รหัสสาขา',
    isDefault: false,
    isFilter: true,
    isRequired: false,
    isActive: true,
    isEditableFilter: true,
  },
  {
    key: 'job_transaction.user.qualification1',
    name: 'คุณวุฒิที่ 1',
    isDefault: false,
    isFilter: true,
    isRequired: false,
    isActive: true,
    isEditableFilter: true,
  },
  {
    key: 'job_transaction.user.qualification2',
    name: 'คุณวุฒิที่ 2',
    isDefault: false,
    isFilter: true,
    isRequired: false,
    isActive: true,
    isEditableFilter: true,
  },
  {
    key: 'job_transaction.user.educationalQualification',
    name: 'วุฒิการศึกษา',
    isDefault: false,
    isFilter: true,
    isRequired: false,
    isActive: true,
    isEditableFilter: true,
  },
  {
    key: 'job_transaction.user.remark1',
    name: 'หมายเหตุ 1',
    isDefault: false,
    isFilter: true,
    isRequired: false,
    isActive: true,
    isEditableFilter: true,
  },
  {
    key: 'job_transaction.user.remark2',
    name: 'หมายเหตุ 2',
    isDefault: false,
    isFilter: true,
    isRequired: false,
    isActive: true,
    isEditableFilter: true,
  },
  {
    key: 'job_transaction.user.employmentGroup',
    name: 'กลุ่มพนักงาน',
    isDefault: false,
    isFilter: true,
    isRequired: false,
    isActive: true,
    isEditableFilter: true,
  },
  {
    key: 'job_transaction.user.nextRenewalTime',
    name: 'ต่อใบอนุญาตครั้งที่',
    isDefault: false,
    isFilter: true,
    isRequired: false,
    isActive: true,
    isEditableFilter: true,
  },
  {
    key: 'job_transaction.user.oicDeduct',
    name: 'สิทธิลดหย่อน',
    isDefault: false,
    isFilter: true,
    isRequired: false,
    isActive: true,
    isEditableFilter: true,
  },
  {
    key: 'job_transaction.user.upline',
    name: 'upline',
    isDefault: false,
    isFilter: true,
    isRequired: false,
    isActive: true,
    isEditableFilter: true,
  },
];

module.exports = {
  /**
   *
   * @param {Db} db
   * @param {MongoClient} client
   */
  async up(db, client) {
    printHeader(serviceName);

    await runBackupCollection(db, organizationColumnSettingCollectionName);
    await runBackupCollection(db, templateColumnSettingCollectionName);

    const columnSettingCollection = db.collection(columnSettingCollectionName);
    const organizationCollection = db.collection(organizationCollectionName);
    const organizationColumnSettingCollection = db.collection(organizationColumnSettingCollectionName);
    const templateColumnSettingCollection = db.collection(templateColumnSettingCollectionName);

    const session = client.startSession();
    session.startTransaction();

    const opt = { session };

    const organization = await organizationCollection.findOne({ domain: domain }, { projection: { id: 1 } });

    if (organization) {
      const organizationId = organization.id;

      const currentDate = new Date();
      const organizationColumnSettings = [...columnSettings, ...jobTransactionColumnSettings].map((column) => ({
        ...column,
        id: v4(),
        organizationId,
        createdAt: currentDate,
        updatedAt: currentDate,
      }));

      const updateOrInsertOrganizationColumnSettingOperations = organizationColumnSettings.map((column) => ({
        updateOne: {
          filter: { organizationId: column.organizationId, key: column.key },
          update: { $set: column },
          upsert: true,
        },
      }));

      await organizationColumnSettingCollection.bulkWrite(updateOrInsertOrganizationColumnSettingOperations, opt);

      const columnSettingTemplates = await templateColumnSettingCollection
        .find({
          organizationId,
          code: {
            $in: [
              // User
              'userManagement',
              'bulkActivateUser',
              'bulkEditUser',
              // User Group
              'userMapUserGroupManagement',
              'userGroupConditionManagement',
              // Classroom
              'transferClassroomUserManagement',
              'attendanceClassroomManagement',
              // My Team
              'memberManagement',
              'subordinateManagement',
              'assignContentManagement',
            ],
          },
        })
        .project({ id: 1, columnSetting: 1, code: 1 })
        .toArray();

      const existingColumnKeyInTemplates = columnSettingTemplates
        .flatMap((template) => template.columnSetting)
        .map((col) => col.key);

      const primaryColumnSettings = await columnSettingCollection
        .find({ key: { $in: existingColumnKeyInTemplates } })
        .project({ _id: 0, key: 1 })
        .toArray();

      const primaryColumnSettingKeys = primaryColumnSettings.map((col) => col.key);

      for (const columnSettingTemplate of columnSettingTemplates) {
        const existColumnSetting = columnSettingTemplate.columnSetting.filter((col) =>
          primaryColumnSettingKeys.includes(col.key),
        );

        const size = existColumnSetting.length;

        const isAssignContentManagement = columnSettingTemplate.code === 'assignContentManagement';
        const newColumnSettings = isAssignContentManagement
          ? jobTransactionColumnSettingInTemplates
          : columnSettingInTemplates;

        const newOrderedColumnSettings = newColumnSettings.map((col, index) => ({ ...col, order: index + size }));
        await templateColumnSettingCollection.updateOne(
          { id: columnSettingTemplate.id, organizationId },
          {
            $set: {
              columnSetting: [...existColumnSetting, ...newOrderedColumnSettings],
              updatedAt: currentDate,
            },
          },
          opt,
        );
      }
    }
    await session.commitTransaction();
    session.endSession();
    printFinish(serviceName);
  },

  async down(db, client) {},
};
