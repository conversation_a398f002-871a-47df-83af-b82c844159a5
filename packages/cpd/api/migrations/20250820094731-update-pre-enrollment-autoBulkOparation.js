const { printHeader, printFinish, runBackupCollection, chunkBulkWrite } = require('./helper/utility');
const organizationDomains = require('./helper/organizationDomains');

const serviceName = 'update-pre-enrollment-autoBulkOparation';
const organizationCollection = 'organizations';
const preEnrollmentReservationCollection = 'pre-enrollment-reservations';
const preEnrollmentTransactionCollection = 'pre-enrollment-transactions';

module.exports = {
  async up(db, client) {
    printHeader(serviceName);

    await runBackupCollection(db, preEnrollmentTransactionCollection);

    const organization = await db
      .collection(organizationCollection)
      .findOne({ domain: { $in: organizationDomains.CPD } });

    const organizationId = organization.id;

    const preEnrollmentReservationList = await db
      .collection(preEnrollmentReservationCollection)
      .aggregate([
        {
          $match: {
            status: 'PASSED',
            organizationId,
          },
        },
        {
          $project: {
            id: 1,
          },
        },
      ])
      .toArray();

    const preEnrollmentReservationIds = preEnrollmentReservationList.map((val) => val.id);

    const aggregateParams = [
      {
        $match: {
          organizationId,
          $or: [
            {
              enrollBy: 'admin',
              preEnrollmentReservationId: { $in: preEnrollmentReservationIds },
            },
            {
              enrollBy: 'self',
              status: 'PASSED',
            },
          ],
          'operationExecute.isAssginPlanPackageLicense': { $exists: false },
        },
      },
      {
        $project: {
          id: 1,
          jobId: 1,
          organizationId: 1,
          status: 1,
        },
      },
    ];

    const datas = await db.collection(preEnrollmentTransactionCollection).aggregate(aggregateParams).toArray();

    console.info(`******  found pre-enrollment-transactions ${datas.length} records ******`);

    const updateOperations = [];

    for (const data of datas) {
      updateOperations.push({
        updateOne: {
          filter: { id: data.id },
          update: {
            $set: {
              'operationExecute.isAssginPlanPackageLicense': false,
              autoBulkOparation: [
                'activate',
                'edit_user_bulk',
                'enrollment',
                'enrollment_bundle',
                'assign_plan_package_license',
              ],
            },
          },
        },
      });
    }

    if (updateOperations.length > 0) {
      await chunkBulkWrite(db, preEnrollmentTransactionCollection, updateOperations);
      console.info(`****** Updated pre-enrollment-transactions ${updateOperations.length} records ******`);
    }

    printFinish(serviceName);
  },

  async down(db, client) {},
};
