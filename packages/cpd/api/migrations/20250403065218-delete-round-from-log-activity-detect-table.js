const { printHeader, printFinish, runBackupCollection } = require('./helper/utility');
const logActivityDetectCollection = 'logs-activity-detect';

const serviceName = 'delete-round-from-log-activity-detect-table';

module.exports = {
  async up(db, client) {
    printHeader(serviceName);

    const operations = [
      {
        updateMany: {
          filter: {},
          update: {
            $unset: {
              round: 1,
            },
          },
        },
      },
    ];

    if (operations.length > 0) {
      await db.collection(logActivityDetectCollection).bulkWrite(operations);
    }

    printFinish(serviceName);
  },

  async down(db, client) {},
};
