import { useState } from 'react';

import Icon, { CloseCircleOutlined, DeleteOutlined, PaperClipOutlined } from '@ant-design/icons';
import { Alert, Button, Divider, Form, Tooltip, Upload } from 'antd';
import { arrayOf, func, object, string } from 'prop-types';
import { useTheme } from 'styled-components';

import Image from '@components/atoms/image';
import { useLocaleText } from '@helpers/hooks';

import { FileUploadStyle } from './style';

const MAX_FILE_SIZE_BYTES = 31_457_280; // 30 Megabytes = 31457280 Bytes

const FileUploader = (props) => {
  const { srcList, onChange, acceptableFileExtensions } = props;
  const [isError, setIsError] = useState(false);
  const { token } = useTheme();
  const { getLocaleText } = useLocaleText();
  const UploadListItemComponent = ({ file, actions, isLastItem, isFileError }) => (
    <Tooltip placement="top" title={isFileError ? 'พบข้อผิดพลาดในการอัปโหลด' : ''}>
      <div className="upload-list-item">
        <PaperClipOutlined
          className={`upload-list-item-prefix-icon ${isFileError ? 'ant-upload-list-item-error' : ''}`}
        />
        <div className={`${isFileError ? 'upload-list-item-text-error' : 'upload-list-item-text'}`}>{file.name}</div>
        <Button danger className="upload-list-item-delete-button" onClick={() => actions.remove()}>
          <DeleteOutlined />
        </Button>
      </div>
      {!isLastItem && <Divider style={{ margin: 0 }} />}
    </Tooltip>
  );

  function isValidFileSizeChecker(size) {
    return size && size <= MAX_FILE_SIZE_BYTES;
  }

  function isValidFileExtChecker(fileName) {
    return fileName && acceptableFileExtensions.includes(fileName.toLowerCase().split('.').pop());
  }

  const uploadConfig = {
    fileList: srcList,
    name: 'files',
    accept: acceptableFileExtensions.join(','),
    multiple: true,
    action: '/api/upload',
    onChange,
    beforeUpload: (file, fileList) => {
      const isValidFileSize = isValidFileSizeChecker(file.size);
      const isValidFileExt = isValidFileExtChecker(file?.name);
      const isValidFile = isValidFileSize && isValidFileExt;
      const isError = !isValidFile;
      setIsError(isError);
      return isValidFile || Upload.LIST_IGNORE;
    },
    style: { marginBottom: 8 },
    itemRender: (_originNode, file, _fileList, actions) => {
      const isValidFileSize = !file.size || isValidFileSizeChecker(file.size);
      const isValidFileExt = isValidFileExtChecker(file?.name);
      const isValidFile = isValidFileSize && isValidFileExt;
      if (_fileList?.length === 0) {
        setIsError(false);
      }
      return (
        <UploadListItemComponent
          file={file}
          actions={actions}
          isLastItem={file?.uid === _fileList[_fileList?.length - 1]?.uid}
          isFileError={!isValidFile}
        />
      );
    },
  };

  return (
    <FileUploadStyle token={token}>
      {isError && (
        <Form.Item>
          <Alert
            showIcon
            icon={<Icon component={CloseCircleOutlined} style={{ fontSize: token.fontSizeSM }} />}
            closeable
            message={getLocaleText('components.drawer.article.attachments_error')}
            type="error"
          />
        </Form.Item>
      )}

      <Form.Item noStyle>
        <Upload.Dragger {...uploadConfig}>
          <div className="upload-placeholder-image">
            <Image src="/static/images/upload-file.svg" alt="upload-file" />
          </div>
          <div className="upload-placeholder-title" style={{ marginTop: token.margin }}>
            {getLocaleText('components.drawer.article.select_or_drop_file')}
          </div>
          <div className="upload-placeholder-description" style={{ marginTop: token.marginXXS }}>
            {getLocaleText('components.drawer.article.format_file_and_size', null, null, { acceptableFileExtensions })}
          </div>
        </Upload.Dragger>
      </Form.Item>
    </FileUploadStyle>
  );
};
FileUploader.defaultProps = {
  srcList: [],
  onChange: () => {},
  acceptableFileExtensions: ['doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx', 'zip', 'rar', 'pdf', 'jpeg', 'jpg', 'png'],
};
FileUploader.propTypes = {
  srcList: arrayOf(object),
  onChange: func,
  acceptableFileExtensions: arrayOf(string),
};
export default FileUploader;
