import React, { useEffect, useMemo, useRef, useState } from 'react';

import { Col, Form, Row } from 'antd';
import { useDispatch, useSelector } from 'react-redux';

import Card from '@components/atoms/card';
import ConfirmModal from '@components/atoms/confirmModalV2';
import FullScreenDrawer from '@components/molecules/fullScreenDrawerV2';
import { DataTypeEnum } from '@constants/columnSetting';
import { date } from '@helpers/date';
import { useLocaleText } from '@helpers/hooks';
import appActions from '@redux/app/actions';
import { appStore } from '@redux/app/state';
import userActions from '@redux/user/actions';
import userActionsV2 from '@redux/userV2/actions';
import { userStore } from '@redux/userV2/state';

import InputAdditionalFieldFactory from './inputAdditionalFieldFactory';

const { updateUserManagementInfoClientAction } = userActions;
const { updateUserAdditionalAction } = userActionsV2;
const { manageUserDetailDrawerAction } = appActions;

const EditUserAdditionalDrawer = ({ userId, additionalColumnSettings, additionalField, isOpen, onClose }) => {
  const [form] = Form.useForm();

  const dispatch = useDispatch();
  const { getLocaleText } = useLocaleText();
  const [isOpenDiscardModal, setIsOpenDiscardModal] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [initialData, setInitialData] = useState({});

  const { adminManageUser, updateUserAdditional, manageUserDetailDrawer } = useSelector((state) => ({
    adminManageUser: state.User.get('adminManageUser'),
    updateUserAdditional: state.UserV2.get(userStore.updateUserAdditional),
    manageUserDetailDrawer: state.App.get(appStore.manageUserDetailDrawer),
  }));

  const {
    isSuccess: isSuccessUpdateUserAdditional,
    isError: isErrorUpdateUserAdditional,
    data: updateUserAdditionalData,
  } = updateUserAdditional;

  const onFinish = (data) => {
    const payload = {
      userId,
      data,
    };
    dispatch(updateUserAdditionalAction.request(payload));
  };

  const onClickSubmit = () => {
    setIsLoading(true);
    form
      .validateFields()
      .then(() => {
        form.submit();
      })
      .catch(() => {
        setIsLoading(false);
      });
  };

  const onCloseDrawer = () => {
    dispatch(updateUserAdditionalAction.reset());
    onClose();
  };

  const onClickDiscard = () => {
    setIsOpenDiscardModal(true);
  };

  const onDiscardConfirm = () => {
    setIsOpenDiscardModal(false);
    dispatch(manageUserDetailDrawerAction.closeEditUserAdditional());
    onCloseDrawer();
  };

  const initialValue = () => {
    const result = {};
    for (const data of additionalColumnSettings) {
      const { groupFields } = data;
      if (!groupFields.length) return;
      for (const field of groupFields) {
        if (field.dataType === DataTypeEnum.DATE && field.columnCode in additionalField) {
          result[field.columnCode] = additionalField[field.columnCode] ? date(additionalField[field.columnCode]) : null;
        } else {
          result[field.columnCode] = additionalField[field.columnCode] || null;
        }
      }
    }

    setInitialData(result);
    form.setFieldsValue(result);
  };

  const additionalFieldsRef = useRef([]);

  const additionalFieldsComponents = useMemo(() => {
    if (!additionalColumnSettings.length) return <div />;
    return additionalColumnSettings.map((fieldData, index) => {
      const { module, id, groupFields, dropdownValue } = fieldData;
      const key = `${module}-${id}`;
      const formName = `${id}.${groupFields
        .filter((groupField) => groupField.isDisplay)
        .map((groupField) => groupField.columnCode)
        .join('.')}`;

      return (
        <Col xs={24} md={12} key={key}>
          <InputAdditionalFieldFactory
            ref={(_ref) => {
              additionalFieldsRef.current[index] = _ref;
            }}
            fieldData={fieldData}
            formName={formName}
            dropdownValue={dropdownValue}
          />
        </Col>
      );
    });
  }, [additionalColumnSettings]);

  useEffect(() => {
    if (!isSuccessUpdateUserAdditional) return;
    const updateUserData = {
      ...adminManageUser.data.user,
      additionalField: updateUserAdditionalData,
    };
    dispatch(updateUserManagementInfoClientAction.update(updateUserData));
    dispatch(manageUserDetailDrawerAction.closeEditUserAdditional());
    onCloseDrawer();
  }, [isSuccessUpdateUserAdditional, adminManageUser]);

  useEffect(() => {
    if (!isErrorUpdateUserAdditional) return;
    setIsLoading(false);
  }, [isErrorUpdateUserAdditional]);

  useEffect(() => {
    if (!additionalField || !isOpen || !additionalColumnSettings.length) return;
    if (!manageUserDetailDrawer.openEditUserAdditional) {
      dispatch(manageUserDetailDrawerAction.openEditUserAdditional());
      initialValue();
    }
  }, [additionalField, isOpen, additionalColumnSettings]);

  useEffect(() => {
    if (!isOpen) return;
    setIsLoading(false);
  }, [isOpen]);

  return (
    <>
      <FullScreenDrawer
        title={getLocaleText('c.drawer.title.user.edit_additional')}
        visible={isOpen}
        onClose={onCloseDrawer}
        onSubmit={onClickSubmit}
        onDiscard={onClickDiscard}
        isLoadingSubmitButton={isLoading}
      >
        <Form form={form} layout="vertical" onFinish={onFinish} style={{ width: '100%' }}>
          <Card style={{ padding: 24 }} textTitle={getLocaleText('c.card.title.user.additional')}>
            <Row gutter={[16, 24]} align="start">
              {additionalFieldsComponents}
            </Row>
          </Card>
        </Form>
      </FullScreenDrawer>

      <ConfirmModal
        title={getLocaleText('c.modal.discard.title')}
        content={getLocaleText('c.modal.discard.body')}
        open={isOpenDiscardModal}
        cancelText={getLocaleText('c.btn.cancel')}
        okText={getLocaleText('c.btn.discard')}
        onOk={onDiscardConfirm}
        onCancel={() => setIsOpenDiscardModal(false)}
      />
    </>
  );
};

export default EditUserAdditionalDrawer;
