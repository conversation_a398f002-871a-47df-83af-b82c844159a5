import React, { forwardRef } from 'react';

import { Form, Input, Select } from 'antd';

import CustomDatePicker from '@components/atoms/customDatePicker';
import { DataTypeEnum } from '@constants/columnSetting';
import { DateFormat } from '@helpers/date';
import { useLocaleText } from '@helpers/hooks';
import { isEnglishLanguage } from '@helpers/utility';

/** @deprecated use inputFieldFactory from directoty /molecules */
const InputAdditionalFieldFactory = forwardRef(({ fieldData, formName, dropdownValue }, ref) => {
  const { localeKey, groupFields } = fieldData;
  const { getLocaleText } = useLocaleText();

  const booleanItems = [
    {
      value: true,
      label: getLocaleText('d.boolean.yes'),
    },
    {
      value: false,
      label: getLocaleText('d.boolean.no'),
    },
  ];

  const localeText = getLocaleText(localeKey);
  const localizedValue = isEnglishLanguage(localeText) ? ` ${localeText}` : localeText;

  for (const data of groupFields) {
    const { dataType, columnCode } = data;
    switch (dataType) {
      case DataTypeEnum.TEXT:
        return (
          <Form.Item name={columnCode} label={getLocaleText(localeKey)} className="mb-0">
            <Input
              size="large"
              placeholder={getLocaleText('c.placeholder.additional.input_required', localizedValue)}
            />
          </Form.Item>
        );
      case DataTypeEnum.FLAG:
        return (
          <Form.Item name={columnCode} label={getLocaleText(localeKey)} className="mb-0">
            <Select
              size="large"
              placeholder={getLocaleText('c.placeholder.additional.select_required', localizedValue)}
              options={booleanItems}
            />
          </Form.Item>
        );
      case DataTypeEnum.DATE:
        return (
          <Form.Item name={columnCode} label={getLocaleText(localeKey)} className="mb-0">
            <CustomDatePicker
              placeholder={getLocaleText('c.placeholder.additional.select_required', localizedValue)}
              size="large"
              className="w-full"
              format={DateFormat.buddhistDayMonthYear}
            />
          </Form.Item>
        );
      case DataTypeEnum.ENUM:
        return (
          <Form.Item name={columnCode} label={getLocaleText(localeKey)} className="mb-0">
            <Select
              size="large"
              placeholder={getLocaleText('c.placeholder.additional.select_required', localizedValue)}
              options={dropdownValue}
            />
          </Form.Item>
        );
      default: {
        return <></>;
      }
    }
  }
});

export default InputAdditionalFieldFactory;
