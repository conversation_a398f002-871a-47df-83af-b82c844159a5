import React, { forwardRef, useEffect, useImperativeHandle, useRef, useState } from 'react';

import { Card, Col, Form, Input, Row, Space, Switch, Tooltip } from 'antd';
import { useDispatch, useSelector } from 'react-redux';
import { useTheme } from 'styled-components';

import FileUploader from '@components/atoms/fileUploader';
import ContentTitle from '@components/molecules/contentTitle';
import HtmlEditor from '@components/molecules/htmlEditor';
import { useLocaleText } from '@helpers/hooks';
import mediaAction from '@redux/media/actions';
import { mediaStore } from '@redux/media/state';

const { createMediaListAction } = mediaAction;

const EditCourseItemArticleForm = forwardRef(({ data, onValuesChange, onFinish }, ref) => {
  const { token } = useTheme();
  const { getLocaleText } = useLocaleText();
  const dispatch = useDispatch();
  const payloadForm = useRef({});
  const [form] = Form.useForm();

  const [isDisableSwitch, setIsDisableSwitch] = useState(true);
  const [attachmentFileList, setAttachmentFileList] = useState([]);
  const [updateAttachmentFileList, setUpdateAttachmentFileList] = useState([]);
  const [removeAttachmentFileList, setRemoveAttachmentFileList] = useState([]);

  const { mediaList } = useSelector((state) => ({
    mediaList: state.Media.get(mediaStore.mediaList),
  }));

  const { isSuccess: isSuccessUploadMedia, data: dataMediaList } = mediaList;

  const getTooltipText = (_isDisableSwitch, _isEnabled) => {
    if (_isDisableSwitch) {
      return 'ไม่สามารถเปิดใช้งานได้ เนื่องจากไม่มีเนื้อหาแบบบทความ';
    }

    return '';
  };

  const numberValidator = (_, value) => {
    if (value && !/^[0-9]+$/.test(value)) {
      return Promise.reject(new Error(getLocaleText('d.article.duration_invalid')));
    }

    if (value < 1 || value > 86400) {
      return Promise.reject(new Error(getLocaleText('d.article.out_of_range_duration')));
    }

    return Promise.resolve();
  };

  const onSubmit = () => {
    form.validateFields().then((values) => {
      payloadForm.current = values;

      if (updateAttachmentFileList.length === 0) {
        onFinish({ ...payloadForm.current, attachments: attachmentFileList });
      } else if (updateAttachmentFileList.length > 0) {
        dispatch(createMediaListAction.request(updateAttachmentFileList));
      }
    });
  };

  const onUploadAttachmentChange = (info) => {
    setAttachmentFileList(info.fileList);
    const { file, fileList } = info;
    if (file.status === 'removed') {
      if (file.uid && !file.id) {
        const newFileUpload = updateAttachmentFileList.filter((item) => item.uid !== file.uid);
        setUpdateAttachmentFileList([...newFileUpload]);
      }

      if (file.id) {
        setRemoveAttachmentFileList([...removeAttachmentFileList, file.id]);
      }
      return;
    }

    if (file.status === 'done' && file.uid && !file.id) {
      const newFileList = fileList.filter((item) => !!item.uid && !item.id);
      setUpdateAttachmentFileList(newFileList);
    }
  };

  const onFormValuesChange = (values) => {
    const isDisable = !(values.article && values.article?.duration > 0 && values.article?.contentHtml);
    setIsDisableSwitch(isDisable);
    onValuesChange(values);
  };

  useEffect(() => {
    const defaultDuration = 60;
    const newDuration = data?.article?.duration ?? defaultDuration;
    if (newDuration > 0 && data?.article?.contentHtml) {
      setIsDisableSwitch(false);
    }

    if (data?.attachments?.length > 0) {
      setAttachmentFileList(data.attachments);
    } else if (data?.attachments?.fileList?.length > 0) {
      const { fileList } = data.attachments;
      const updateFileList = fileList.filter((item) => !!item.uid && !item.id);
      setUpdateAttachmentFileList(updateFileList);
      setAttachmentFileList(fileList);
    }
  }, []);

  useEffect(() => {
    if (isSuccessUploadMedia && dataMediaList?.length > 0) {
      const newAttachments = [];
      let mediaListIndex = 0;

      for (const attachmentFile of attachmentFileList) {
        if (attachmentFile.id) {
          newAttachments.push(attachmentFile);
        } else {
          const dataMedia = dataMediaList[mediaListIndex].data;
          const newAttachment = {
            id: dataMedia.media.id,
            name: dataMedia.media.filename,
            path: dataMedia.media.path,
            url: dataMedia.signedUrl,
          };
          newAttachments.push(newAttachment);
          mediaListIndex++;
        }
      }

      onFinish({
        ...payloadForm.current,
        attachments: newAttachments,
      });

      dispatch(createMediaListAction.reset());
    }
  }, [isSuccessUploadMedia, dataMediaList]);

  useImperativeHandle(ref, () => ({
    validateFields: () => form.validateFields(),
    resetFields: () => form.resetFields(),
    onSubmit,
  }));

  return (
    <Form
      form={form}
      layout="vertical"
      style={{ width: '100%' }}
      scrollToFirstError
      onValuesChange={(_, values) => onFormValuesChange(values)}
      initialValues={{
        id: data?.id,
        type: data?.type,
        name: data?.name,
        description: data?.description,
        isEnabled: data?.isEnabled,
        attachments: data?.attachments ?? [],
        materialMediaId: data?.materialMediaId,
        code: data?.code,

        article: {
          ...data?.article,
          duration: data?.article?.duration ?? 60,
        },
      }}
    >
      <Form.Item name="id" hidden />
      <Form.Item name="type" hidden />
      <Form.Item name="materialMediaId" hidden />
      <Form.Item name="code" hidden />

      <Space direction="vertical" style={{ width: '100%' }} size="middle">
        <Card>
          <ContentTitle
            style={{ marginBottom: token.marginMD }}
            title={getLocaleText('c.drawer.survey.title.status')}
          />
          <Space align="baseline">
            <Tooltip title={getTooltipText(isDisableSwitch, data?.isEnabled)}>
              <Form.Item name="isEnabled" valuePropName="checked">
                <Switch disabled={isDisableSwitch} size="large" />
              </Form.Item>
            </Tooltip>
            <span style={{ paddingLeft: 24 }}>{getLocaleText('general.checkbox.enable')}</span>
          </Space>
        </Card>

        <Card>
          <ContentTitle
            style={{ marginBottom: token.marginMD }}
            title={getLocaleText('components.drawer.article.basic_detail')}
          />
          <Form.Item
            style={{ paddingTop: token.paddingXS }}
            name="name"
            label={getLocaleText('d.article.name')}
            rules={[{ required: true, message: `กรุณากรอก${getLocaleText('d.article.name')}` }]}
          >
            <Input placeholder={getLocaleText('components.drawer.article.input_name')} size="large" />
          </Form.Item>

          <Form.Item name="description" label={getLocaleText('d.article.description')}>
            <Input.TextArea
              rows={6}
              placeholder={getLocaleText('components.drawer.article.input_description')}
              size="large"
            />
          </Form.Item>
        </Card>

        <Card>
          <ContentTitle
            style={{ marginBottom: token.marginMD }}
            title={getLocaleText('components.drawer.article.article_detail')}
          />
          <Form.Item
            name={['article', 'duration']}
            label={getLocaleText('d.article.duration')}
            rules={[
              { required: true, message: getLocaleText('d.article.required_duration') },
              { validator: numberValidator },
            ]}
            validateFirst
          >
            <Input
              onChange={(e) => {
                const duration = e.target.value;

                if (!Number(duration) || duration < 1) {
                  setIsDisableSwitch(true);
                  return;
                }

                setIsDisableSwitch(false);
              }}
              placeholder={getLocaleText('components.drawer.article.input_duration')}
              size="large"
            />
          </Form.Item>

          <Form.Item
            name={['article', 'contentHtml']}
            label={getLocaleText('components.drawer.article.content_detail')}
            rules={[{ required: true, message: 'กรุณากรอกรายละเอียดเนื้อหา' }]}
          >
            <HtmlEditor />
          </Form.Item>
        </Card>

        <Card>
          <Row gutter={[16, 16]}>
            <Col span={24}>
              <ContentTitle
                style={{ marginBottom: token.marginMD }}
                title={getLocaleText('c.label.attachment.has_file_attachment', null, `${attachmentFileList.length}`)}
                subTitle={getLocaleText('components.drawer.article.attachments_detail')}
              />
            </Col>

            <Col span={24}>
              <Form.Item className="mb-0" name="attachments">
                <FileUploader srcList={attachmentFileList} onChange={onUploadAttachmentChange} />
              </Form.Item>
            </Col>
          </Row>
        </Card>
      </Space>
    </Form>
  );
});

export default EditCourseItemArticleForm;
