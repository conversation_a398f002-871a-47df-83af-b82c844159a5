import React, { forwardRef, useEffect, useImperativeHandle, useState } from 'react';

import { DeleteOutlined, EditOutlined, ExclamationCircleOutlined, PlusOutlined } from '@ant-design/icons';
import {
  Button,
  Card,
  Checkbox,
  Col,
  Flex,
  Form,
  Input,
  Modal,
  Radio,
  Row,
  Space,
  Switch,
  Tooltip,
  Typography,
} from 'antd';
import dynamic from 'next/dynamic';
import { bool, func, object } from 'prop-types';
import { useDispatch } from 'react-redux';
import { useTheme } from 'styled-components';
import { v4 as uuidv4 } from 'uuid';

import EmptyDataPlaceholder from '@components/atoms/emptyDataPlaceholder';
import RenderWrapper from '@components/atoms/renderWrapper';
import ContentTitle from '@components/molecules/contentTitle';
import HtmlEditor from '@components/molecules/htmlEditor';
import QuizEnabledDynamicFormItem from '@components/organisms/form/editCourseItemQuizForm/formItem/quizEnabledDynamicFormItem';
import { htmlEditorExtensionEnum } from '@constants/htmlEditor';
import { QuizShowAnswerTypeEnum, QuizTestTypeEnum } from '@constants/quiz';
import { useLocaleText } from '@helpers/hooks';
import { sanitizeDirtyHTML } from '@helpers/html/sanitizeHTML';
import transformEntityTagHtml from '@helpers/html/transformEntityTagHtml';
import messageActions from '@redux/message/actions';

import Style from './style';

const EditQuestionDrawer = dynamic(() => import('@components/organisms/drawer/editQuestionDrawer'), { ssr: false });

const { Text } = Typography;

const SECONDS_PER_MINUTE = 60;

const EditCourseItemQuizForm = ({ data, isPublishedCourse, onValuesChange, onFinish }, ref) => {
  const { token } = useTheme();
  const { getLocaleText } = useLocaleText();
  const dispatch = useDispatch();
  const { set_message } = messageActions;

  const [form] = Form.useForm();

  const [isDisableSwitch, setIsDisableSwitch] = useState(true);
  const [selectedRetestRadio, setSelectedRetestRadio] = useState('noLimit');
  const [selectedShowAnswerRadio, setSelectedShowAnswerRadio] = useState(QuizShowAnswerTypeEnum.CRITERIA);
  const [questions, setQuestions] = useState([]);
  const [isDisplayRetestSection, setIsDisplayRetestSection] = useState(false);
  const [isDisplayTimeLimitSection, setIsDisplayTimeLimitSection] = useState(false);
  const [isDisplayShowAnswerSection, setIsDisplayShowAnswerSection] = useState(false);
  const [isDisplayAfterQuestionSubmitSection, setIsDisplayAfterQuestionSubmitSection] = useState(false);
  const [isDisplayAfterQuizSubmitSection, setIsDisplayAfterQuizSubmitSection] = useState(false);

  const [openEditQuestionDrawer, setOpenEditQuestionDrawer] = useState({
    isOpen: false,
    data: null,
  });

  const getTooltipText = (_isDisableSwitch, _isEnabled) => {
    if (_isDisableSwitch) {
      return 'ไม่สามารถเปิดใช้งานได้ เนื่องจากไม่มีแบบทดสอบ';
    }

    return '';
  };

  const getQuestionTitle = (value) => value.replace(/(<([^>]+)>)/gi, '');

  const getQuizTotalDurationSec = (limitTimeDuration) =>
    (limitTimeDuration.totalDurationSec && Number(limitTimeDuration.totalDurationSec) * SECONDS_PER_MINUTE) || null;

  const onChangeResetOption = (e) => {
    const option = e.target.value;
    setSelectedRetestRadio(e.target.value);
    if (option !== 'limitTimes') {
      const exitValue = form.getFieldValue(['quiz', 'retest', 'totalCountRetest']);
      form.setFields([
        {
          name: ['quiz', 'retest', 'totalCountRetest'],
          value: exitValue,
          errors: [],
        },
      ]);
    }
  };

  const onSubmit = () => {
    const formValues = form.getFieldsValue();
    form.validateFields().then((result) => {
      const { quiz, ...courseItemData } = result;
      const { retest: retestForm, limitTimeDuration: limitTimeDurationForm, showAnswer: showAnswerForm } = result.quiz;

      const retest = {
        isEnabled: retestForm.isEnabled,
        totalCountRetest:
          formValues.retestOption === 'limitTimes' && retestForm.totalCountRetest
            ? Number(retestForm.totalCountRetest)
            : null,
      };

      const limitTimeDuration = {
        isEnabled: limitTimeDurationForm.isEnabled ?? false,
        totalDurationSec: getQuizTotalDurationSec(limitTimeDurationForm),
      };

      const showAnswer = {
        isEnabled: showAnswerForm.isEnabled ?? false,
        afterQuestionSubmit: {
          isEnabled: showAnswerForm.afterQuestionSubmit?.isEnabled ?? false,
          isShowCorrectAnswer: showAnswerForm.afterQuestionSubmit?.isShowCorrectAnswer ?? true,
        },
        afterQuizSubmit: {
          isEnabled: showAnswerForm.afterQuizSubmit?.isEnabled ?? false,
          type: showAnswerForm.afterQuizSubmit?.type ?? null,
          totalCountRetest: showAnswerForm.afterQuizSubmit?.totalCountRetest
            ? Number(showAnswerForm.afterQuizSubmit?.totalCountRetest)
            : null,
          isShowCorrectAnswer: showAnswerForm.afterQuizSubmit?.isShowCorrectAnswer ?? true,
        },
      };
      const formPayload = {
        ...courseItemData,
        quiz: {
          ...quiz,
          retest,
          limitTimeDuration,
          showAnswer,
          questions,
        },
      };
      onFinish(formPayload);
    });
  };

  const onInitData = (values) => {
    const { quiz } = values;

    // Default for new course item
    if (!quiz) {
      form.setFieldValue(['quiz', 'retest', 'isEnabled'], true);
      form.setFieldValue('retestOption', 'noLimit');
      setIsDisplayRetestSection(true);
    } else {
      // Retest section
      const isRetestEnabled = quiz?.retest?.isEnabled ?? false;
      setIsDisplayRetestSection(isRetestEnabled);

      const hasLimitTimes = quiz?.retest?.totalCountRetest !== null;
      form.setFieldValue('retestOption', hasLimitTimes ? 'limitTimes' : 'noLimit');
      setSelectedRetestRadio(hasLimitTimes ? 'limitTimes' : 'noLimit');

      // Limit time section
      const isLimitTimeEnabled = quiz?.limitTimeDuration?.isEnabled ?? false;
      setIsDisplayTimeLimitSection(isLimitTimeEnabled);

      if (isLimitTimeEnabled) {
        form.setFieldValue(
          ['quiz', 'limitTimeDuration', 'totalDurationSec'],
          quiz?.limitTimeDuration?.totalDurationSec / SECONDS_PER_MINUTE || null,
        );
      }
    }

    // Show answer section
    const showAnswer = quiz?.showAnswer ?? {};
    form.setFieldValue(['quiz', 'showAnswer', 'isEnabled'], showAnswer.isEnabled ?? false);
    form.setFieldValue(
      ['quiz', 'showAnswer', 'afterQuestionSubmit', 'isEnabled'],
      showAnswer.afterQuestionSubmit?.isEnabled,
    );
    form.setFieldValue(['quiz', 'showAnswer', 'afterQuizSubmit', 'isEnabled'], showAnswer.afterQuizSubmit?.isEnabled);

    if (showAnswer.afterQuizSubmit?.type) {
      setSelectedShowAnswerRadio(showAnswer.afterQuizSubmit.type);
      form.setFieldValue(['quiz', 'showAnswer', 'afterQuizSubmit', 'type'], showAnswer.afterQuizSubmit.type);
    } else {
      setSelectedShowAnswerRadio(QuizShowAnswerTypeEnum.ALWAYS);
      form.setFieldValue(['quiz', 'showAnswer', 'afterQuizSubmit', 'type'], QuizShowAnswerTypeEnum.ALWAYS);
    }

    setIsDisplayShowAnswerSection(showAnswer.isEnabled ?? false);
    setIsDisplayAfterQuestionSubmitSection(showAnswer.afterQuestionSubmit?.isEnabled ?? false);
    setIsDisplayAfterQuizSubmitSection(showAnswer.afterQuizSubmit?.isEnabled ?? false);
  };

  const onFormValuesChange = (values) => {
    const { quiz } = values;

    setIsDisplayRetestSection(!!quiz?.retest?.isEnabled);
    setIsDisplayTimeLimitSection(!!quiz?.limitTimeDuration?.isEnabled);
    setIsDisplayShowAnswerSection(!!quiz?.showAnswer?.isEnabled);

    if (quiz?.limitTimeDuration?.isEnabled) {
      quiz.limitTimeDuration.totalDurationSec = getQuizTotalDurationSec(quiz.limitTimeDuration);
    }

    if (quiz?.showAnswer?.afterQuizSubmit?.type) {
      setSelectedShowAnswerRadio(quiz.showAnswer.afterQuizSubmit.type);
    }

    if (values?.retestOption) {
      setSelectedRetestRadio(values.retestOption);
    }

    onValuesChange(values);
  };

  const onInputKeyPress = (event) => {
    const { key } = event;

    const isNotAllowDigitNumber = !/^\d$/.test(key);
    if (isNotAllowDigitNumber) {
      event.preventDefault();
    }
  };

  const quizRetestTimesConfigValidator = (_, value) => {
    if (!isDisplayRetestSection || selectedRetestRadio !== 'limitTimes') {
      return Promise.resolve();
    }

    if (!value) {
      return Promise.reject(new Error('กรุณากรอกจำนวนครั้ง'));
    }

    if (!String(value).match(/^(?!0$)\d+$/)) {
      return Promise.reject(new Error('กรุณากรอกจำนวนครั้ง โดยค่าต้องมากกว่า 0 เสมอ'));
    }

    return Promise.resolve();
  };

  const quizTotalDurationValidator = (_, value) => {
    if (!isDisplayTimeLimitSection) {
      return Promise.resolve();
    }

    if (!value) {
      return Promise.reject(new Error('กรุณากรอกระยะเวลาในการทำข้อสอบ'));
    }

    if (!String(value).match(/^(?!0$)\d+$/)) {
      return Promise.reject(new Error('กรุณากรอกระยะเวลาในการทำข้อสอบ โดยค่าต้องมากกว่า 0 เสมอ'));
    }

    return Promise.resolve();
  };

  const showAnswerOptionCheckBoxValidator = (_, value) => {
    const isShowAnswer = form.getFieldValue(['quiz', 'showAnswer', 'isEnabled']);
    const afterQuestionSubmit = form.getFieldValue(['quiz', 'showAnswer', 'afterQuestionSubmit', 'isEnabled']);
    const afterQuizSubmit = form.getFieldValue(['quiz', 'showAnswer', 'afterQuizSubmit', 'isEnabled']);
    if (!isShowAnswer) {
      return Promise.resolve();
    }

    if (afterQuestionSubmit || afterQuizSubmit) {
      return Promise.resolve();
    }
    return Promise.reject(new Error('กรุณาเลือกแสดงเฉลยคำตอบ'));
  };

  const quizTotalCountShowAnswerRetestValidator = (_, value) => {
    const isRetestEnabled = form.getFieldValue(['quiz', 'showAnswer', 'afterQuizSubmit', 'isEnabled']);
    const maxRetestTime = form.getFieldValue(['quiz', 'retest', 'totalCountRetest']);
    if (isRetestEnabled && value && Number(value) > Number(maxRetestTime)) {
      return Promise.reject(new Error(`ไม่สามารถกรอกจำนวนครั้งมากกว่า ${maxRetestTime} ครั้ง ของการทำแบบทดสอบซ้ำได้`));
    }

    if (value && !String(value).match(/^(?!0$)\d+$/)) {
      return Promise.reject(new Error('กรุณากรอกจำนวนครั้ง โดยค่าต้องมากกว่า 0 เสมอ'));
    }
    return Promise.resolve();
  };

  const onPreventPasteAction = (e) => {
    e.preventDefault();
  };

  const onSubmitQuestionDrawer = (questionData) => {
    setQuestions((prev) => {
      if (questionData.id) {
        const itemIndex = prev.findIndex((item) => item.id === questionData.id);
        if (itemIndex > -1) prev[itemIndex] = questionData;
        dispatch(set_message('success', getLocaleText('c.drawer.quiz.title.save')));
      } else {
        prev.push({ ...questionData, id: uuidv4() });
        dispatch(set_message('success', getLocaleText('c.drawer.quiz.title.question_create_success')));
      }

      return prev;
    });

    setOpenEditQuestionDrawer({ isOpen: false, data: null });
  };

  const onClickAddQuestion = () => {
    setOpenEditQuestionDrawer({ isOpen: true, data: null });
  };

  const onClickEditQuestion = (question) => {
    setOpenEditQuestionDrawer({ isOpen: true, data: question });
  };

  const onClickConfirmRemoveQuestion = (id) => {
    const currentQuestions = questions.filter((item) => item.id !== id);
    const isNoQuestions = currentQuestions.length === 0;
    if (isNoQuestions) {
      form.setFieldValue('isEnabled', false);
    }
    setQuestions(currentQuestions);
    setOpenEditQuestionDrawer({ isOpen: false, data: null });
  };

  const onClickRemoveQuestion = (id, name) => {
    Modal.confirm({
      title: getLocaleText('c.modal.question.warning_delete_title', null, getQuestionTitle(name)),
      content: getLocaleText('c.modal.question.warning_delete_content'),
      icon: <ExclamationCircleOutlined />,
      okText: getLocaleText('c.btn.confirm'),
      onOk() {
        onClickConfirmRemoveQuestion(id);
      },
      onClose() {},
    });
  };

  const onChangeShowAnswerAfterSubmitQuiz = (e) => {
    const { checked } = e.target;
    setIsDisplayAfterQuizSubmitSection(checked);
  };

  const onChangeShowAnswerAfterSubmitQuestion = (e) => {
    const { checked } = e.target;
    setIsDisplayAfterQuestionSubmitSection(checked);
  };

  useImperativeHandle(ref, () => ({
    validateFields: () => form.validateFields(),
    resetFields: () => form.resetFields(),
    onSubmit,
  }));

  useEffect(() => {
    const initialQuestions = data?.quiz?.questionSets?.[0]?.questions || [];
    setIsDisableSwitch(initialQuestions.length === 0);
    setQuestions(initialQuestions);
    onInitData(data);
  }, []);

  useEffect(() => {
    const { quiz, ...courseItemData } = form.getFieldsValue();

    quiz.limitTimeDuration.totalDurationSec = getQuizTotalDurationSec(quiz.limitTimeDuration);

    const isNoQuestions = questions?.length === 0;
    setIsDisableSwitch(isNoQuestions);

    onValuesChange({
      ...courseItemData,
      quiz: {
        ...quiz,
        questionSets: [
          {
            questions,
          },
        ],
      },
    });
  }, [JSON.stringify(questions)]);

  return (
    <Style>
      <Form
        form={form}
        layout="vertical"
        style={{ width: '100%' }}
        scrollToFirstError
        onValuesChange={(_, values) => onFormValuesChange(values)}
        initialValues={{
          id: data?.id,
          type: data?.type,
          name: data?.name,
          description: data?.description,
          isEnabled: data?.isEnabled,
          materialMediaId: data?.materialMediaId,
          code: data?.code,
          quiz: data?.quiz,
        }}
      >
        <Form.Item name="id" hidden />
        <Form.Item name="type" hidden />
        <Form.Item name="materialMediaId" hidden />
        <Form.Item name="code" hidden />

        <Space direction="vertical" style={{ width: '100%' }} size="middle">
          <Card>
            <Flex gap={16} vertical align="stretch">
              <ContentTitle
                style={{ marginBottom: token.marginMD }}
                title={getLocaleText('c.drawer.survey.title.status')}
              />
              <Space align="baseline">
                <Tooltip title={getTooltipText(isDisableSwitch, data?.isEnabled)}>
                  <Form.Item name="isEnabled" valuePropName="checked">
                    <Switch disabled={isDisableSwitch || isPublishedCourse} size="large" />
                  </Form.Item>
                </Tooltip>
                <span style={{ paddingLeft: 24 }}>{getLocaleText('general.checkbox.enable')}</span>
              </Space>
            </Flex>
          </Card>

          {/* course item detail */}
          <Card>
            <ContentTitle
              style={{ marginBottom: token.marginMD }}
              title={getLocaleText('c.drawer.quiz.title.basic_details')}
            />
            <Form.Item
              style={{ paddingTop: token.paddingXS }}
              name="name"
              label="ชื่อแบบทดสอบ"
              rules={[{ required: true, message: 'กรุณากรอกชื่อแบบทดสอบ' }]}
            >
              <Input placeholder="กรอกชื่อแบบทดสอบ" size="large" />
            </Form.Item>

            <Form.Item name="description" label={getLocaleText('d.label.description')}>
              <Input.TextArea rows={6} placeholder="กรอกคำอธิบาย" size="large" />
            </Form.Item>
          </Card>

          {/* quiz detail */}
          <Card>
            <ContentTitle style={{ marginBottom: 10 }} title="รายละเอียดแบบทดสอบหลัก" />
            <Form.Item
              name={['quiz', 'name']}
              label="ชื่อแบบทดสอบหลัก"
              rules={[{ required: true, message: getLocaleText('d.label.quiz.quiz_error_required') }]}
            >
              <Input placeholder="กรอกชื่อแบบทดสอบ" size="large" />
            </Form.Item>
            <Form.Item
              name={['quiz', 'type']}
              label={getLocaleText('d.label.quiz.type')}
              rules={[{ required: true, message: getLocaleText('d.label.quiz.type_error_required') }]}
            >
              <Radio.Group size="large">
                <Radio value={QuizTestTypeEnum.REGULAR}>{getLocaleText('d.label.quiz.type_regular')}</Radio>
                <Radio value={QuizTestTypeEnum.PRE_TEST}>{getLocaleText('d.label.quiz.type_pre_test')}</Radio>
                <Radio value={QuizTestTypeEnum.POST_TEST}>{getLocaleText('d.label.quiz.type_post_test')}</Radio>
              </Radio.Group>
            </Form.Item>
            <Form.Item
              name={['quiz', 'description']}
              label={getLocaleText('d.label.quiz.description')}
              rules={[
                {
                  required: false,
                  message: getLocaleText('d.label.quiz.description_error_required'),
                },
              ]}
            >
              <HtmlEditor height={200} disabledFunctions={[htmlEditorExtensionEnum.canva]} />
            </Form.Item>
          </Card>

          {/* quiz isRetest */}
          <Card>
            <Row gutter={[0, 16]}>
              <Col span={24}>
                <ContentTitle title={getLocaleText('c.drawer.quiz.title.setting')} />
              </Col>
              <Col span={24}>
                <QuizEnabledDynamicFormItem
                  formItemName={['quiz', 'retest', 'isEnabled']}
                  label={getLocaleText('d.label.quiz.enabled_retest')}
                  isShowForm={isDisplayRetestSection}
                >
                  <Form.Item name="retestOption" className="mb-0">
                    <Radio.Group size="large" className="radio-group" onChange={onChangeResetOption}>
                      <Radio className="radio" value="noLimit">
                        ไม่จำกัดจำนวนครั้ง
                      </Radio>
                      <div className="radio-option-input">
                        <div className="radio">
                          <Radio value="limitTimes">ระบุจำนวนครั้ง</Radio>
                        </div>

                        <div className="field-input">
                          <Form.Item
                            className="mb-0"
                            name={['quiz', 'retest', 'totalCountRetest']}
                            rules={[{ validator: quizRetestTimesConfigValidator }]}
                            validateFirst
                          >
                            <Input
                              onKeyPress={onInputKeyPress}
                              placeholder="กรอกจำนวนครั้ง"
                              disabled={selectedRetestRadio !== 'limitTimes'}
                              size="large"
                              style={{ width: '100%' }}
                            />
                          </Form.Item>
                        </div>
                      </div>
                    </Radio.Group>
                  </Form.Item>
                </QuizEnabledDynamicFormItem>
              </Col>
              {/* Limit time duration */}
              <Col span={24}>
                <QuizEnabledDynamicFormItem
                  formItemName={['quiz', 'limitTimeDuration', 'isEnabled']}
                  label="เปิดใช้งาน จำกัดเวลาทำแบบทดสอบ"
                  isShowForm={isDisplayTimeLimitSection}
                >
                  <Flex vertical gap={16} align="stretch">
                    <Form.Item
                      className="mb-0"
                      name={['quiz', 'limitTimeDuration', 'totalDurationSec']}
                      label="ระยะเวลาในการทำข้อสอบ (นาที)"
                      required
                      rules={[{ validator: quizTotalDurationValidator }]}
                      validateFirst
                    >
                      <Input
                        onKeyPress={onInputKeyPress}
                        onPaste={onPreventPasteAction}
                        placeholder="กรอกระยะเวลาในการทำข้อสอบ"
                        size="large"
                      />
                    </Form.Item>
                  </Flex>
                </QuizEnabledDynamicFormItem>
              </Col>
              <Col span={24}>
                <QuizEnabledDynamicFormItem
                  formItemName={['quiz', 'showAnswer', 'isEnabled']}
                  label="เปิดใช้งาน แสดงเฉลยคำตอบ"
                  isShowForm={isDisplayShowAnswerSection}
                >
                  <Space direction="vertical">
                    <Form.Item
                      name={['quiz', 'showAnswer', 'afterQuestionSubmit', 'isEnabled']}
                      valuePropName="checked"
                      style={{ marginBottom: 0 }}
                      dependencies={[['quiz', 'showAnswer', 'afterQuizSubmit', 'isEnabled']]}
                    >
                      <Checkbox onChange={onChangeShowAnswerAfterSubmitQuestion}>
                        แสดงเฉลยทันทีหลังตอบคำถามในแต่ละข้อ
                      </Checkbox>
                    </Form.Item>
                    <RenderWrapper isRender={isDisplayAfterQuestionSubmitSection}>
                      <Space direction="vertical" style={{ paddingLeft: '24px', paddingBottom: '8px' }}>
                        <Text type="secondary">รูปแบบการแสดง</Text>
                        <Form.Item
                          name={['quiz', 'showAnswer', 'afterQuestionSubmit', 'isShowCorrectAnswer']}
                          className="mb-0"
                          initialValue={true}
                        >
                          <Radio.Group size="large">
                            <Space direction="vertical">
                              <Radio value={false}>แสดงผลคำตอบ แต่ไม่เฉลยคำตอบที่ถูกต้อง</Radio>
                              <Radio value={true}>แสดงผลคำตอบ พร้อมเฉลยคำตอบที่ถูกต้อง</Radio>
                            </Space>
                          </Radio.Group>
                        </Form.Item>
                      </Space>
                    </RenderWrapper>

                    <Form.Item
                      name={['quiz', 'showAnswer', 'afterQuizSubmit', 'isEnabled']}
                      valuePropName="checked"
                      style={{ marginBottom: 0 }}
                      rules={[
                        {
                          validator: showAnswerOptionCheckBoxValidator,
                        },
                      ]}
                    >
                      <Checkbox onChange={onChangeShowAnswerAfterSubmitQuiz}>แสดงเฉลยหลังจากส่งคำตอบทั้งหมด</Checkbox>
                    </Form.Item>
                    <RenderWrapper isRender={isDisplayAfterQuizSubmitSection}>
                      <>
                        <Space direction="vertical" style={{ paddingLeft: '24px', paddingBottom: '8px' }}>
                          <Text type="secondary">รูปแบบการแสดง</Text>
                          <Form.Item
                            name={['quiz', 'showAnswer', 'afterQuizSubmit', 'isShowCorrectAnswer']}
                            className="mb-0"
                            initialValue={true}
                          >
                            <Radio.Group size="large">
                              <Space direction="vertical">
                                <Radio value={false}>แสดงผลคำตอบ แต่ไม่เฉลยคำตอบที่ถูกต้อง</Radio>
                                <Radio value={true}>แสดงผลคำตอบ พร้อมเฉลยคำตอบที่ถูกต้อง</Radio>
                              </Space>
                            </Radio.Group>
                          </Form.Item>
                        </Space>
                        <Space direction="vertical" size={16} style={{ paddingLeft: '24px' }}>
                          <Text type="secondary">เงื่อนไขการแสดง</Text>
                          <Form.Item
                            name={['quiz', 'showAnswer', 'afterQuizSubmit', 'type']}
                            rules={[
                              {
                                required: true,
                                message: 'กรุณาเลือกเงื่อนไขการแสดง',
                              },
                            ]}
                          >
                            <Radio.Group size="large">
                              <Space direction="vertical">
                                <Radio value={QuizShowAnswerTypeEnum.CRITERIA}>
                                  แสดงเมื่อผ่านเกณฑ์การเรียน (กรณีไม่ได้กำหนดค่าเริ่มต้น จะเป็น 80%)
                                </Radio>
                                <Radio value={QuizShowAnswerTypeEnum.RETEST}>
                                  <div style={{ display: 'flex', alignItems: 'center' }}>
                                    แสดงเมื่อไม่ผ่านเกณฑ์การเรียนเกินจำนวนที่กำหนด
                                    <Form.Item
                                      name={['quiz', 'showAnswer', 'afterQuizSubmit', 'totalCountRetest']}
                                      style={{ margin: 0, paddingLeft: '8px' }}
                                      rules={[
                                        {
                                          required: selectedShowAnswerRadio === QuizShowAnswerTypeEnum.RETEST,
                                          message: 'กรุณากรอกจำนวนครั้ง',
                                        },
                                        { validator: quizTotalCountShowAnswerRetestValidator },
                                      ]}
                                    >
                                      <Input
                                        onKeyPress={onInputKeyPress}
                                        placeholder="กรอกจำนวนครั้ง"
                                        style={{ width: '180px', marginRight: '8px' }}
                                        disabled={
                                          selectedShowAnswerRadio !== QuizShowAnswerTypeEnum.RETEST ||
                                          selectedRetestRadio === 'noLimit'
                                        }
                                        size="large"
                                      />
                                    </Form.Item>
                                    <span>ครั้ง</span>
                                  </div>
                                </Radio>
                                <Radio value={QuizShowAnswerTypeEnum.ALWAYS}>แสดงเมื่อส่งคำตอบเสมอ</Radio>
                              </Space>
                            </Radio.Group>
                          </Form.Item>
                        </Space>
                      </>
                    </RenderWrapper>
                  </Space>
                </QuizEnabledDynamicFormItem>
              </Col>
            </Row>
          </Card>

          <Card>
            <ContentTitle
              title={`${getLocaleText('c.drawer.quiz.title.question')} (${questions?.length ?? 0})`}
              subTitle={getLocaleText('c.drawer.quiz.title.question_subtitle')}
              extraRightContent={
                <Space size={10}>
                  <Button
                    info
                    type="primary"
                    onClick={() => onClickAddQuestion()}
                    icon={<PlusOutlined />}
                    style={{ fontSize: token.fontSizeSM }}
                  >
                    <span style={{ fontSize: token.fontSize }}>
                      {getLocaleText('c.drawer.quiz.btn.create_question')}
                    </span>
                  </Button>
                </Space>
              }
            />

            {questions?.length > 0 ? (
              <div
                style={{
                  overflow: 'auto',
                  backgroundColor: token.colorFillSecondary,
                  borderRadius: token.borderRadiusLG,
                }}
              >
                {questions?.map((item) => (
                  <Row
                    key={uuidv4()}
                    style={{
                      alignItems: 'center',
                      justify: 'space-between',
                      margin: token.marginXS,
                      paddingLeft: token.padding,
                      paddingRight: token.padding,
                      paddingTop: token.paddingXS,
                      paddingBottom: token.paddingXS,
                      backgroundColor: token.colorBgContainer,
                    }}
                  >
                    <Text ellipsis style={{ flex: 1 }}>
                      {getQuestionTitle(
                        transformEntityTagHtml(sanitizeDirtyHTML(item.name || '', { ALLOWED_TAGS: [] })),
                      )}
                    </Text>
                    <Button
                      onClick={() => onClickEditQuestion(item)}
                      icon={<EditOutlined />}
                      color="primary"
                      style={{ fontSize: token.fontSizeSM, marginLeft: token.marginXS }}
                    />
                    <Button
                      onClick={() => onClickRemoveQuestion(item.id, item.name)}
                      icon={<DeleteOutlined />}
                      style={{ fontSize: token.fontSizeSM, marginLeft: token.marginXS }}
                      danger
                    />
                  </Row>
                )) ?? <EmptyDataPlaceholder />}
              </div>
            ) : (
              <EmptyDataPlaceholder />
            )}
          </Card>

          <EditQuestionDrawer
            isOpen={openEditQuestionDrawer.isOpen}
            data={openEditQuestionDrawer.data}
            onRemove={onClickRemoveQuestion}
            onSubmit={onSubmitQuestionDrawer}
            onClose={() => setOpenEditQuestionDrawer({ isOpen: false, data: null })}
          />
        </Space>
      </Form>
    </Style>
  );
};

EditCourseItemQuizForm.propTypes = {
  data: object,
  isPublishedCourse: bool,
  onValuesChange: func,
  onFinish: func,
};

export default forwardRef(EditCourseItemQuizForm);
