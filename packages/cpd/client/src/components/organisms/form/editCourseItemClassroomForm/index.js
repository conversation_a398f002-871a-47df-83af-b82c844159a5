import React, { forwardRef, useEffect, useImperativeHandle, useRef, useState } from 'react';

import { Card, Col, Form, Input, Row, Space, Switch, Tooltip, Typography } from 'antd';
import { useDispatch, useSelector } from 'react-redux';
import { useTheme } from 'styled-components';

import FileUploader from '@components/atoms/fileUploader';
import ContentTitle from '@components/molecules/contentTitle';
import DetailItemDisplay from '@components/molecules/detailItemDisplay';
import HtmlEditor from '@components/molecules/htmlEditor';
import { htmlEditorExtensionEnum } from '@constants/htmlEditor';
import { useLocaleText } from '@helpers/hooks';
import mediaAction from '@redux/media/actions';
import { mediaStore } from '@redux/media/state';

const { createMediaListAction } = mediaAction;

const { Paragraph } = Typography;

const EditCourseItemClassroomForm = forwardRef(({ data, onValuesChange, onFinish }, ref) => {
  const { token } = useTheme();
  const { getLocaleText } = useLocaleText();
  const dispatch = useDispatch();
  const payloadForm = useRef({});
  const [form] = Form.useForm();

  const [attachmentFileList, setAttachmentFileList] = useState([]);
  const [updateAttachmentFileList, setUpdateAttachmentFileList] = useState([]);
  const [removeAttachmentFileList, setRemoveAttachmentFileList] = useState([]);

  const { mediaList } = useSelector((state) => ({
    mediaList: state.Media.get(mediaStore.mediaList),
  }));

  const { isSuccess: isSuccessUploadMedia, data: dataMediaList } = mediaList;

  const onSubmit = () => {
    form.validateFields().then((values) => {
      payloadForm.current = values;

      if (updateAttachmentFileList.length === 0) {
        onFinish({ ...payloadForm.current, attachments: attachmentFileList });
      } else if (updateAttachmentFileList.length > 0) {
        dispatch(createMediaListAction.request(updateAttachmentFileList));
      }
    });
  };

  const onUploadAttachmentChange = (info) => {
    setAttachmentFileList(info.fileList);
    const { file, fileList } = info;

    if (file.status === 'removed') {
      if (file.uid && !file.id) {
        const newFileUpload = updateAttachmentFileList.filter((item) => item.uid !== file.uid);
        setUpdateAttachmentFileList([...newFileUpload]);
      }

      if (file.id) {
        setRemoveAttachmentFileList([...removeAttachmentFileList, file.id]);
      }
    }

    if (file.status === 'done' && file.uid && !file.id) {
      const newFileList = fileList.filter((item) => !!item.uid && !item.id);
      setUpdateAttachmentFileList(newFileList);
    }
  };

  useImperativeHandle(ref, () => ({
    validateFields: () => form.validateFields(),
    resetFields: () => form.resetFields(),
    onSubmit,
  }));

  useEffect(() => {
    if (data?.attachments?.length > 0) {
      setAttachmentFileList(data.attachments);
    } else if (data?.attachments?.fileList?.length > 0) {
      const { fileList } = data.attachments;
      const updateFileList = fileList.filter((item) => !!item.uid && !item.id);
      setUpdateAttachmentFileList(updateFileList);
      setAttachmentFileList(fileList);
    }
  }, []);

  useEffect(() => {
    if (isSuccessUploadMedia && dataMediaList?.length > 0) {
      const newAttachments = [];
      let mediaListIndex = 0;

      for (const attachmentFile of attachmentFileList) {
        if (attachmentFile.id) {
          newAttachments.push(attachmentFile);
        } else {
          const dataMedia = dataMediaList[mediaListIndex].data;
          const newAttachment = {
            id: dataMedia.media.id,
            name: dataMedia.media.filename,
            path: dataMedia.media.path,
            url: dataMedia.signedUrl,
          };
          newAttachments.push(newAttachment);
          mediaListIndex++;
        }
      }

      onFinish({
        ...payloadForm.current,
        attachments: newAttachments,
      });

      dispatch(createMediaListAction.reset());
    }
  }, [isSuccessUploadMedia, dataMediaList]);

  return (
    <Form
      form={form}
      layout="vertical"
      style={{ width: '100%' }}
      scrollToFirstError
      onValuesChange={(_, values) => onValuesChange(values)}
      initialValues={{
        id: data?.id,
        type: data?.type,
        name: data?.name,
        description: data?.description,
        isEnabled: data?.isEnabled,
        attachments: data?.attachments ?? [],

        materialMediaId: data?.materialMediaId,
        code: data?.code,

        classroom: data?.classroom,
      }}
    >
      <Form.Item name="id" hidden />
      <Form.Item name="type" hidden />
      <Form.Item name="materialMediaId" hidden />
      <Form.Item name="code" hidden />

      <Space direction="vertical" style={{ width: '100%' }} size="middle">
        <Card>
          <ContentTitle
            style={{ marginBottom: token.marginMD }}
            title={getLocaleText('c.curriculum.classroom.card.status')}
          />
          <Space align="baseline">
            <Form.Item name="isEnabled" valuePropName="checked">
              <Switch size="large" />
            </Form.Item>
            <span style={{ paddingLeft: 24 }}>{getLocaleText('general.checkbox.enable')}</span>
          </Space>
        </Card>

        <Card>
          <ContentTitle
            style={{ marginBottom: token.marginMD }}
            title={getLocaleText('components.drawer.article.basic_detail')}
          />
          <Form.Item>
            <DetailItemDisplay
              label={`${getLocaleText('c.curriculum.classroom.label.classroom_id')}:`}
              value={
                <Paragraph style={{ marginBottom: 0 }} copyable={{ tooltips: false }}>
                  {data?.code}
                </Paragraph>
              }
            />
          </Form.Item>

          <Form.Item
            style={{ paddingTop: token.paddingXS }}
            name="name"
            label={getLocaleText('c.curriculum.classroom.label.name')}
            rules={[{ required: true, message: getLocaleText('c.curriculum.classroom.label.please_enter_name') }]}
          >
            <Input placeholder={getLocaleText('c.curriculum.classroom.label.enter_name')} size="large" />
          </Form.Item>

          <Form.Item name="description" label={getLocaleText('c.curriculum.classroom.label.description')}>
            <Input.TextArea
              rows={6}
              placeholder={getLocaleText('c.curriculum.classroom.label.enter_description')}
              size="large"
            />
          </Form.Item>
        </Card>

        <Card>
          <ContentTitle
            style={{ marginBottom: token.marginMD }}
            title={getLocaleText('c.curriculum.classroom.card.classroom_information')}
          />

          <Form.Item name={['classroom', 'contentHtml']} label={getLocaleText('c.curriculum.classroom.label.content')}>
            <HtmlEditor disabledFunctions={[htmlEditorExtensionEnum.canva]} />
          </Form.Item>
        </Card>

        <Card>
          <Row gutter={[16, 16]}>
            <Col span={24}>
              <ContentTitle
                style={{ marginBottom: token.marginMD }}
                title={getLocaleText('c.label.attachment.has_file_attachment', null, `${attachmentFileList.length}`)}
                subTitle={getLocaleText('components.drawer.article.attachments_detail')}
              />
            </Col>

            <Col span={24}>
              <Form.Item className="mb-0" name="attachments">
                <FileUploader srcList={attachmentFileList} onChange={onUploadAttachmentChange} />
              </Form.Item>
            </Col>
          </Row>
        </Card>
      </Space>
    </Form>
  );
});

export default EditCourseItemClassroomForm;
