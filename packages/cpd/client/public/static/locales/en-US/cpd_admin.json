{"yes": "Yes", "no": "No", "admin": {"setting": "Settings", "edit": "Edit", "language": "Language", "myprofile": "My Profile", "mylearning": "My courses", "logout": "Logout", "dashboard": {"title": "Home", "description": "Welcome to learning system"}, "footer": {"copyright": "© 2023 SkillLane Technology. All rights reserved."}, "not_found": {"title": "404", "sub_title": "Sorry, the page you visited does not exist"}}, "user": {"age_range": "Age Range", "detail": "User Detail", "profile": "Profile", "learningHistory": "Learning History", "enrollmentHistory": "Enrollment History", "account": "Account", "fullname": "Full Name", "salesId": "Sales Id", "avatar": "Avatar", "username": "Username", "email": "Email", "citizenId": "Citizen Id", "salute": "Prefix", "firstname": "First Name", "middlename": "Middle Name", "lastname": "Last Name", "gender": "Gender", "mobilePhoneNumber": "Phone", "dateOfBirth": "Date of Birth", "employeeId": "Employee Id", "position": "Position", "positionCode": "Position Code", "positionName": "Position Name", "onboardDate": "Onboard Date", "active": "Active", "isSSO": "SSO", "personId": "Person Id", "faiAgent": "Fai Agent", "branchCode": "Branch Code", "branchName": "Branch Name", "zone": "Zone", "department": "Department", "lifeSolutionAgent": "Life Solutions Agent", "lifeSolutionAgentLogo": "Life Solutions Agent logo", "qualification1": "Qualification 1", "qualification2": "Qualification 2", "qualification3": "Qualification 3", "qualification4": "Qualification 4", "educationalQualification": "Educational Qualification", "remark1": "Remark 1", "remark2": "Remark 2", "isUL": "UL", "isUK": "UK", "license.startedAt": "License Start Date", "license.expiredAt": "License End Date", "oicLicenseLifeNo": "OIC License Life No", "oicStartDateLife": "OIC Start Date Life", "oicEndDateLife": "OIC End Date Life", "oicLicenseNonLife": "OIC License Non Life", "oicStartDateNonLife": "OIC Start Date Non Life", "oicEndDateNonLife": "OIC End Date Non Life", "tsiLicenseNo": "TSI License No", "tsiLicenseType": "TSI License Type", "tsiStartDate": "TSI Start Date", "tsiEndDate": "TSI End Date", "manage": "Manage", "employmentGroup": "Employment Group", "positionStartDate": "Position Start Date", "positionLevelCode": "Position Level Code", "positionLevelName": "Position Level Name", "isTerminated": "Employment Status", "zoneDescription": "Zone Description", "faiDescription": "Fai Description", "regionDescription": "Region Description", "centerDescription": "Center Description", "unitDescription": "Unit Description", "oicDeduct": "OIC Deduct", "nextRenewalTime": "Next Renewal Time", "isPassedUlSaleQualify": "Ul Sale Qualify", "user_group_participation": "Participation", "permissionGroup": "Permission Group", "customer": "Customer", "partnerName": "Partner Name", "plan": "Plan", "upline": "Upline", "information": {"general": "General Information", "license": "License Information", "additional": "Additional Information", "permission": "Permission", "permission_group": "Account Permission", "company_manager": "Company Manager", "regulator_manager": "Regulator Manager", "company": "Company", "regulator": "Regulator", "training_center": "Training center", "user_account": "User Account", "local_account": "Local Account", "sso_account": "Single Sign On", "organization": "Organization"}, "local_account": {"send_set_first_password": "Send Set Password Email", "set_first_password": "Password updated: {0}", "not_set_first_password": "Password not set yet", "alert_send_email_success": "An email to set first-time password has been sent to {0}", "unable_resend_email": "Unable to send email because your already set a password", "update_new_password_success": "New password saved successfully", "update_new_password_error": "Failed to set new password. Please try again.", "duplicate_old_password": "The new password must not be the same as your current password."}, "sso_account": {"send_invite_connect_sso": "<PERSON><PERSON><PERSON> Account", "connect": "Connected: {0}", "disconnect": "Disconnect", "alert_send_email_success": "Account activation email has been sent to {0}"}, "two_factor_account": {"two_factor": "2-step verification (2FA)"}, "drawer": {"edit_title": "Edit: {0}"}, "select": {"placeholder": {"permission": "Select account permission", "company_manager_condition": "“Must not select any permission” to enable this company manager assignment", "regulator_manager_condition": "“Must not select any permission” to enable this regulator manager assignment", "assign_company_manager": "Enable assign company manager", "company": "Select company", "regulator": "Select regulator", "training_center": "Select training center"}, "error": {"company_required": "Please select company", "regulator_required": "Please select regulator", "training_center_required": "Please select training center"}}, "switch": {"company_manager": "Enable manage permissions for company", "regulator_manager": "Enable manage permissions for regulators"}, "bulk_operation": {"title": "Manage Import Data", "edit_user_title": "Bulk edit users", "result": {"incomplete": {"title": "Importing Failed", "description_1": "Importing completed {0} records and", "description_2": "failed {0} records", "description_3": "Please check them to import again."}, "error": {"title": "Importing Failed", "description_1": "Importing completed {0} records and", "description_2": "failed {0} records", "description_3": "Please check them to import again."}, "cancel": {"title": "Cancel Success", "description_1": "Cancel importing completed {0} records", "label": {"cancel_enrollment_history_total": "Canceled enrollment history {0} records"}}, "detail": {"title_error": "Importing failed {0} records", "title_completed": "Importing success {0} records", "title_result": "Result", "label": {"user_name": "username:", "email": "email:", "citizen_id": "citizen id", "old_username": "old username:", "new_username": "new username:", "course_code": "course code", "productsku_code": "productsku code", "ref_code": "ref code", "learning_path_code": "learning path code:", "course_category_code": "course category code", "operation": "operation"}}, "footer": {"import_date": "Created at: {0} ", "import_by": "By {0}", "by_system": "System"}}, "buttons": {"download": "Download"}}}, "my_profile": {"title": "My Profile", "personal_info": "Personal Information", "fullname": "Fullname", "email": "Email", "citizen_id": "Citizen ID", "license_number": "Licenses", "TSI-001": "Investment adviser", "OIC-001": "Non-life insurance agent", "OIC-002": "Life insurance agent", "TFAC_CPA": "Certified Public Accountant", "TFAC_RA": "Accountant"}, "my_team": {"title": "User Detail", "profile": "Profile", "enrollment_history": "Enrollment History", "department": "Department", "organization": "Organization", "additional": "Additional Information", "supervisor": "Supervisor", "course": {"objective_type": {"title": "Objective Type", "oic": "OIC", "regular": "Regular", "tsi": "TSI"}}, "enrollment": {"empty_text": "No data available", "start_at": "Start date"}, "objective_types": {"title": "Learning Objectives"}, "assign_content": {"download": {"success": "Download file success", "error": "Something went wrong, can not download file, please try again later"}}}, "my_learning": {"my_content": "My Content", "in_progress": "In Progress", "content": "Content", "catalog": "Catalog", "records": "records", "history": "History", "no_content": "There have no in progress content", "no_content_description": "You can search or explore catalog for enrollment", "enrollment_attachments": "Documents", "required_attachments": "Documents are required", "warning_reset_progress": "The learning progress has been reset.", "required_select_round": "There are courses waiting to be chosen for the study period.", "learning_path_nearly_expired": "The learning path is nearly expired.", "score_result": "Score Result", "content_detail": "Content Detail", "learning_path_detail": "Learning Path Detail", "select_course": "Select Course", "learning_format": "Learning format", "type_choices": {"ALL": "All", "LEARNING_PATH": "Learning paths", "OIC": "OIC: Insurance", "TSI": "TSI: Investment", "REGULAR": "Regular"}, "status_choices": {"ALL": "All", "PRE_ENROLLMENT": "Waiting to start round", "PRE_ASSIGN": "Waiting to start round", "NOT_STARTED": "Waiting to start", "IN_PROGRESS": "In progress", "PENDING_RESULT": "Pending Result", "PASSED": "Finish", "PENDING_APPROVAL": "Pending approval", "APPROVED": "Approved", "REJECTED": "Rejected", "EXPIRED": "Expired", "CANCELED": "Canceled", "COMPLETED": "Completed"}, "sort_choices": {"update_new_to_old": "Update new-old", "update_old_to_new": "Update old-new", "enroll_new_to_old": "Enrolled date new-old", "enroll_old_to_new": "Enrolled date old-new", "content_name_a_to_z": "Content name A-Z", "content_name_z_to_a": "Content name Z-A"}, "objective_type": {"REGULAR": "Regular", "TRAINING": "Training"}, "eligible_learning_choices": {"ALL": "All eligible learning", "LEARNABLE": "Learnable", "EXPIRED": "Expired"}, "action_button_text": {"start": "Start Learning", "continue": "Resume Learning", "wait_to_start": "Session Pending", "cancel": "Cancel", "wait_for_approve": "Pending Approval", "approved": "Approved", "rejected": "Rejected", "completed": "Completed", "pending_result": "Pending Result", "view_enrollment_detail": "Enrollment Detail", "no_continue": "Unable To Resume Learning"}, "tooltip_action_button_text": {"plan_package_license_expired": "Because this course has expired", "no_plan_package_license_available": "Because there is no license available for this course"}, "prefix_duration": {"total": "Learning duration", "remain": "Remaining time to study videos or articles", "done": "Finished Learning"}, "prefix_remain_date": {"start_at": "Start date", "send_for_approval_at": "Submitted", "approved_at": "Approved", "rejected_at": "Rejected", "expired_at": "End date", "no_limit": "Unlimited learning time", "plan_license_expired": "Course has expired"}, "cancel_pre_enrollment_success": "Successful cancellation of course", "cancel_pre_enrollment_error": "Unsuccessful cancellation of course"}, "history_content": {"course_detail": "Course detail", "learning_path_detail": "Learning Path Detail", "plan_license_expired": "Course has expired", "learning_type_choices": {"all": "All Learning", "learning_path": "Learning Paths", "regular": "Regular", "oic": "OIC: Insurance", "tsi": "TSI: Investment"}, "sort_choices": {"date_new_to_old": "Enrolled date new-old", "date_old_to_new": "Enrolled date old-new", "content_name_a_to_z": "Content name A-Z", "content_name_z_to_a": "Content name Z-A"}, "history_table": {"no_content": "No learning history", "headers": {"content_name": "Content Name", "passed_at": "Passed Date", "status": "Status"}, "status_col": {"approved": "Approved", "pending_approval": "Pending Approval", "rejected": "Rejected", "expired": "Expired", "canceled": "Canceled", "completed": "Completed", "certificates": "Certificates", "certificate": "Certificate", "get_cert_when": "You'll get certificate since {0}"}}}, "course_detail": {"content": "Contents", "status": {"COMPLETED": "Completed", "NOT_STARTED": "Not start", "IN_PROGRESS": "Learning", "PASSED": "Session Pending", "PENDING_APPROVAL": "Pending approval", "VERIFIED": "Verified", "APPROVED": "Approved", "REJECTED": "Rejected", "EXPIRED": "Expired", "CANCELED": "Canceled"}, "action_button_text": {"enrollment_unavailable": "Enrollment Unavailable", "enrollment_temporary_unavailable": "Enrollment Temporary Unavailable", "self_enrollment_unavailable": "Self-Enrollment Unavailable", "start_learning": "Start Learning", "resume_learning": "Resume Learning", "enroll_again": "Enroll Again", "pending_approval": "Pending Approval", "approved": "Approved", "rejected": "Rejected", "unenroll": "Unenroll", "completed": "Completed", "pending_result": "Pending Result", "expired": "Expired", "session_pending": "Session Pending", "choose_learning_session": "Choose Learning Session", "no_continue": "Unable To Resume Learning", "re_enroll_unavailable": "Unable To Re Enroll"}, "action_button_sub_text": {"self_enrollment_unavailable": "If you wish to enroll, please contact support.", "enrollment_temporary_unavailable": "Unable to check the conditions for enrollment.", "incompatible_course_requirements": "Incompatible course requirements", "pre_enrolled_not_cancelable": "To cancel enrollment, please contact support.", "no_learning_session_available": "No learning session available", "re_enroll_unavailable": "Because the specified period for re enroll the course has not yet been reached", "no_plan_package_license_available": "Because there is no license available for this course", "plan_package_license_expired": "Because this course has expired"}, "status_time": {"unlimited_learning_time": "Unlimited learning time", "start_at": "Start date: {0}", "expired_at": "End date: {0}", "submitted_at": "Submitted: {0}", "approved_at": "Approved: {0}", "rejected_at": "Rejected: {0}", "learning_days": "Learning duration: {0} Days", "learning_duration": "Learning duration: {0}", "remain_study_time": "Need to study another video or article: {0}", "finished_learning": "Finished learning"}, "tabs": {"detail": {"title": "Course details"}, "criteria": {"title": "Criteria"}, "achievement": {"title": "Achievement", "reward": "<PERSON><PERSON>", "badge": "Badge", "certificate": "Certificate", "criteria_achievement": "Achievement Criteria", "learning_progress": "Learning Progress", "criteria": "Criteria", "learning_progress_equal": "Learning progress equal to", "learning_progress_greater_than_or_equal": "Learning progress greater than or equal to", "time_spent": "Time Spent Learning", "time_spent_course": "Total course learning time greater than or equal to {0}% (video and article only)", "time_spent_course_item": "Each content learning time greater than or equal to {0}% (video and article only)", "quiz": "Quiz", "quiz_last_post_test_score_more_equal": "Last enabled post-test score greater than or equal to", "quiz_all_post_test_score_more_equal": "All enabled post-test scores greater than or equal to", "completed_date": "Completed Date", "completed": "Completed", "received_date": "Received Date", "content": "Contents", "view_certificate": "View Certificate"}, "curriculum": {"title": "Curriculum", "collapse": "Collapse", "expand": "Expand", "survey": "Survey", "item_count": "{{count}} items"}, "instructor": {"title": "<PERSON><PERSON><PERSON><PERSON>"}, "discussion": {"title": "Discussion"}, "document": {"title": "Documents", "document_name": "Document", "quested_date": "Quested Date", "due_date": "Due Date", "submission_date": "Submission Date", "status": "Status", "submit_document": "Submit Document"}, "quiz": {"title": "Quizzes", "quiz_name": "Quiz Name", "criteria": "Criteria", "score": "Score", "status": {"title": "Status", "pass": "Pass", "fail": "Fail"}, "detail": "Detail", "quiz_answer_history": {"modal": {"title": "Quiz Detail", "quiz_text": "Quiz", "quiz_enable_criteria": "Criteria", "quiz_disable_criteria": "Not Criteria", "score": "Score", "quiz_start": "Started At", "pass": "Pass", "fail": "Fail", "quiz_my_score": "Score", "status": "Status", "quiz_time_duration": "Time Duration", "quiz_time": "Quiz Time", "quiz_last_update": "Last Update", "times": "Times", "quiz_retest": "Retest", "remain": "<PERSON><PERSON><PERSON>", "unlimited": "Unlimited", "after_question_submit": "Question Submit", "after_quiz_submit": "Quiz Submit", "quiz_evaluated": "Evaluated", "question": "Question", "quiz_criteria": "Criteria", "minute": "Minute"}}}}, "select_round": {"current_step": "Step {{current_step}} of {{total_steps}}", "back": "Back", "next": "Next", "confirm_information": "Confirm Information", "steps": {"choose_round": {"title": "Choose a session", "please_choose_session": "Please choose a session", "description": "Please select the start date according to the session you prefer.", "start_date": "Start date", "end_date": "End date", "cancel": "Cancel", "start": "Start", "end": "End", "select": "Select"}, "confirm_select": {"title": "Confirm enrollment information", "description": "Please verify the details before confirming the information. If the information is incorrect, please contact support.", "personal_information": {"title": "Personal Information", "full_name": "Full name (Thai)", "national_id": "National ID number (13 digits)", "email": "Email", "phone_number": "Phone number"}, "oic_license_information": {"title": "Insurance Information", "license_number": "License number", "issued_date": "Issued date (B.E.)", "expired_date": "Expired date (B.E.)"}, "tsi_license_information": {"title": "Investment Information", "license_number": "License number", "issued_date": "Issued date (B.E.)", "expired_date": "Expired date (B.E.)"}, "course_information": {"title": "Course Information", "content_name": "Content name", "start_date": "Start date", "end_date": "End date"}}, "warning_check_enroll": {"title": "Your license information has expired. from the date you selected the course", "content": "Please select a round before your license expires.", "button_ok": "Choose a new round"}}}, "description": {"title": "Course Information", "expand": "Expand", "collapse": "Collapse"}, "quiz": {"table": {"title": "Quizzes", "quiz_name": "Quiz Name", "criteria": "Criteria", "score": "Score", "status": {"title": "Status", "pass": "Pass", "fail": "Fail"}}}, "classroom": {"table": {"name": "Classroom Name", "attendance_criteria": "Attendance Criteria", "attendance_score": "Number of classes attended", "home_worker_criteria": "Homework Scoring Criteria", "home_worker_score": "Homework Score", "classroom_status": {"title": "Classroom results", "pass": "Pass", "fail": "Fail"}}}}, "enrollment": {"status": {"ALL": "All", "NOT_STARTED": "Not start", "IN_PROGRESS": "In progress", "PENDING_RESULT": "Pending Result", "PASSED": "Passed", "COMPLETED": "Completed", "PENDING_APPROVAL": "Pending approval", "VERIFIED": "Verified", "APPROVED": "Approved", "REJECTED": "Rejected", "EXPIRED": "Expired", "CANCELED": "Canceled", "PRE_ENROLLMENT": "Waiting to start", "PRE_ENROLLMENT_REJECTED": "Rejected"}, "enroll_status": {"success": "Enrollment Success", "pre_enrollment_success": "Pre Enrollment Success", "failed": "Enrollment failed, Please try again"}, "action": {"reset_learning_progress_success": "Reset enrollment status success", "reset_learning_progress_error": "Some thing wrong. <PERSON><PERSON> reset learning progress", "update_enrollment_status_success": "Update enrollment status success", "update_enrollment_status_error": "Some thing wrong. Cannot update status"}}, "round": {"detail": "Round Detail", "manage_course": "Manage Course", "information": {"firstRegistrationDate": "First Registration Date", "lastRegistrationDate": "Last Registration Date", "noExpiredDate": "No expired date", "status": "Round status", "manage": "Manage"}, "searchBar": {"startDate": "Start date", "endDate": "End date"}, "roundDate": "Round Date", "status": "Status", "all": "All", "active": "Active", "inActive": "Inactive", "trainingDate": "Training Date", "action": {"request_round_detail_error": "Not found this round in system", "add_course_in_round_success": "Add content success", "add_course_in_round_error": "Some thing wrong. Cannot add this content in round", "remove_course_in_round_success": "Remove content success", "remove_course_in_round_error": "Some thing wrong. Cannot remove this content in round", "remove_course_in_round_reserve_error": "Some thing wrong. Cannot remove this content in round", "delete_round_success": "Delete round success", "delete_round_reserve_error": "Some thing wrong. Cannot delete this round", "delete_round_error": "Some thing wrong. Cannot delete this round"}, "button": {"remove_round": "Remove Round", "add_content": "Add Content", "remove_content_in_round": "Remove Content", "bulk_round": "Bulk Round"}, "message": {"bulk_success": "Bulk round success"}, "bulk_round": {"title": "Bulk Rounds", "choose_round_type": "Select type to bulk round", "course": "Course", "learning_path": "Learning Path", "next_button": "Next", "bulk_learning_path_round": "Bulk learning path rounds"}, "tab": {"course": {"title": "Course", "table": {"enable": "Enable", "disable": "Disable", "total": "Total {0} records"}}, "learning_path": {"title": "Learning Path", "table": {"code": "Learning path code", "name": "Learning path name", "status": "Learning path status", "manage": "Manage", "enable": "Enable", "disable": "Disable", "total": "Total {0} records"}, "dropdown": {"placeholder": "Search Learning Path Code/Name", "add_button": "Add learning path"}}}, "modal": {"remove_title": "Do you want to delete round?", "remove_message_line_1": "Round", "remove_message_line_2": "Round cannot be brought back after deleted.", "course": {"remove_title": "Do you want to remove course?", "remove_message_line_1": "Remove course", "remove_message_line_2": "From round"}, "learning_path": {"remove_title": "Do you want to remove learning path?", "remove_message_line_1": "Remove learning path", "remove_message_line_2": "From round"}}}, "license.oic": "OIC License", "license.oic.life": "Life", "license.oic.non.life": "Non-Life", "license.tsi": "TSI License", "license.tsi.type": "Investment License", "license.tfac.cpa": "Certified Public Accountant", "license.tfac.ra": "Accountant", "applicant_type": {"agent": "Agent", "broker": "Broker"}, "course": {"productSKUId": "Product SKU ID", "code": "Code", "image": "Image", "thumbnail": "<PERSON><PERSON><PERSON><PERSON>", "name": "Name", "contentType": "Content Type", "objective": "Objective", "regulator": "Regulator", "source": "Source", "instructor": "<PERSON><PERSON><PERSON><PERSON>", "certificate": "Certificate", "selfEnroll": "Self-Enroll", "round": "Round", "status": "Status", "isEnabled": "Status", "credit": "Credit", "totalCredit": "{0} credits", "productSKUCode": "ProductSKUCode", "createdAt": "Created At", "updatedAt": "Updated At", "contentProvider": "Content Provider", "isActivated": "Activated", "manage": "Manage", "objectiveType": "Objective Type", "contentProviderType": "Content Provider", "description": "Description", "edit": "Edit", "general": "General", "detail": "Detail", "overview": "Overview Summary", "trainingCenter": "Training Center", "applicantType": "Applicant Type", "licenseRenewal": "License Renewal", "licenseType": "License Type", "learn": "Learning", "version": "Version {0}", "button": {"delete": "Delete", "edit_tootip": "Cannot be edited"}, "card": {"access": {"title": "Access", "access_type": "Access type", "user_group": "User groups"}, "oic_report": {"title": "Information for OIC reports", "example_template": "Download Example Template", "button": {"delete": "Delete Data", "import": "Import Data"}}, "tsi_report": {"title": "Setting TSI Course", "button": {"edit": "Edit"}, "detail": {"tsi_code": "TSI Code", "pillar_name": "Pillar Name"}, "drawer": {"edit": {"title": "Edit: Setting TSI Course", "tsi_code_placeholder": "Enter TSI Code", "pillar_name_placeholder": "<PERSON><PERSON>", "tsi_code_required": "Please enter TSI Code", "pillar_name_required": "Please enter pillar name"}}}, "activated": {"yes": "Yes", "no": " No"}}, "drawer": {"edit_access": {"title": "Edit: Access", "access_type": {"title": "User group access", "label": "Access conditions"}, "user_groups": {"title": "User Group", "choose": "Select user groups", "search": "Search", "add": "Add", "name": "Name", "count": "Count", "status": "State", "manage": "Manage", "remove": "Remove", "total": "Total {0} records", "error_at_least_1_user": "Choose at least 1 user group"}}, "oic_report": {"title": "Import Data OIC Report"}}, "modal": {"oic_report": {"confirm_delete": {"title": "Confirm delete", "description": "Are you sure to delete this operation cannot be brought back."}, "confirm_import": {"title": "Confirm import", "description1": "import operation", "description2": "old data is always replaced with new data.", "description3": "and cannot be brought back."}}, "confirm_delete": {"title": "Do you want to delete this content?", "content_line_1": "Content “{0}”", "content_line_2": "Once deleted, it can't be revert"}}, "message": {"update_access_success": "Update successfully", "update_access_fail": "Update fail", "fetch_user_group_dropdown_fail": "Something went wrong, Can't search user groups", "fetch_user_group_detail_fail": "Something went wrong Can't add user groups", "delete_success": "Delete successfully", "delete_error": "Something went wrong, Can't delete", "import_success": "Import data successfully", "manage": {"delete_success": "Delete content successfully", "delete_error": "Something went wrong, Can't delete this content"}, "update_course_certificate_success": "Update successfully", "update_course_instructor_success": "Added instructor successfully"}, "access_type": {"public": "Public", "private": "Private", "user_groups": "Specific usergroups"}, "oic_report": {"diploma_name": "Diploma Name", "course_short_name": "Course Short Name", "course_code": "Course Code", "course_name": "Course Name", "subject_code": "Subject Code", "subject_name": "Subject Name", "pillar_name": "Pillar Name", "training_type": "Training Type", "training_duration": "Training Duration"}, "course_version": {"version": "version", "published_at": "Latest published {0}", "status": {"draft": "Drafting", "preview": "Preview", "published": "Publishing"}, "alert": {"first_published_draft_success": "Successfully published course", "published_draft_success": "New version successfully published", "new_draft_success": "New version successfully drafted.", "overwrite_draft": {"description": "This new draft version is an overlay of an existing draft version. In the system, once a draft version is created, the original version cannot be restored.", "title": "Confirm new draft version?"}, "cancel_draft": {"description": "When canceling a draft, the system will not be able to restore the draft again.", "title": "Confirm cancellation of draft?", "success": "Successfully canceled draft"}}}, "history": {"title": "Version history", "current_published_version": "Current published version", "version": "version", "published_date": "Published at", "by": "by", "all_version": "All versions", "status": {"published": "Published", "draft": "Draft"}, "table": {"version": "Version", "status": "Status", "created_date": "Created at", "published_date": "Published at", "published_by": "Published by"}}}, "category": {"title": "Category", "all_content": "All study materials", "detail": {"tabs": {"course_list.label": "Course list"}, "drawer": {"title": "Edit: Content", "add_content.button": "Add content"}}, "edit_order": {"alert": {"invalid_level": "Not allow to update exceed 4 levels"}, "drawer": {"title": "Sorting", "card": {"title": "Category order"}}}}, "learning_settings": {"status": {"enabled": "Enabled", "disabled": "Disabled"}, "regular": {"title": "Regular Setting", "settings": {"seek": "Can seek", "speed": "Can change video speed", "article_countdown": "Timer countdown on article", "play_video_background": "Stop playing video when switching tabs"}}, "attention_check": {"title": "Attention Check Setting", "settings": {"attention_check": "Attention check", "learnable_fullscreen": "Learnable only full screen mode"}}, "authen": {"title": "Identity Verification Setting", "type": {"card": "Card", "face": "Face"}, "settings": {"ocr": "OCR", "rpc": "RPC", "liveness": "Liveness"}}, "edit": {"regular": {"title": "Edit: Regular Setting", "settings": {"seek": "Enable seek video", "speed": "Enable change video speed", "article_countdown": "Enable timer countdown on article", "play_video_background": "Enable stop playing video when switching tabs"}}, "attention_check": {"title": "Edit: Attention Check Setting", "settings": {"attention_check": "Enable attention check", "learnable_fullscreen": "Learnable only full screen mode"}}, "authen": {"title": "Edit: Identity Verification Setting", "settings": {"ocr": "OCR", "rpc": "RPC", "liveness": "Liveness"}}}}, "course_items": {"c": {"tooltip": {"course_is_published": {"enabled": "Cannot disable the course, the course has already been published", "disabled": "Cannot enable the course, the course has already been published"}}}, "article": {"article": "Article"}, "survey": {"survey": "Survey"}, "video": {"video": "Video", "status": "Status", "basic_information": "Basic Information", "attachment": "Attachments ({0})", "c": {"tooltip": {"error_enabled_video": "Cannot be enabled because there is no video", "no_video": "There are no usable videos"}, "alert": {"transcoding": "The video takes approximately 10-20 minutes to process, depending on the file size. You can close this window while it's processing", "transcodeFailed": "Video transcode failed, please try again", "transcodeSuccess": "Video transcode success", "uploading": "The video is currently uploading. Please do not exit this window.", "uploadFailed": "Video upload failed. Please try again.", "upload_video_not_complete": "Video uploaded not complete. Please try again."}, "modal": {"discard_message": "Once discarded, it cannot be retrieved.", "discard_title": "Do you want to discard all the data?"}, "drawer": {"cannot_save_video": "Cannot save due to video uploading.", "cannot_save_video_not_complete": "Cannot save due to video not complete."}}, "d": {"edit_video": "Edit: Video", "form": {"video_name_label": "Video Name", "video_name_required": "Please enter the video name.", "video_placeholder": "Enter the video name", "description_label": "Description", "description_required": "Enter Description"}}}}, "achievement": {"title": "Achievement", "title_placeholder": "Achievement Name", "name": "Achievement Name", "type": "Achievement Type", "isEnabled": "Achievement Status", "updatedAt": "Last Updated", "action": "Edit", "setting_badge": "<PERSON>ge Set<PERSON>s", "enable_badge": "Enable Badge", "create_drawer": {"title": "Create Achievement", "save": "Create", "basic_info": {"header": "Achievement Information", "label": "Achievement Name", "placeholder": "Enter Achievement Name", "required": "Please enter the Achievement Name"}, "description": {"label": "Description", "placeholder": "Enter Description"}, "validations": {"name": {"more_than_maximum": "The Achievement name should not exceed {0} characters", "consecutive_space": "The Achievement name cannot have consecutive spaces", "end_with_space": "The Achievement name cannot end with a space", "start_with_space": "The Achievement name cannot start with a space", "duplicated": "This Achievement name already exists"}, "description": {"more_than_maximum": "The description should not exceed {0} characters", "consecutive_space": "The description cannot have consecutive spaces", "end_with_space": "The description cannot end with a space", "start_with_space": "The description cannot start with a space"}}}, "edit_drawer": {"title": "Edit: Achievement Details", "save": "Save", "basic_info": {"header": "Achievement Details", "label": "Achievement Name", "placeholder": "Enter Achievement Name", "required": "Please enter the Achievement Name"}, "description": {"label": "Description", "placeholder": "Enter Description"}, "validations": {"name": {"more_than_maximum": "The Achievement name should not exceed {0} characters", "consecutive_space": "The Achievement name cannot have consecutive spaces", "end_with_space": "The Achievement name cannot end with a space", "start_with_space": "The Achievement name cannot start with a space", "duplicated": "This Achievement name already exists"}, "description": {"more_than_maximum": "The description should not exceed {0} characters", "consecutive_space": "The description cannot have consecutive spaces", "end_with_space": "The description cannot end with a space", "start_with_space": "The description cannot start with a space"}}}, "edit_criteria_drawer": {"title": "Edit: <PERSON> <PERSON><PERSON><PERSON>", "save": "Save", "card_type": {"header": "Achievement Criteria Settings", "label": "Registration Type", "placeholder": "Select Criteria Type", "required": "Please select a Criteria Type"}, "basic_info": {"header": "Achievement Details", "label": "Achievement Name", "placeholder": "Enter Achievement Name", "required": "Please enter the Achievement Name"}, "description": {"label": "Description", "placeholder": "Enter Description"}, "validations": {"name": {"more_than_maximum": "The Achievement name should not exceed {0} characters", "consecutive_space": "The Achievement name cannot have consecutive spaces", "end_with_space": "The Achievement name cannot end with a space", "start_with_space": "The Achievement name cannot start with a space", "duplicated": "This Achievement name already exists"}, "description": {"more_than_maximum": "The description should not exceed {0} characters", "consecutive_space": "The description cannot have consecutive spaces", "end_with_space": "The description cannot end with a space", "start_with_space": "The description cannot start with a space"}}}, "edit_badge_drawer": {"title": "Edit: <PERSON><PERSON>", "setting_badge": "<PERSON>ge Set<PERSON>s", "enable_badge": "Enable Badge", "badges": "Badges ({0})", "select_badge": "Select Badge", "find_badge": "Search Badge", "add": "Add", "error_save_modal": {"title": "Unable to Save", "message_content_1": "Due to incomplete information, including:", "message_content_2": "Please check and complete the required information", "message_content_3": "before saving again later.", "no_badge": "No active badges available"}}, "edit_date_range_drawer": {"title": "Edit: Set Achievement Start and End Dates", "setting_date_range": "Set the date range affecting achievement", "start_enrollment": "Start Date of Learning", "no_specified_start_enrollment": "No specified start date", "set_start_enrollment": "Set start date", "start_active_time": "Set effective start time", "select_start_enrollment": "Please select the start date of learning", "end_enrollment": "End Date of Learning", "no_specified_end_enrollment": "No specified end date", "set_end_enrollment": "Set end date", "end_active_time": "Set effective end time", "select_end_enrollment": "Please select the end date of learning", "select_date": "Please select a date", "select_end_after_start": "Please select an end date after the start date"}, "footer": {"status": "Achievement Status", "save_status": "Save Status", "publish": "Publish Achievement"}, "detail": {"not_found": "Data not found", "title_page_header": "Achievement: {0}", "title": "Achievement Information", "name": "Achievement Name", "reward": "<PERSON><PERSON>", "certificate": "Certificate", "badge": "Badge", "description": "Description", "type": "Registration Type", "condition_setting": "Achievement Criteria Settings", "all_type": "All", "compulsory": "Compulsory", "voluntary": "Voluntary", "enable": "Enable", "disable": "Disable", "delete": "Delete Achievement", "published_at": "Last Published {0}", "setting_date_range": "Set the date range affecting achievement", "impact_start_date": "Start date affecting achievement", "no_specified_start_date": "No specified start date", "no_specified_end_date": "No specified end date", "impact_all_date": "Affects all time periods", "criteria": {"title": "Achievement Criteria", "learning": "Learning", "learning_progress": "Learning Progress", "enable_learning_progress": "Enable Learning Progress", "learning_progress_more_equal": "Progress greater than or equal to", "time_spent": "Time Spent Learning", "tooltip_time_spent": "Time includes only videos and articles with reading time enabled", "enable_time_spent": "Enable Time Spent Learning", "time_spent_course_more_equal": "Total course learning time greater than or equal to", "time_spent_course_item_more_equal": "Each content learning time greater than or equal to", "quiz": "Quiz", "enable_quiz": "Enable Quiz", "quiz_last_post_test_score_more_equal": "Last enabled post-test score greater than or equal to", "quiz_all_post_test_score_more_equal": "All enabled post-test scores greater than or equal to"}}, "message": {"save_success": "Data saved successfully", "save_fail": "An error occurred. Unable to save data", "publish_success": "Published successfully", "enable_success": "Enabled successfully", "disable_success": "Disabled successfully", "enable_fail": "An error occurred. Unable to enable", "disable_fail": "An error occurred. Unable to disable", "delete_success": "Achievement deleted successfully", "delete_fail": "An error occurred. Unable to delete data", "save_criteria_fail": "Please enable at least one criterion", "error_invalid_reward": "You cannot disable earned rewards because you must enable at least one earned reward"}, "modal": {"discard_message": "Once discarded, it cannot be retrieved", "discard_title": "Do you want to discard all data?", "publish_title": "Confirm Achievement Publication", "publish_line_1": "Do you confirm to publish the Achievement", "publish_line_2": "or not?", "publish_ok": "Publish", "delete_title": "Do you want to delete this Achievement?", "delete_line_1": "Do you want to delete the Achievement", "delete_line_2": "Once deleted, it cannot be retrieved", "delete_ok": "Delete", "error_invalid_reward_title": "Unable to publish the achievement", "error_invalid_reward_content": "Because at least one reward has not been activated. Please add it in the 'Rewards' menu"}, "toast": {"create_success": "Create successfully", "create_error": "Error"}}, "announcement": {"thumbnail": "Image", "title": "Title", "title_placeholder": "Topic of news and announcement", "authors": "Authors", "publishedStartAt": "Published start at", "publishedEndAt": "Published end at", "status": "Status", "status_type": {"all": "All", "draft": "Draft", "published": "Published", "unpublished": "UnPublished", "scheduled": "Scheduled", "expired": "Expired"}, "updatedAt": "Updated at", "action": "Action", "manage": {"header": "News And Announcements", "menus": {"overview": "Overview", "access": "Access"}, "published_at": "Published at", "published_without_expired": "Published at", "scheduled_at": "Scheduled at", "latest_published_at": "Latest published at", "expired_at": "Expired at", "overview": {"header": "Overview", "title": "title", "content": "content", "authors": "authors", "thumbnail": "thumbnail", "size": "size"}, "attachments": "attachments"}, "create_drawer": {"title": "Create news and announcements", "save": "Create", "basic_info": {"header": "Basic Information", "label": "News and Announcements", "placeholder": "Enter title", "required": "Please enter title"}, "validations": {"name": {"less_than_minimum": "The length of the announcement name must be longer than equal 3 characters", "more_than_maximum": "The length of the announcement name must be less than equal 200 character", "consecutive_space": "Announcement name can't have consecutive space", "end_with_space": "Announcement name can't end with space", "start_with_space": "Announcement name can't start with space"}}}, "edit_overview_drawer": {"title": "Edit: Overview", "save": "Save", "discard": "Discard", "overview": {"header": "Overview", "title": {"label": "Title", "placeholder": "Enter title", "required": "title is required"}, "content": {"label": "content"}}, "authors": {"header": "authors", "label": "add authors", "placeholder": "Enter author's name", "add": "Add", "table": {"order": "Order", "author_name": "Name", "action": "Action", "delete": "Delete"}, "existed": "This authors already existed."}, "thumbnail": {"header": "<PERSON><PERSON><PERSON><PERSON>", "size": "Size", "sizeLimit": "Size limit", "supportedFiles": "Supported files", "chooseFile": "Choose <PERSON>", "fileNotFound": "File Not Found", "alert": "Select image only .png, .jpg,.jpeg and size limit 30 MB"}, "validations": {"name": {"less_than_minimum": "The length of the announcement name must be longer than equal 3 characters", "more_than_maximum": "The length of the announcement name must be less than equal 200 character", "consecutive_space": "Announcement name can't have consecutive space", "end_with_space": "Announcement name can't end with space", "start_with_space": "Announcement name can't start with space"}}}, "edit_attachment_drawer": {"title": "Edit: Overview", "description": "Supported file only {0} and can drag to sort", "upload_description": "Supported file only {0} and limit size 30MB", "save": "Save"}, "footer": {"status": "Status", "button": {"publish": "Publish", "cancel_publish": "Unpublish", "edit_publish_duration": "Edit publish duration", "edit_expired_datetime": "Edit expired datetime", "define_expired_datetime": "Add expired datetime"}}, "modal": {"manage_publish_duration": {"title": {"confirm_publish": "Confirm publish duration news and announcement", "edit_publish_duration": "Edit publish duration of news and announcement", "edit_expired_datetime": "Edit expired datetime"}, "description": "You can schedule publish's datetime and expired datetime", "publish": {"label": "Publishing", "placeholder_date": "Choose date", "placeholder_time": "Choose time", "publish_now": "Publish now", "scheduled_publish": "Schedule publish datetime", "required_date": "Publish's date is required", "required_time": "Publish's time is required"}, "expired": {"label": "Expiration", "placeholder_date": "Choose date", "placeholder_time": "Choose time", "no_expiration": "No expiration", "define_expired_datetime": "Define expired datetime", "required_date": "Expired's date is required", "choose_date_after_publish": "Choose date after publish date", "required_time": "Expired's time is required", "choose_time_after_publish": "Choose time after publish datetime"}, "confirm": "Confirm", "discard": "Discard"}, "confirm_cancel_scheduled_publish": {"title": "Confirm unpublish", "description": "Are you sure to unpublish?"}, "confirm_unpublish": {"title": "Confirm unpublish", "description": "Are you sure to unpublish?"}, "confirm_delete": {"title": "Confirm delete", "description": "Are you sure to delete “{0}” this operation can't be undone"}}}, "badge": {"title": "Badge", "title_placeholder": "Badge Name", "type": "Badge Type", "name": "Badge Name", "thumbnail": "Badge Icon", "description": " Badge Description", "isEnabled": "Badge Status", "updatedAt": "Last Updated", "action": "Edit", "create_drawer": {"title": "Create Badge", "save": "Create", "basic_info": {"header": "Badge Information", "label": "Badge Name", "placeholder": "Enter Badge Name", "required": "Please enter the Badge Name"}, "description": {"label": "Description", "placeholder": "Enter Description"}, "validations": {"name": {"more_than_maximum": "The Badge name should not exceed {0} characters", "consecutive_space": "The Badge name cannot have consecutive spaces", "end_with_space": "The Badge name cannot end with a space", "start_with_space": "The Badge name cannot start with a space", "duplicated": "This Badge name already exists"}, "description": {"more_than_maximum": "The description should not exceed {0} characters", "consecutive_space": "The description cannot have consecutive spaces", "end_with_space": "The description cannot end with a space", "start_with_space": "The description cannot start with a space"}}}, "edit_drawer": {"title": "Edit: General Information", "save": "Save", "basic_info": {"header": "General Information", "label": "Badge Name", "placeholder": "Enter Badge Name", "required": "Please enter the Badge Name"}, "badge_image": {"header": "Badge Image", "thumbnail": {"alert": "Select image only .png, .jpg,.jpeg and size limit 2 MB", "size": "Size", "sizeLimit": "Size limit", "supportedFiles": "Supported files", "chooseFile": "Choose <PERSON>"}}, "description": {"label": "Description", "placeholder": "Enter Description"}, "validations": {"name": {"more_than_maximum": "The Badge name should not exceed {0} characters", "consecutive_space": "The Badge name cannot have consecutive spaces", "end_with_space": "The Badge name cannot end with a space", "start_with_space": "The Badge name cannot start with a space", "duplicated": "This Badge name already exists"}, "description": {"more_than_maximum": "The description should not exceed {0} characters", "consecutive_space": "The description cannot have consecutive spaces", "end_with_space": "The description cannot end with a space", "start_with_space": "The description cannot start with a space"}}}, "footer": {"status": "Badge Status", "save_status": "Save Status", "publish": "Publish Badge"}, "detail": {"not_found": "Data not found", "title_page_header": "Badge: {0}", "title": "Badge Information", "name": "Badge Name", "description": "Description", "all_type": "All", "enable": "Enable", "disable": "Disable", "delete": "Delete Badge", "published_at": "Last Published {0}"}, "message": {"save_success": "Data saved successfully", "save_fail": "An error occurred. Unable to save data", "publish_success": "Published successfully", "enable_success": "Enabled successfully", "disable_success": "Disabled successfully", "enable_fail": "An error occurred. Unable to enable", "disable_fail": "An error occurred. Unable to disable", "delete_success": "Badge deleted successfully", "delete_fail": "An error occurred. Unable to delete data"}, "modal": {"discard_message": "Once discarded, it cannot be retrieved", "discard_title": "Do you want to discard all data?", "publish_title": "Confirm Badge Publication", "publish_line_1": "Do you confirm to publish the Badge", "publish_line_2": "or not?", "publish_ok": "Publish", "delete_title": "Do you want to delete this Badge?", "delete_line_1": "Do you want to delete the Badge", "delete_line_2": "Once deleted, it cannot be retrieved", "delete_ok": "Delete", "error_update_status": {"title": "Unable to save", "message_line_1": "This badge is the final reward of", "message_line_2": "Please check and complete the required information", "message_line_3": "before saving again later"}}}, "knowledge_content": {"banner": {"title": "Knowledge Content", "description": "Including all types of knowledge content, You can access and study quickly"}, "section": {"highlight": "Recommend", "popular": "Popular", "latest": "New", "category": "Explore by knowledge content type", "all_content": "All knowledge content"}, "filter": {"title": "Filter", "type": "knowledge content type", "category": "knowledge content category", "result_amount": "amount", "result_unit": "records", "no_result": {"title": "not found", "description": "please choose other filter or reset filter"}}, "no_content": "No knowledge content"}, "knowledge_content_category": {"title": "Title", "code": "Code", "thumbnail": "<PERSON><PERSON><PERSON><PERSON>", "totalContent": "Total Content", "createdAt": "Created At", "updatedAt": "Updated At", "status": "Status", "action": "Action", "banner": {"title": "Category{0}", "description": "Including all types of knowledge content, You can access and study quickly"}, "create_drawer": {"title": "Create Knowledge Content Category", "save": "Create", "header": "Basic Information", "form": {"title": {"label": "Title", "placeholder": "Enter title"}, "validator": {"required": "Please enter title", "maxlength": "Title maximum {0} characters", "start_with_space": "Title can't start with space", "end_with_space": "Title can't end with space", "consecutive_space": "Title can't have consecutive space", "title_error_required": "Please enter title"}, "type": {"label": "Type", "placeholder": "Select type", "required": "Please select type"}, "code": {"label": "Code", "placeholder": "Enter Code", "required": "Please enter code.", "existed": "This code already existed.", "invalid": "Only upper characters (A-Z), number (0-9), and underscore (_) are allowed."}, "random_code": "Generate"}}, "message": {"create": {"success": "Create knowledge content category success", "error": "Something went wrong, Could not create"}}, "modal": {"confirm_delete": {"title": "Confirm delete", "content": "Are you sure to delete “{0}” this operation can't be undone"}, "confirm_discard": {"title": "Do you want to discard?", "content": "Once an abandonment is performed, it cannot be brought back."}}, "learner": {"title": "Explore knowledge media by category"}}, "dashboard_knowledge_content": {"download": "Download", "page_header": "Knowledge content dashboard", "title": "Knowledge content name", "title_placeholder": "Fill knowledge content name", "type": "Knowledge content type", "category_name": "Knowledge content category", "category_placeholder": "Fill knowledge content category", "popular_content": {"title": "Top 10 popular knowledge contents", "knowledge_content_title": "Knowledge contents", "count_total_user": "Count of users", "count_total_view": "Count of view"}, "search_result": "Search Result", "summary": {"total_user": "Total user", "total_view": "Total view", "total_like": "Total like", "total_rating": "Total rating", "total_comment": "Total comment", "total_share": "Total share"}, "line_chart": {"title": "Number of users and number of views over time", "total_view": "Total view", "total_user": "Total user", "date": "Date"}, "notification": {"start_download": "{0} is downloading", "loading": "Please wait a moment", "download_success": "{0} has been downloaded successfully.", "verify_file": "Please check the downloaded file.", "download_error": "{0} has been downloaded failed", "error_file": "There was an error downloading the file"}, "chart": {"age_chart_title": "Number of users by age", "gender_chart_title": "Number of users by gender", "media_type_chart_title": "Number of users and number of views by type of knowledge media", "category_chart_title": "Number of users and number of views by category", "media_type_zone_chart_title": "Number of views of knowledge media types by zone", "category_position_chart_title": "Number of views of knowledge media categories by position level", "count_total_user": "Number of users", "count_total_view": "Number of views"}, "dashboard_table": {"title": "All knowledge content name", "name": "Knowledge content name", "total_user": "Total user", "total_view": "Total view", "total_like": "Total like", "average_views_per_user": "Average views per user", "average_rating": "Average rating", "total_comment": "Total comment", "total_click_copy": "Total click copy", "total_download": "Total download", "total_play": "Total play", "total_playtime": "Total playtime", "average_playtime": "Average playtime", "average_playtime_per_user": "Average playtime per user", "publication_date": "Publication date"}, "undefined": "Undefined", "more_than_years": "Over {0} years old", "less_than_years": "Less than {0} years"}, "knowledge_content_item": {"page_header": "Knowledge Content: {0}", "type": {"all": "All", "article": "article", "ebook": "ebook", "podcast": "podcast", "video": "video"}, "card": {"title": {"attachments": "Attachments", "instructor": "Instructor ({0})", "overview": "Overview", "thumbnail": "<PERSON><PERSON><PERSON><PERSON>", "comment": "Comment"}}, "date": {"published_at": "published at", "latest_published_at": "latest published at", "expired_at": "expired at"}, "download_file": {"message": {"success": "Successfully downloaded waiting for the file", "error": "There was an error downloading the file"}}, "status": {"all": "All", "draft": "Draft", "published": "Published", "unpublished": "UnPublished", "scheduled": "Scheduled", "expired": "Expired"}, "detail": {"code": "Code", "delete": "Delete", "description": "Description", "instructors": "All Instructors", "thumbnail_size": "size: 660x390 px", "thumbnail": "<PERSON><PERSON><PERSON><PERSON>", "title_placeholder": "Please enter email.", "title": "Title", "type": "Type", "view_ebook": "View E-Book", "rating": "Rating", "copy_link": "Copy Link", "download": "Download", "expand_description": "Expand Description", "hide_description": "Hide Description", "attachment": "Attachment", "rating_vote": "Rating <PERSON><PERSON>", "rating_reset": "Reset"}, "comment": {"discussion_board": "Discussion Board", "enabled": "Enabled", "disabled": "Disabled"}, "create_drawer": {"title": "Create Knowledge Content Item", "save": "Create", "header": "Basic Information", "form": {"title": {"label": "Title", "placeholder": "Enter title", "required": "Please enter title"}, "validator": {"required": "Please enter title", "maxlength": "Title maximum {0} characters", "start_with_space": "Title can't start with space", "end_with_space": "Title can't end with space", "consecutive_space": "Title can't have consecutive space", "title_error_required": "Please enter title"}, "type": {"label": "Type", "placeholder": "Select type", "required": "Please select type"}, "code": {"label": "Code", "placeholder": "Enter Code", "required": "Please enter code.", "existed": "This code already existed.", "invalid": "Only upper characters (A-Z), number (0-9), and underscore (_) are allowed."}, "randomCode": "Generate"}}, "edit_attachment_drawer": {"title": "Edit: Overview", "description": "Supported file only {0} and can drag to sort", "upload_description": "Supported file only {0} and limit size 30MB", "save": "Save", "attachment": "Attachments ({0})"}, "edit_overview_drawer": {"title": "Edit: Overview", "overview": {"content": {"label": "Description"}}, "thumbnail": {"alert": "Select image only .png, .jpg,.jpeg and size limit 30 MB"}}, "edit_is_comment_enabled_drawer": {"title": "Edit: Comment <PERSON>ting", "comment_title_setting": "Comment Setting", "enabled": "Enabled Discussion Board"}, "edit_pin_drawer": {"title": "Pin highlight", "btn_cancel": "Cancel", "btn_submit": "Save", "form": {"btn_submit": "Add content", "max_length_tooltip": "จำกัดสูงจุด 20 รายการ หากต้องการเพิ่ม กรุณานำหัวข้อสื่อความรู้อื่นออกก่อน", "select_content_placeholder": "Knowledge content title"}, "message": {"save": {"success": "Pin knowledge contents success", "error": "Something went wrong, Can not pin knowledge contents"}, "pin": {"error": "Something went wrong, Can not add knowledge content"}}, "modal": {"confirm": {"title": "Confirm pin information", "content": "Do you want to confirm the Knowledge content pin?"}, "discard": {"title": "Do you want to discard?", "content": "Once an abandonment is performed, it cannot be brought back."}}, "table": {"header": {"row_number": "Number", "code": "Code", "thumbnail_url": "<PERSON><PERSON><PERSON><PERSON>", "title": "Title", "type": "Type", "category": "Category", "created_at": "Created At", "updated_at": "Updated At", "published_start_at": "Publication date", "is_download_enabled": "Download enabled", "status": "Status", "action": "Action"}, "row": {"enabled": "Enabled", "disabled": "Disabled", "remove": "Remove"}}}, "edit_instructor": {"label": "Sort by author", "placeholder": "Type to add the author's name", "create_button": "Create a new author", "add_button": "Add an author"}, "menu": {"detail": "Detail", "categories": "Categories ({0})", "comment": "Comment"}, "footer": {"status": "Status", "button": {"publish": "Publish", "cancel_publish": "Unpublished", "edit_publish_duration": "Edit publish duration", "edit_expired_datetime": "Edit expired datetime", "define_expired_datetime": "Add expired datetime"}}, "modal": {"manage_publish_duration": {"title": {"confirm_publish": "Confirm publish duration news and knowledge content", "edit_publish_duration": "Edit publish duration of news and knowledge content", "edit_expired_datetime": "Edit expired datetime"}, "description": "You can schedule publish's datetime and expired datetime", "publish": {"label": "Publishing", "placeholder_date": "Choose date", "placeholder_time": "Choose time", "publish_now": "Publish now", "scheduled_publish": "Schedule publish datetime", "required_date": "Publish's date is required", "required_time": "Publish's time is required"}, "expired": {"label": "Expiration", "placeholder_date": "Choose date", "placeholder_time": "Choose time", "no_expiration": "No expiration", "define_expired_datetime": "Define expired datetime", "required_date": "Expired's date is required", "choose_date_after_publish": "Choose date after publish date", "required_time": "Expired's time is required", "choose_time_after_publish": "Choose time after publish datetime"}, "confirm": "Confirm", "discard": "Discard"}, "confirm_cancel_scheduled_publish": {"title": "Confirm unpublish scheduled", "description": "Are you sure to unpublish scheduled?"}, "confirm_unpublish": {"title": "Confirm unpublish", "description": "Are you sure to unpublish?"}, "confirm_delete": {"title": "Confirm delete", "description": "Are you sure to delete “{0}” this operation can't be undone"}, "alert_media_transcode": {"title": "This knowledge content cannot be published.", "content": "Because the content is not yet available. Please check before activating again later."}}, "carousel_header": {"recommended_knowledge_media": "Recommended learning media", "popular_knowledge_media": "Popular knowledge media", "new_knowledge_media": "Latest new knowledge media"}, "message": {"link_copied": "<PERSON> copied."}}, "organization_report": {"button": {"download": "Download"}}, "oic_report": {"title": "Report", "download": {"success": "Successfully downloaded waiting for the report", "error": "There was an error downloading the report"}, "form": {"input": {"placeholder": {"round_start_at": "Start date", "round_end_at": "End date"}}, "label": {"applicant_type": "Applicant Type", "business_type": "Business Type", "license_renewal": "License Reward", "license_type": "License Type", "round_date": "Round Date", "status": "Status", "training_center": "Training Center", "report_type": "Report Type", "deduct_report_status": "Deduct Report Status"}, "selector": {"select_all_business_type": "All Business type", "select_tsi_type": "Select TSI license type", "select_type": "<PERSON><PERSON>", "pre_report": "Summary report of the number of trainees", "regulator_pre_report": "Report by course", "post_report": "Summary report of the number of trainee", "regulator_post_report": "Report by course", "placeholder": {"select_applicant_type": "Select Applicant Type", "select_business_type": "Select Business Type", "select_license_renewal": "Select License Reward", "select_license_type": "Select License Type", "select_round_date": "Please choose round date", "select_status": "Select Status", "select_training_center": "Select Training Center", "select_report_type": "Select report type"}}, "validate": {"error": {"select_applicant_type": "Please choose applicant type", "select_business_type": "Please choose business type", "select_license_renewal": "Please choose license reward", "select_license_type": "Please choose license type", "select_round_date": "Please choose round date", "select_status": "Please choose status", "select_training_center": "Please choose training center", "select_deduct_report_status": "Please choose deduct report status", "select_report_type": "Please select report type"}}}, "modal": {"title": {"pre_report": "Create Pre-Enrollment Report", "post_report": "Create Post-Enrollment Report", "deduct_report": "Create Deduct Report"}, "submit": "Download", "cancel": "Cancel"}, "table": {"header": {"title": "Report", "action": "Action"}, "row": {"btn": {"create_report": "Create Report"}, "pre_report": "Pre-Enrollment Report", "post_report": "Post-Enrollment Report", "deduct_report": "Deduct Report"}}}, "report": {"model_content_usage_type": {"package_license": "license", "cpd_credit": "cpd credit", "credit_package": "credit package"}}, "learner_report": {"form": {"input": {"placeholder": {"round_start_at": "Start date", "round_end_at": "End date"}}, "label": {"start_date_range": "Start date", "end_date_range": "End date", "objective_type": "Course Objective Type", "categories": "Course Category", "course": "Course", "learning_status": "Learning Status", "evaluate_result": "Evaluate Result", "regulator": "Regulator", "model_content_usage_type": "Course Usage Type", "plan": "Plan", "package": "Package"}, "validate": {"error": {"select_round_date": "Please choose round date"}}}, "modal": {"title": "Learner Report", "submit": "Download", "cancel": "Cancel"}, "default": {"all": "All"}, "status": {"all": "All", "not_start": "Not Start", "in_progress": "In Progress", "pending_result": "Pending Result", "passed": "Passed", "completed": "Completed", "pending_approval": "Pending Approval", "verified": "Verified", "approved": "Approved", "rejected": "Rejected", "expired": "Expired", "canceled": "Canceled", "pre_enroll_canceled": "Pre Enrollment Canceled"}, "evaluate_result_type": {"approved": "Approved", "rejected": "Rejected", "canceled": "Canceled", "not_evaluated": "Not Evaluated", "not_required": "Not Required"}, "objective_type": {"regular": "Regular", "oic": "OIC", "tsi": "TSI"}}, "enrollment_compulsory_report": {"form": {"label": {"model_content_usage_type": "Course Usage Type", "plan": "Plan", "package": "Package", "objective_type": "Course Objective", "course": "Course", "regulator": "Training Objective", "learning_status": "Learning Status", "department": "Department"}}, "default": {"all": "All"}, "modal": {"title": "Learning Report", "submit": "Download", "cancel": "Cancel"}}, "tsi_report": {"modal": {"title": "TSI Report", "label": {"round_date": "Round Date", "passed_date": "End Date", "course": "Course", "business_type": "Business Type", "status": "Status"}, "message": {"required_round_date": "Please choose round date", "required_business_type": "Please choose business type", "required_status": "Please choose status", "system_processing": "The System is processing.", "review_in_report_histories": "Can you check the report by clicking on 'Check' or go to the report creation history page."}, "placeholder": {"start_round_date": "Start Date", "to_round_date": "End Date"}, "select": {"business_type_all": "All", "status_all": "All"}, "button": {"cancel": "Cancel", "submit": "Submit", "review": "Check"}}, "type": {"approval": "Approval"}}, "survey_report": {"modal": {"title": "Survey Report (On System)"}, "label": {"date_range": "Submission Date Range", "survey_name": "Survey Name", "course_name": "Course Name", "enrollment_status": "Status"}, "message": {"required_date_range": "Please choose date range", "required_survey": "Please choose survey"}, "placeholder": {"start_date": "Start Date", "end_date": "End Date", "survey_name": "Survey Name"}}, "pre_enroll": {"modal": {"message": {"required_round_date": "Please choose round date", "required_business_type": "Please choose business type", "required_status": "Please choose status", "system_processing": "The System is processing.", "review_in_report_histories": "Can you check the report by clicking on 'Check' or go to the report creation history page."}, "button": {"cancel": "Cancel", "submit": "Submit", "review": "Check"}}}, "user_group_detail": {"title": "User Group", "button": {"delete": "Delete", "add_condition": "add condition", "remove_sub_condition": "remove condition", "add_operation": "add course condition"}, "message": {"fetch_fail": "Something went wrong", "publish_success": "User group has been published successfully", "publish_fail": "Something went wrong, can not publish user groups", "enable_success": "User group has been enabled successfully", "enable_fail": "Something went wrong, can not enable user groups", "disable_success": "User group has been disabled successfully", "disable_fail": "Something went wrong, can not disable user groups", "update_success": "User group has been updated successfully", "update_fail": "Something went wrong, can not update user groups", "delete_success": "User group has been deleted successfully", "delete_fail": "Something went wrong, can not delete user group"}, "drawer": {"edit": {"title": "Edit: General Information"}, "edit_condition": {"title": "Edit: Set user group conditions", "subtitle1": "Set user group conditions", "subtitle2": "User group conditions"}}, "modal": {"warn_delete": {"title": "Do you want to delete this user group", "content_line_1": "User groups “{0}”", "content_line_2": "this is last user group in these courses", "content_line_3": "Once deleted, it can't be revert", "content_line_4": "Specific user groups content", "content_line_5": "will be convert to private content", "content_line_learning_path": "this is last user group in these learning path"}, "confirm_delete": {"title": "Do you want to delete this user group", "content_line_1": "User groups “{0}”", "content_line_2": "Once deleted, it can't be revert"}, "unable_publish": {"title": "Could not enable this user group", "incomplete_warning": "Because of incomplete information such as", "no_user": "User group must have at least 1 user", "recommend_line_1": "Please recheck and operate ", "recommend_line_2": "before enable user group again", "confirm": "Confirm"}}, "tabs": {"general": "General", "users": "Users"}, "sections": {"general": {"title": "General", "name": "name", "description": "description"}, "users": {"title": "Member"}, "condition": {"title": "Set user group conditions", "user_group_condition": "User group conditions", "have_condition": "Filter by user group conditions", "no_condition": "Unconditional", "data_condition": "Condition information", "match_condition": "All user groups must match", "all_condition": "All conditions", "any_condition": "One must match"}}, "footer": {"publish": "Publish", "save": "Save", "status": "Status", "disabled": "Disabled", "enabled": "Enabled"}, "form": {"switch": {"label": {"user_group_condition": "Filter by user group conditions"}}, "radio": {"label": {"user_group_match_condition": "All user groups must match", "all_condition": "All conditions", "any_condition": "Some conditions"}}, "select": {"label": {"select_data": "Select data", "operator": "Select conditions"}, "placeholder": {"select_data": "Select data", "select_operator": "Select conditions", "select_value": "Condition value"}, "rule": {"required_data": "Please select information", "required_operator": "Please select conditions", "required_value": "Please enter the value of the condition.", "required_enrollment_passed_operator": "Please select at least 1 condition for passing the course."}}, "input": {"rule": {"required_number": "number is required", "more_than_zero": "number must be greater than zero", "integer": "number must be integer", "required_value_greater_than": "value must be greater than the condition", "required_value_less_than": "value must be less than the condition"}}, "rule": {"at_least_sub_condition": "Please create at least 1 condition."}}}, "learning_path": {"column_setting": {"name": "Name", "code": "Code", "thumbnail": "<PERSON><PERSON><PERSON><PERSON>", "number_of_content": "Contents", "is_certificate_enabled": "Certificates", "enroll_type": "Plan", "expiry_day": "Study period", "mode": "Mode", "created_at": "Created At", "updated_at": "Updated At", "status": "Status"}, "list": {"title": "Learning Paths", "button": {"create": "Create Learning Path"}, "place_holder": {"name": "Learning path name", "code": "Enter value", "no_expiration": "No expiration"}}}, "learning_path_detail": {"select_round": {"current_step": "Step {{current_step}} of {{total_steps}}", "back": "Back", "next": "Next", "time": "Time", "confirm_information": "Confirm Information", "steps": {"choose_round": {"title": "Choose a session", "please_choose_session": "Please choose a session", "description": "Please select the start date according to the session you prefer.", "start_date": "Start date", "end_date": "End date", "cancel": "Cancel", "start": "Start", "end": "End", "select": "Select"}, "confirm_select": {"title": "Confirm enrollment information", "description": "Please verify the details before confirming the information. If the information is incorrect, please contact support.", "personal_information": {"title": "Personal Information", "full_name": "Full name (Thai)", "national_id": "National ID number (13 digits)", "email": "Email", "phone_number": "Phone number"}, "learning_path_information": {"title": "Learning Part Information", "content_name": "Content name", "start_date": "Start date", "end_date": "End date"}}, "warning_check_enroll": {"title": "Your license information has expired. from the date you selected the course", "content": "Please select a round before your license expires.", "button_ok": "Choose a new round"}}}, "notification": {"success": {"success_self_enrollment": "Enrollment Learning Path Success", "success_self_pre_enrollment": "Pre Enrollment Learning Path Success", "cancel_self_enrollment": "Cancel learning path enrollment success"}, "error": {"error_self_enrollment": "Error register learning path enrollment", "error_cancel_self_enrollment": "Cancel learning path enrollment error"}}, "description": {"title": "Learning Part Information", "expand": "Expand", "collapse": "Collapse"}}, "learning_path_enrollment": {"detail": {"label": "Detail", "title": "Learning path detail", "of": "Of", "version": "version {0}", "enroll": "Enrollment information", "enroll_by": "Enroll by", "learning_format": "Learning format", "learning_sequence": "Compulsory to study according to the course sequence", "learning_no_sequence": "No compulsory to study according to the course sequence", "enroll_date": "Enrollment date information", "start_date": "Start date", "finish_date": "Finish date", "expiry_date": "Expiry date", "learning_progress": "Learning progress", "progress_percent": "Learning (%)", "total_duration": "Total duration", "user": "User information", "message": {"fetch_fail": "Error, Not found learning path detail", "normal": {"success": "Change expiry date success", "error": "Change expiry date error", "date": "Current expiry date ", "label": "Select expiry date", "validate": "Pleases select expiry date", "title": "Change expiry date"}, "expired": {"success": "Expand date learning path success", "error": "Expand date learning path error", "date": "Old expiry date", "label": "Select new expiry date", "validate": "Please select new expiry date", "title": "Expand learning path date"}, "cancel_enroll": {"success": "Cancel enrollment success", "error": "Cancel enrollment error"}}, "footer": {"status": "Status:", "cancel_enroll": "Cancel enrollment", "verify_cert": "Verify certificate", "cancel_text": "Cancel", "confirm_email": {"placeholder": "Please enter email", "input_email": "Please enter email {0}", "done": "To confirm action", "validate": {"email": "The email address for verification was entered incorrectly. Please type your email address as above."}}}}, "learning_progress": {"label": "Learning progress", "access_latest": "Latest access:", "completed": "Completed", "duration": "Duration", "certificate": "Receive a certificate of learning path", "learning_path_success": "Successfully completed the learning path", "summary": {"learning": "Learning (%)", "total_course": "Number of courses", "remain": "Remain duration"}}, "title": "Learning Path Enrollments", "full_name": "Full name", "email": "Email", "learning_path_name": "Learning path name", "started_date": "Start date", "expired_date": "End date", "expiry_day": "Duration", "content_progress": "Learning progress (%)", "status": "Status", "learning_status": {"PRE_ASSIGN": "Waiting to start", "ASSIGNED": "Ready to learn", "IN_PROGRESS": "In progress", "COMPLETED": "Completed", "EXPIRED": "Expired", "CANCELED": "Canceled"}, "enroll_by": "Enroll By", "enroll_status": {"pre_assign": "Pre-Assign", "canceled": "Canceled", "assigned": "Assigned", "in_progress": "In-Progress", "completed": "Completed", "expired": "Expired"}, "enroll": {"supervisor": "Manager", "admin": "Admin", "self_enroll": "Self-Enroll"}, "select": {"default": "All"}, "button": {"bulk_enrollment": "Bulk Enrollments", "expand_learning_path": "Learning Path Expand", "detail": "View Detail"}}, "assign_content": {"content_type": {"course": "Course", "learning_path": "Learning Path"}, "preview_learning_path_detail": "{0} Courses", "current_step": "Step {0}", "btn": {"back_step": "Back", "save_draft": "Save Draft", "next_step": "Next", "submit_confirm": "Submit Confirm"}, "error_message": {"save_draft": "Error Save draft", "submit_confirm": "Error Assign course to my team"}, "step_select_course": {"title": "Select a Course or Learning Path", "description": "Please select course or learning path to assign to your team", "search_course": "Search course name", "search_learning_path": "Search learning path name", "tab": {"course": "Course", "learning_path": "Learning Path"}, "keyword_notfound_description": "please re-check the keyword or reset the search filter"}, "step_content_information": {"title": {"course": "Course Information", "learning_path": "Learning Path Information"}}, "step_select_round": {"title": "Select Round", "description": "Please Select Round Date"}, "step_select_enroll_type": {"title": "Select Enroll Type", "description": "Please select enroll type", "enroll_type": {"info": {"course": "Enroll type of course"}, "voluntary": "Voluntary", "compulsory": "Compulsory"}}, "step_user": {"title": "Select User", "description": "Please select the list of members you want to assign course. limit up to 2,000 members", "user_subordinate_list": "List of all members", "user_assign_content_list": "List of members to assign course"}, "step_confirm_information": {"title": "Confirm information in assign course", "description": "Please wait to check the information in confirmation of assign course", "course_information": {"title": "Course information", "course_name": "Course Name:", "course_objective": "Objective:", "course_instructor": "instructor:"}, "enroll_type_information": {"title": "Enrollment type information", "enroll_type": "Enrollment type:"}, "round_information": {"title": "Round information", "round_start_at": "Start date:", "round_end_at": "End date:"}, "user_information": {"title": "User information to assign course", "user_total": "Total user:"}}, "step_request_success": {"title": "The request to confirm information in the course registration has been successfully submitted", "description1": "Please wait to check the information in confirmation of assign course", "description2": "You can view your course registration results on the assign course page", "back_to_page": "Back to assign course page"}}, "pre_enrollment_transactions": {"status": "Enrollment Status", "errorMsg": "Reason"}, "job_transaction": {"status": "Job Status", "errorMessages": "Reason"}, "page": {"title": {"achievements": "Achievements", "announcements": "News and Announcements", "badge": "Badge", "courseManagement": "Courses", "roundManagement": "Rounds", "discussionBoard": "Discussion Board", "discussionBoardDetail": "Discussion Board Detail", "additional_doc": "Additional Documents", "deduct_doc": "Deduct Documents", "course_category": "All Categories", "instructors": "All Instructors", "departments": "Departments", "knowledge_content_item": "Knowledge Content", "knowledge_content_category": "Knowledge Content Categories", "my_team": "My Team", "user_group": "User Groups", "customer": "Customer", "plan": "Plan"}, "subtitle": {"totalRecords": "Total: {0} records"}}, "components": {"content_admin": "Please contact the administrator.", "login": {"error": {"invalid_login": "Due to frequent invalid logins, please wait 5 minutes and try again.", "invalid_email_set_password": "The password reset link is invalid. Please check or submit a new password reset request again.", "email_expired": "Password reset email has expired. Please submit a new password reset request again.", "two_factor_token_expired": "Session has expired, Please login again"}, "login_button": "<PERSON><PERSON>", "username": "Username", "required_username": "Please enter your username.", "input_username": "Fill username", "invalid_username_or_password": "Username or password invalid Please, try again", "password": "Password", "forgot_password": "Forgot password", "required_password": "Please enter your password", "input_password": "Fill password", "login_with": "Login with", "or": "or", "new_password_required": "You need to set a new password.", "new_password_required_description": "Since your current password has expired", "new_password_required_description2": "Please set a new password to continue using it."}, "drawer": {"buttons": {"create": "Create", "save": "Save", "discard": "Discard", "confirm": "Confirm", "cancel": "Cancel"}, "article": {"input_name": "Please, Enter article name", "input_description": "Please, <PERSON><PERSON> descrit<PERSON>", "input_duration": "Please, Enter duration", "create_article": "Create Article", "edit": "Edit", "edit_article": "Edit: Article", "basic_detail": "Basic detail", "article_detail": "Article detail", "content_detail": "Content detail", "attachments": "Attachment", "attachments_detail": "Attach a specific file format .doc, .docx, .xls, .xlsx, .ppt, .pptx, .zip, .rar, .pdf, .jpeg, .jpg, .png only and can sort the display.", "attachments_error": "Some attachments cannot be uploaded. because the file format is incorrect or the file size exceeds limit allowed.", "select_or_drop_file": "Select or drag files to upload.", "format_file_and_size": "File format {{acceptableFileExtensions}} maximum file size 30 MB"}, "survey": {"input_name": "Please, Enter survey name", "input_description": "Please, <PERSON><PERSON> descrit<PERSON>"}}, "button": {"download": "Download", "createContent": "Create Content", "settingColumn": "Setting Column", "create_department": "Create Department"}, "table": {"title": "Content", "subtitle": "Content list", "button": {"edit": "Edit", "detail": "Detail", "remove": "Remove"}, "pagination": {"total_records": "Total {0} Records"}}, "input": {"placeholder": {"fillData": "Fill out data", "search": "Search"}}, "modal": {"title": {"columnSetting": "Column <PERSON>"}}, "dropdown": {"ordering": {"ascending": "Oldest - Newest", "descending": "Newest - Oldest"}}}, "general": {"toast": {"save": "Updated!", "error": "Something went wrong", "not_found": "Not found", "delete_success": "Delete success"}, "save": "Save", "discard": "Discard", "cancel": "Cancel", "select_input": {"error": "Please select {0}"}, "checkbox": {"enable": "Active", "disable": "Inactive"}, "confirm": "Confirm", "filter": {"all": "All"}, "reset": "Reset", "submit": "Submit", "show_more": "Show more", "show_all": "Show All", "show_less": "Show less", "modal": {"discard_title": "Do you want to discard?", "discard_description": "Once discard, it cannot be brought back.", "confirm_email": {"placeholder": "Please enter email", "input_email": "Please enter email {0}", "done": "To confirm action", "validate": {"required": "email is required", "match": "input invalid email"}}}}, "action": "Action", "search": "Search", "cancel": "Cancel", "apply": "Apply", "customize_columns": "Customize columns", "sort_columns": "Sort column", "new_course": {"title": "Create new course", "introduction": "Basic course information", "form": {"contentType": {"label": "Content Type", "placeholder": "Select content type"}, "objectiveType": {"label": "Objective", "placeholder": "Select objective"}, "regulator": {"label": "Regulator", "placeholder": "Select regulator"}, "name": {"label": "Name", "placeholder": "Enter name"}, "code": {"label": "Code", "placeholder": "Enter code"}, "randomCode": "Generate"}, "contentType": {"elearning": "e-Learning"}, "validations": {"content": {"required": "Please select content type."}, "objectiveType": {"required": "Please select objective."}, "regulator": {"required": "Please select regulator."}, "code": {"required": "Please enter code.", "existed": "This code already existed.", "invalid": "Only upper characters (A-Z), number (0-9), and underscore (_) are allowed."}, "productSKUCode": {"required": "Please enter productSKUCode.", "existed": "This productSKUCode already existed.", "invalid": "Only upper characters (A-Z), number (0-9), and underscore (_) are allowed."}, "name": {"required": "Please enter name.", "invalid": "Please enter valid character.", "start_with_space": "Name can't start with space", "end_with_space": "Name can't end with space", "consecutive_space": "Name can't have consecutive space", "name_limit_length": "Name should be no longer than {0} characters"}}, "drawer": {"buttons": {"create": "Create", "discard": "Discard"}}, "modal": {"cancelCreate": {"title": "Are you sure to discard all changes?", "message": "Changed cannot be restored after you discard."}, "buttons": {"discard": "Discard", "cancel": "Cancel"}}, "message": {"create_success": "Create course success", "create_error": "Something went wrong, Could not create", "edit_success": "Edit course success", "edit_error": "Something went wrong, Could not update"}}, "page_title.approval": "Pending Approval", "d.value.all": "All", "c.button.reset": "Reset", "c.button.discard": "Discard", "c.button.confirm": "Confirm", "c.button.cancel": "Cancel", "c.modal.enrollment_detail.authen_info.title": "Delete Image Confirmation", "c.modal.enrollment_detail.authen_info.confirm_remove": "Please enter the email {0} to confirm the operation.", "c.modal.enrollment_detail.authen_info.enter_email": "Please enter email", "c.modal.enrollment_detail.authen_info.enter_email_invalid": "Please enter \"{0}\"", "c.modal.enrollment_detail.authen_info.required_email": "Please enter email", "c.modal.enrollment_detail.authen_info.invalid_email": "Invalid confirmation email, Please enter email as above.", "c": {"icons": {"enabled": "Active", "disabled": "Inactive"}, "achievement": {"title": "All achievement", "create": "Create achievement"}, "announcement": {"title": "All announcements and news", "highlight": "Pin highlight", "create": "Create news and announcements", "banner": {"title": "News and Announcements", "description": "Found the latest news and announcements."}, "empty_data": {"title": "Not have data", "description": "There is currently no information available, Please check later or try again."}}, "activity_log": {"title": "<PERSON><PERSON>"}, "badge": {"title": "Badge", "create": "Create Badge"}, "breadcrumb": {"home": "Home", "news_and_announcements": "News and Announcements", "knowledge_content": "Knowledge Contents"}, "course": {"drawer": {"save": "Save", "discard": "Discard", "self_enroll": {"enrollment_setting": "Enrollment setting", "edit_setting": "Edit: Enrollment setting", "setting": "Trainee can self enrollment", "is_enabled": "Enabled", "is_disabled": "Disabled"}, "expiry_day": {"setting": "Expiry date setting", "enabled": "Enabled expiry date", "edit_setting": "Edit: Expiry Date Setting", "expiryDay": "Expiry date", "input_expiryDay": "Enter the expiry date", "error": {"required_expiry_day": "Please enter expiry date", "required_only_number": "Please enter only the number", "have_round_required_expiry_day": "Due to the activation of the study cycle Please enter the number of study days. where the value must always be greater than 0", "expiry_day_more_zero": "Please enter expiry date must be greater than 0"}}, "re_enroll": {"title": "Re enroll passed setting", "edit_setting": "Edit: Re-En<PERSON>", "enabled": "Enabled, Re enroll", "input_form": "open re enroll once passed (day)", "placeholder": "fill amount of re enroll day", "validator": {"empty": "Please, Fill amount of re enroll", "minimum": "Re enroll must be longer 0 day"}}, "re_enroll_expire": {"title": "Re enroll expired setting", "edit_setting": "Edit: Re-En<PERSON>", "enabled": "Enabled, Re enroll", "input_form": "open re enroll once expired (day)", "placeholder": "fill amount of re enroll day", "validator": {"empty": "Please, Fill amount of re enroll", "minimum": "Re enroll must be longer 0 day"}}, "edit_instructor": {"title": "Edit: <PERSON><PERSON><PERSON><PERSON>", "dropdown": {"title": "Add instructor", "add_button": "Add instructor", "create_button": "Create new instructor...", "label": "Add/Create instructor", "placeholder": "Search by name/email"}, "list": {"subtitle": "Sort the instructors"}}, "edit_course_enroll_type_title": "Edit: Enrollment Type"}, "tab": {"enrollment": {"bulk_enrollments": "Bulk Enrollments", "tab": "Enrollment", "self_enroll": {"enrollment_setting": "Enrollment setting", "setting": "Tainee can self enrollment", "is_enabled": "Enabled", "is_disabled": "Disabled"}, "expiry_day": {"setting": "Expiry date setting", "expiryDay": "Expiry date", "expiry_day_enabled": "Enabled to expiry date"}, "re_enroll": {"title": "Re enroll passed setting", "is_repeat_enroll": "Re enroll enabled", "re_enroll_day": "open re enroll once passed", "day": "day"}, "re_enroll_expire": {"title": "Re enroll expired setting", "is_repeat_enroll": "Re enroll enabled", "re_enroll_day": "open re enroll once expired", "day": "day"}}, "general": {"instructor": {"title": "Instructor ({0})"}, "certificate": {"setting_criteria": {"quiz": "Quiz", "classroom": "Classroom"}, "validator": {"quiz": {"not_enough": "Please enter an integer greater than 0.", "conditions_pass_score_incorrect": "Pass score condition is invalid.", "please_enter_conditions": "Please enter conditions."}, "classroom": {"not_enough": "Please enter an integer greater than 0.", "conditions_attendance_incorrect": "Attendance score condition is invalid.", "conditions_homework_incorrect": "Homework score condition is invalid.", "please_enter_attendance_score": "Please enter your attendance score.", "please_enter_homework_score": "Please enter your homework score.", "please_enter_least_one_conditions": "Please enter at least one condition."}}, "tooltip": {"classroom": {"attendance_score": "Set the condition for the number of class attendances. If the number of attendances is not specified, there will be no attendance requirements for the class.", "homework_score": "Set the condition for homework scores in the class. If the homework score is not specified, there will be no homework requirements for the class."}}}}}, "menu": {"detail": "Detail", "instructor": "Instructor ({0})", "access": "Access", "enrollment": "Enrollment", "learn": "Learn", "certificate": "Certificate", "oic_report": "OIC Report", "tsi_report": "Setting TSI Course"}, "error": {"is_not_have_course": "Not have course", "is_not_have_expiryDay": "Course not have expiryDay", "is_not_have_round": "Course not have round", "is_not_have_course_item": "Course not have course item", "is_not_have_objectiveType": "Course not have objectiveType", "is_not_have_regulatorInfo": "Course not have regulatorInfo", "incomplete_regulatorInfo_oic": "Content Property setting", "incomplete_regulatorInfo_tsi": "Content Property setting", "incomplete_regulatorInfo_none": "Content Property setting", "incomplete_report_setting_tsi": "Setting TSI Course"}, "detail": {"not_found_course": "Course not found", "back_to_my_learning": "Back to my course", "criteria": {"learning": "Criteria", "enable_learning_progress": "Enable learning progress", "percentage_learning_progress": "Percentage learning progress", "enable_time_spent": "Enable time spent", "percentage_time_spent": "Percentage time spent", "course_item_progress": "({{item}}/{{totalItem}})", "learning_progress_more_equal": "Learning progress more equal (≥)", "learning_progress_equal": "Learning progress equal", "time_spent_course_more_equal": "Time spent course more equal", "time_spent_course_equal": "Time spent course equal", "time_spent_course_item_more_equal": "Time spent course item more equal", "time_spent_course_item_equal": "Time spent course item equal", "enable_quiz": "Enable quiz", "enable_classroom": "Enable classroom", "quiz_items_pass_criteria": "Pass {0} quiz (criteria)", "classroom_items_pass_criteria": "Pass {0} classroom (criteria)", "time_spent_course_item": "(Only video, article)"}}, "label": {"course_enroll_type": "Enrollment Type", "course_enroll_type_immediate": "Immediate", "course_enroll_type_pre_enroll": "Scheduler"}, "card": {"enroll_type_title": "Enrollment Type Settings"}, "tooltip": {"course_enroll_type_pre_enroll": "You need to add a round. Before activating the course"}, "toast": {"update_success": "Update successfully ", "update_error": "Something went wrong, Can not updated"}, "modal": {"discard_enroll_type_title": "Do you want to discard?", "discard_enroll_type_description": "Once discarded, it cannot be retrieved"}}, "card": {"title": {"user": {"license": "License Information", "additional": "Additional Information"}, "course": {"overview": "Overview", "course_property": "Course Property"}, "waiting_to_enroll_detail": {"information": "Waiting to enrollment", "edit_information": "Edit: waiting to enrollment"}, "waiting_to_enroll_history": {"history": "history"}}, "certificate": {"information": "Certificate Information", "setting_certificate": "Set up a certificate", "setting_approvement": "Set up completion verification", "edit_conditional_approval_type": "Set up completion verification", "setting_criteria_certificate": "Conditions for obtaining a certificate", "criteria_certificate": "Conditions for obtaining a certificate"}, "user": {"general": "General", "contact_info": "Contact Info", "oic_license": "OIC License", "tsi_license": "TSI License", "organization_title": "Organization", "supervisor_title": "Supervisor"}, "sub_title": {"certificate": {"certificate_no": "certificate {{no}}", "name": "certificate"}}, "department": {"members": "Members", "moderators": "Moderators", "general_title": "General", "department_title": "Department hierarchy", "config_title": "Config", "management_title": "Management"}, "permission_group": {"general_title": "General", "create_title": "Create Permission Group", "update_title": "Update: Permission Group", "update_member_title": "Update: Members", "update_member_success": "Saved Success", "update_member_failed": "Something was wrong. can't to save", "grant_permission_title": "Give All Permissions"}, "knowledge_content_category": {"overview": "Overview", "general": "General", "thumbnail": "<PERSON><PERSON><PERSON><PERSON>"}, "knowledge_content_item": {"title": "Knowledge Content Item"}, "enrollment_attachment_additional_type": {"general_title": "General"}}, "radio": {"label": {"certificate": {"manual_approve": "ตรวจสอบหลังเรียนจบโดยผู้ดูแลระบบ", "system_approve": "ตรวจสอบหลังเรียนจบโดยระบบ"}, "deduct": {"no_deduct": "For those not eligible for deductions.", "deduct_without_document": "For those eligible for deductions without requiring the attachment of documents", "deduct_with_document": "For those eligible for deductions that require attaching documents within the system"}}}, "label": {"empty_data": "No data", "active": "Active", "inactive": "Inactive", "course": {"round_date": "Round Date", "training_center": "Training Center", "license_renewal": "License Reward", "applicant_type": "Applicant Type", "license_type": "License Type", "objective_type": "Objective Type", "business_type": "Business Type", "status": "Status", "part": "Lesson", "items": "Items", "part_name": "Lesson", "part_description": "Description", "deduct": "Attach deduction documents"}, "course_category": {"not_found": "Not found", "back_to_catalog": "Back to catalog"}, "user": {"search_result": "Search Result: {0} items", "active": "Active", "inactive": "Inactive", "salute_mr": "Mr.", "salute_mrs": "Mrs.", "salute_ms": "Miss", "salute_etc": "ETC (specify)", "salute_placeholder": "Choose prefix", "firstname": "First Name", "middlename": "Middle Name", "lastname": "Last Name", "citizen_id": "Citizen ID", "name": "Name", "email": "Email", "phone_number": "Phone Number", "username": "Username", "supervisor": "Supervisor"}, "certificate": {"image": "ตัวอย่างประกาศนียบัตร", "slug": "รูปแบบประกาศนียบัตร", "refCode": "รหัส Ref (Ref Code)", "refName": "ชื่อ Ref (Ref Name)", "tsiCode": "รหัสวิชา (เฉพาะ TSI)", "pillarName": "องค์ความรู้ (เฉพาะ TSI)", "type": "ประเภทการประกัน", "enabled_certificate": "เปิดใช้งานประกาศนียบัตร", "multiple_certificates": "เปิดใช้งานประกาศนียบัตรหลายใบ", "obtaining_certificates": "การได้รับประกาศนียบัตร", "auto_obtain_certification": "ได้รับประกาศนียบัตรทันที เมื่อได้รับการอนุมัติ", "graduate_obtain_certification": "ได้รับประกาศนียบัตร หลังจากวันสิ้นสุดรอบการเรียน", "suddenly_obtain_certification": "ได้รับประกาศนียบัตรทันที เมื่อเรียนจบ", "warning_enable_multiple_certificates": "Only 4th renewal, Please enable multiple certificate"}, "attachment": {"has_file_attachment": "Attachments {0} files", "has_file_download": "Available {0} files"}, "department": {"name": "Name", "code": "Code", "description": "Description", "parent": "Main department", "member": "Member", "moderator": "Moderator", "hierarchy": "Hierarchy", "general": "General", "supervisor": "Supervisor", "config": "Department management", "config_view_user_information": "View user information", "config_enroll_under_direct_report": "Enroll under direct report", "config_view_and_download_report": "View and download report"}, "permission_group": {"name": "Permission Group Name", "description": "Description"}, "my_team": {"members": "Members", "moderators": "Moderators", "subordinates": "Subordinates", "empty_data": "(No team member)"}, "knowledge_content_item": {"code": "Code", "type": "Type", "status": "Status", "type_all": "All"}, "knowledge_content_category": {"code": "Code", "createdAt": "Created At", "updatedAt": "Updated At", "status": "Status", "title": "Title", "thumbnail": "<PERSON><PERSON><PERSON><PERSON>", "image_size": "Image size: {0} px"}}, "input": {"placeholder": {"course": {"enter_code": "Enter Content Code", "enter_productSKUCode": "Enter Content ProductSKUCode", "enter_name": "Enter Content Name", "round_start_at": "Start date", "round_end_at": "End date", "business_type": "Enter Business Type", "status": "Enter status"}, "certificate": {"enter_ref_code": "กรอกรหัส Ref (Ref Code)", "enter_ref_name": "กรอกชื่อ Ref (Ref Name)", "enter_tsiCode": "กรอกรหัสวิชา (เฉพาะ TSI)", "enter_pillarName": "กรอกองค์ความรู้ (เฉพาะ TSI)"}, "enrollment_filter": {"course_name": "Course name"}}, "error": {"text": "Please enter text ", "select": "Please select ", "course": {"name_content_length": "Course name should be no longer than {0} characters", "select_training_center": "Please choose training center", "select_license_renewal": "Please choose license reward", "select_applicant_type": "Please choose applicant type", "select_license_type": "Please choose license type", "select_objective_type": "Please choose objective type", "select_round_date": "Please choose round date", "select_business_type": "Please choose business type", "select_status": "Please choose status"}, "certificate": {"enter_ref_code": "กรุณากรอกรหัส Ref (Ref Code)", "enter_ref_name": "กรุณากรอกชื่อ Ref (Ref Name)"}}}, "selector": {"select_type": "<PERSON><PERSON>", "select_tsi_type": "Select TSI license type", "select_all_business_type": "All Business type", "placeholder": {"course": {"select_training": "Choose Training Center", "select_license_renewal": "Choose License Reward", "select_applicant_type": "Choose Applicant Type", "select_license_type": "Choose License Type", "select_objective_type": "Choose Objective Type", "select_business_type": "Choose Business Type", "select_status": "Choose <PERSON>"}, "certificate": {"choose_slug_certification": "เลือกรูปแบบประกาศนียบัตร", "choose_type": "เลือกประเภทการประกัน"}}, "error": {"certificate": {"choose_slug_certification": "กรุณาเลือกรูปแบบประกาศนียบัตร", "choose_type": "กรุณาเลือกประเภทการประกัน"}}}, "button": {"edit": "Edit", "additional_filters": "Additional Filters", "column_setting": "Column <PERSON>", "view_detail": "View detail", "download": "Download", "edit_document": "Edit document", "fill_in_document": "Edit document", "course": {"save_status": "Save Status", "generate_code": "Generate", "publish": "Publish"}, "download_example_file": "Download Example", "user": {"download_user": "Download", "manage_bulk_user": "Manage Bulk User", "bulk_create_user": "Bulk Create User", "bulk_update_user": "Bulk Update User", "bulk_assign_plan_package_license": "Bulk Assign License", "bulk_transfer_plan_package_license": "bulk Assign License", "create_user": "Create User", "add_supervisor": "Add Supervisor"}, "waiting_to_enroll": {"bulk_enrollments": "Bulk Enrollments"}, "enrollment": {"bulk_enrollments": "Bulk Enrollments"}, "enrollment_history": {"create": "Bulk Enrollment Histories"}, "certificate": {"save": "บันทึก", "save_and_test": "บันทึกและทดสอบส่ง", "remove": "นำออก", "add": "เพิ่มประกาศนียบัตร"}, "department": {"create_department": "Create Department"}, "user_group": {"create": "Create User Group"}, "knowledge_content_item": {"create": "Create Knowledge Content", "pin": "<PERSON>n"}, "knowledge_content_category": {"sorting": "Sorting", "create": "Create", "delete": "Delete", "add_item": "Add", "remove_item": "Remove", "save_status": "Save Status"}}, "footer": {"text": {"course": {"status": "Content Status"}}, "privacy": "Privacy policy", "terms": "Term & Condition of use"}, "modal": {"title": {"course": {"discard": "Would you like to discard all data", "publish_error": "Can't publish this content", "objective_type": "Content Objective", "remove": "Want to remove the content?"}, "instructor": {"remove": "Would you like to remove an instructor?"}, "certificate": {"delete_certificate": "ต้องการลบประกาศนียบัตรหรือไม่"}, "announcement": {"discard": "Would you like to discard all data", "update_highlight": "ยืนยันการแก้ไขการปักหมุดหรือไม่"}, "knowledge_content_item": {"discard": "Would you like to discard all data"}, "course_detail": {"title": "Confirm registration cancellation"}, "learning_path": {"title": "Confirm registration cancellation"}}, "subtitle": {"course": {"description_objective_type": "Choose a content creation purpose. For content imported from outside the system"}}, "body": {"course_detail": "Do you want to confirm cancellation of your registration for the {0} course", "announcement": {"discard": "Once an abandonment is performed, it cannot be brought back.", "update_highlight": "คุณต้องการยืนยันการแก้ไขการปักหมุดข่าวสารและประกาศ ใช่หรือไม่"}, "course": {"description_1": "Due to insufficient information, including:", "description_2": "Please check and complete the above information. before reactivating later.", "description_item_1": "Content Property setting", "description_item_2": "Report output settings", "discard": "Once an abandonment is performed, it cannot be brought back.", "remove": "Once removed, content can be added later through content item editing"}, "instructor": {"remove": "Once removed, more instructors can be added later."}, "certificate": {"delete_certificate_content_1": "certificate: {{certificate}}", "delete_certificate_content_2": "Once deleted, it cannot be brought back."}, "knowledge_content_item": {"discard": "Once an abandonment is performed, it cannot be brought back."}, "learning_path": {"cancel": "Do you want to confirm cancellation of your registration for the {0} learning path"}}, "footer": {"button": {"course": {"ok": "Ok", "confirm": "Confirm", "discard": "Discard", "cancel": "Cancel", "remove": "Remove"}, "certificate": {"ok": "ตกลง", "delete": "ลบ", "cancel": "ยกเลิก"}}, "column_setting": {"reset": "Reset", "cancel": "Cancel", "apply": "Apply"}}, "course": {"error_delete_part_title": "Can't delete lesson", "error_delete_part_content": "Please your delete content before", "warning_delete_part_title": "Do you want to delete a lesson?", "warning_delete_part_content": "Once deleted, it cannot be brought back.", "error_delete_course_item_title": "Can't to delete “{0}”", "error_delete_course_item_content": "<span>{0} is being activated<br />Please disable it before delete to {0}</span>", "warning_delete_course_item_title": "Do you want to delete “{0}” or not?", "warning_delete_course_item_content": "Once deleted, it cannot be brought back.", "warning_discard_title": "Do you want to discard?", "warning_discard_content": "Once discard, it cannot be brought back."}, "answer": {"warning_delete_title": "Do you want to delete answer “{0}” or not?", "warning_delete_content": "Once deleted, it cannot be brought back."}, "question": {"warning_delete_title": "Do you want to delete question “{0}” or not?", "warning_delete_content": "Once deleted, it cannot be brought back."}, "quiz": {"warning_delete_title": "Do you want to delete “{0}” or not?", "warning_delete_content": "Once deleted, it cannot be brought back.", "error_delete_title": "Can't to delete “{0}”", "error_delete_content": "Quiz is being activated<br />Please disable it before delete to Quiz.", "warning_discard_title": "Do you want to discard?", "warning_discard_content": "Once an abandonment is performed, it cannot be brought back."}, "survey": {"warning_delete_title": "Do you want to delete “{0}” or not?", "warning_delete_content": "Once deleted, it cannot be brought back.", "error_delete_title": "Can't to delete “{0}”", "error_delete_content": "Survey is being activated<br />Please disable it before delete to Survey.", "warning_discard_title": "Do you want to discard?", "warning_discard_content": "Once an abandonment is performed, it cannot be brought back."}, "user": {"discard_create_user_title": "Want to discard all data?", "discard_create_user_content": "Once an abandonment is performed, it cannot be brought back.", "warning_discard_organization_title": "Do you want to discard?", "warning_discard_organization_content": "Once an abandonment is performed, it cannot be brought back."}, "pre_enroll": {"pre_enroll_transaction_status_detail": "Pre-Enroll Transaction Status Details", "pre_enroll_transaction_status_edit": "Pre-Enroll Transaction Status Edit"}, "video": {"warning_delete_title": "Do you want to delete “{0}” or not?", "warning_delete_content": "Once deleted, it cannot be brought back.", "error_delete_title": "Can't to delete “{0}”", "error_delete_content": "Video is being activated<br />Please disable it before delete to Video.", "warning_discard_title": "Do you want to discard?", "warning_discard_content": "Once an abandonment is performed, it cannot be brought back."}, "department": {"warning_delete_title": "Do you want to delete department or not?", "warning_delete_content": "Department “{0}” <br />it cannot be brought back", "warning_discard_title": "Do you want to discard?", "warning_discard_content": "Once an abandonment is performed, it cannot be brought back."}, "permission_group": {"warning_delete_title": "Do you want to delete this permission group?", "warning_delete_content": "Permission Group “{0}” Once deleted, it cannot be brought back.", "warning_discard_title": "Do you want to discard?", "warning_discard_content": "Once an abandonment is performed, it cannot be brought back.", "warning_update_title": "Update permission group confirmation", "warning_update_content": "Please enter the email {0} to confirm the operation.", "warning_confirm_email_empty": "Update permission group confirmation", "warning_confirm_email_invalid": "Please enter the email {0} to confirm the operation."}, "discard": {"title": "Do you want to discard?", "body": "Once an abandonment is performed, it cannot be brought back."}, "knowledge_content_category": {"warning_delete_title": "Do you want to delete knowledge content category or not?", "warning_delete_content": "Knowledge content category “{0}” <br />it cannot be brought back", "warning_discard_title": "Do you want to discard?", "warning_discard_content": "Once an abandonment is performed, it cannot be brought back.", "warning_remove_item_title": "Want to remove this item?", "warning_remove_item_content": "Once removed, can be added later <br />Through editing the knowledge content list."}, "enrollment_attachment_additional_type": {"warning_discard_title": "Do you want to discard?", "warning_discard_content": "Once an abandonment is performed, it cannot be brought back."}, "enrollment_detail": {"reset_history": {"before": "Before reset", "after": "After reset", "progress": "Progress: {0} items"}}}, "tooltip": {"title": {"course": {"incomplete_information": "Incomplete Information"}, "certificate": {"ref_code": "Ref Code เมื่อตั้งค่าแล้วไม่สามารถเปลี่ยนแปลงได้", "refName": "กรอกเฉพาะประเภทไม่ต้องใส่คำว่าประกาศนียบัตร ตัวอย่างการนำไปแสดงผล “ประกาศนียบัตร[Ref Name]”", "tsiCode": "TSI Course Code เป็นรหัสที่ใช้แสดงบนประกาศนียบัตรของ TSI เท่านั้น", "type": "ใช้สำหรับอ้างอิงถึงประเภทการประกัน"}, "enrollment_detail": {"no_permission": "No permission"}, "announcement": {"highlight_info": "สำหรับข่าวสาร/ประกาศ ที่ต้องการแสดงให้ผู้เรียน เห็นด้านบนสุดของหน้า Annoucement ง่ายต่อการสื่อสาร", "at_least_item": "จำกัดสูงจุด 21 รายการ หากต้องการเพิ่ม กรุณานำหัวข้อข่าวสารและประกาศอื่นออกก่อน"}}, "content_avaliable": "Avaliable", "processing": "Processing", "processing_failed": "Failed to process", "no_content": "No Content", "pre_enroll": {"please_cancel_transaction_from_b2c": "Please cancel this transaction from SkillLane Retail (B2C).", "cannot_change_this_status": "Cannot be change to this status"}}, "uploader": {"text": {"size": "Dimension", "maximum_file_size": "Maximum file size", "supported_files": "Supported files"}, "error": {"course": {"file_exceed_size": "File size exceeded", "file_invalid_format": "Please choose to upload the file format .png, .jpg,.jpeg and the size does not exceed 30 MB."}}, "button": {"course": {"choose_file": "Choose <PERSON>"}}, "dropzone": {"choose_file": "Choose or drag file here", "alert": "Can not upload file due to some file is not supported"}}, "drawer": {"edit_title": "Edit: {0}", "title": {"create": "Create: {0}", "update": "Edit: {0}", "user": {"edit_additional": "Edit: Additional information"}, "course": {"title": {"edit_course_property": "Edit: Course Property", "edit_course_overview": "Edit: Course Overview", "edit_course_certificate_approval_setting": "แก้ไข: ตั้งค่าการตรวจสอบการเรียนจบ", "edit_course_certificate_criteria_setting": "แก้ไข: เงื่อนไขการได้รับประกาศนียบัตร"}}, "certificate": {"edit_certificate_setting": "Edit: Set Diploma"}, "discard": "ต้องการละทิ้งข้อมูลทั้งหมดหรือไม่"}, "course": {"title": {"create_part": "C<PERSON> <PERSON><PERSON>", "edit_part": "<PERSON>", "edit_course": "Edit: {0}"}, "label": {"saving": "Saving..."}}, "enrollment": {"remark": {"title": "Remark"}}, "quiz": {"btn": {"create_answer": "Add Answer", "create_question": "Create Question"}, "label": {"create": "Create", "quiz": "Quiz", "update": "Update"}, "title": {"answer": "Answer", "answer_create": "Create an answer", "answer_create_success": "Create an answer success", "answer_delete_success": "Remove an answer success", "answer_update": "Update: Answer", "basic_details": "Basic Details", "correct": "Correct", "create": "Create a quiz", "question": "Question", "question_create": "Create a question", "question_create_success": "Create a question success", "question_delete_success": "Remove a question success", "question_subtitle": "Create and Edit Questions", "question_update": "Update: Question", "setting": "Setting", "status": "Status", "update": "Update: Quiz", "save": "Saved success"}, "tooltip": {"enabled_error_required": "ไม่สามารถเปิดใช้งานได้ เนื่องจากไม่มีแบบทดสอบ", "question_error_required": "ไม่สามารถลบได้เนื่องจากต้องมีคำตอบอย่างน้อย 2 คำตอบ"}}, "survey": {"title": {"basic_information": "basic_information", "content_html": "Contnet of survey (HTML)", "content_html_secondary": "Enabled for JotForm HTML", "status": "Status"}}, "user": {"create_user_title": "Create User", "edit_general_information_title": "Edit: General Information", "edit_license_title": "Edit: License"}, "content": {"discard_message": "เมื่อดำเนินการละทิ้งแล้วไม่สามารถนำกลับมาได้"}, "department": {"create_title": "Create Department", "ordering_department_hierarchy": "Ordering Department Hierarchy", "edit_title": "Edit: {0}"}, "permission_group": {"create_title": "Create Permission Group", "update_title": "Update Permission Group", "grant_permission_title": "Grant Permission"}, "knowledge_content_category": {"edit_overview_title": "Edit: Overview", "edit_knowledge_content_item_title": "Edit: Knowledge Contents"}, "promote_notification": {"create": "Create", "create_notification": "Create a notification", "edit": "Edit", "general_title": "General", "name": "Notification name", "input_name": "Enter a name for the notification.", "content": "Notification content", "select_content": "Select notification content", "title": "Title", "input_title": "<PERSON><PERSON> in the title", "description": "Description"}}, "list": {"subtitle": {"course": {"image_size": "size: {0} px"}}, "title": {"certificate": {"approval_type": "เงื่อนไขการเรียนจบ"}}}, "page": {"title": {"course": {"title_detail": "Edit Content", "edit": "Edit Content: {0}"}}, "user": {"title": "Users", "sub_title": "Total: {0} Items"}, "waiting_to_enroll_detail": {"title": "Details of waiting for enrollment"}, "department": {"edit_title": "Edit: {0}"}}, "btn": {"edit": "Edit", "ok": "Confirm", "create": "Create", "save": "Save", "confirm": "Confirm", "cancel": "Cancel", "cancel_items": "Cancel items", "discard": "Discard", "done": "Done", "delete": "Delete", "download": "Download", "reset": "Reset", "collapse_all": "Collapse", "expanded_all": "Expanded", "view_detail": "View Detail", "course": {"preview_video": "Preview", "preview_video_view": "Preview", "curriculum": {"create_part": "C<PERSON> <PERSON><PERSON>", "create_course_item": "Create Content"}}, "enrollment_detail": {"additional_document": {"request": "Request"}, "activity_log": {"compare": "Compare", "view_detail": "View Details"}}, "department": {"delete": "Delete"}}, "tab": {"achievement": {"overview": "General", "reward": "<PERSON><PERSON>"}, "course": {"general": "General", "curriculum": "Curriculum", "instructor": "<PERSON><PERSON><PERSON><PERSON>"}, "enrollment_detail": {"overview": "Overview", "attendance_history": "Attendance History", "learning_history": "Learning History", "learning_progress": "Learning Progress", "approval_history": "Approval History", "learning_detail": "Learning Detail", "reset_history": "Reset Enrollment History"}, "oic_enrollments": {"waiting_to_enroll": "Waiting to enroll", "learning": "Learning", "pending_approval": "Pending Approval"}, "waiting_to_enroll_detail": {"overview": "Overview", "history": "History"}, "department": {"list": "List", "hierarchy": "Hierarchy", "general": "General", "moderators": "Moderators", "members": "Members"}, "my_team": {"pre_enroll": "Pre-Enrollment", "report": "Report"}, "knowledge_content_category": {"overview": "Overview", "knowledge_content_list": "Knowledge Content List", "title": "Knowledge Content Category: {0}"}}, "tag": {"active": "Active", "inactive": "Inactive", "enroll_type": {"compulsory": "Compulsory", "voluntary": "Voluntary"}}, "placeholder": {"column_setting": {"search": "Search"}, "user": {"citizen_required": "Enter citizen", "email_required": "Enter email", "username_required": "Enter username", "salute_required": "Enter salute", "firstname_required": "Enter first name", "middlename_required": "Enter middle name", "lastname_required": "Enter last name", "citizen_id_required": "Enter citizen id", "phone_number_required": "Enter phone number", "gender_required": "Select gender", "date_of_birth_required": "Select Date of Birth", "search_keywords_supervisor": "Search by name/lastname/email of supervisor"}, "license": {"oic_non_life_no_license_required": "Enter OIC non life no", "oic_life_no_license_required": "Enter OIC life no", "tsi_no_license_required": "Enter TSI no", "started_required": "Select license started", "expired_required": "Select license expired"}, "additional": {"input_required": "Enter {0}", "select_required": "Select {0}"}, "course": {"part_name": "Enter name", "part_description": "Enter description"}, "enrollment": {"remark": "Enter your remarks here"}, "pre_enroll": {"please_select_status": "Select status", "enter_reason": "Enter reason"}, "survey": {"name": "Enter survey name", "description": "Enter description", "contentHtml": "Enter Content"}, "department": {"name": "Enter department name", "code": "Enter department code", "description": "Enter description", "parent": "Select main department", "select_item": "Select department"}, "permission_group": {"name": "Enter permission group name", "description": "Enter description"}, "knowledge_content_item": {"title": "Title", "enter_code": "Enter code"}, "knowledge_content_category": {"title": "Title", "enter_code": "Enter code", "enter_title": "Enter category name", "search_code_and_name": "Search by code/name"}, "user_group": {"warning_discard_title": "Would you like to discard all data?", "warning_discard_content": "Once discarded, it cannot be returned."}, "classroom": {"location_required": "Enter Location"}}, "form": {"user": {"username": "Username", "salute": "Prefix", "firstname": "First Name", "middlename": "Middle Name", "lastname": "Last Name", "email": "Email", "citizen_id": "Citizen ID", "phone_number": "Phone Number", "gender": "Gender", "date_of_birth": "Date of Birth", "employee_id": "Employee Id", "position": "Position"}, "license": {"started": "License started", "expired": "License expired"}, "radio": {"label": {"certificate": {"conditional_approval_type": "เงื่อนไขการเรียนจบ"}}}, "pre_enroll": {"status": "Status", "reason": "Reason for edit"}, "user_group": {"name": {"placeholder": "user group name"}, "isEnabled": {"label": "user group status:"}, "participation_type": {"label": "participation"}, "fullname": {"label": "full name"}, "citizenId": {"label": "citizen"}, "email": {"label": "email"}, "updatedAt": {"label": "update date:"}}, "my_team_report": {"name": "Search...", "sort": "Sort by {0}"}}, "form_error": {"please_enter": "Please enter {0}", "course": {"part_name_required": "Please enter name"}, "enrollment_detail": {"additional_document": {"select_document_list": "Please select at least 1 item.", "select_date": "Please select the document submission date", "enter_request_reason": "Please enter the reason for document request."}, "remark": {"max_length": "Remark must be less than {0} characters"}, "enter_reason": "กรุณากรอกเหตุผล"}, "user": {"upload_avatar": "Please select upload file format .png, .jpg, .jpeg and size not exceeding 30 MB", "select_salute": "Please select salute", "enter_salute": "Please choose salute", "enter_name": "Please enter name", "enter_username": "Plesase enter username", "enter_firstname": "Please enter firstname", "enter_lastname": "Please enter lastname", "enter_license_no_tsi": "Please enter tsi license no.", "email_invalid": "Email invalid", "citizen_id_invalid": "Citizen ID invalid", "citizen_id_fill_13_digit": "Please fill in your ID card number to complete 13 digits.", "phone_number_invalid": "Phone number invalid", "password_format_incorrect": "Password format is incorrect", "name_format_incorrect": "Invalid format. Please enter {0} again.", "username_invalid": "Invalid format. Please enter a username", "username_least_characters": "Please enter a username at least 8 characters and no more than 30 characters", "license_no_tsi_invalid": "License number is incorrect", "license_no_tsi_x_digit": "Please enter the license number to complete {0} digits", "license_no_oic_invalid": "License number is incorrect", "license_no_oic_x_digit": "Please enter the license number to complete {0} digits", "already_exists_username": "Username already exists. Please enter a new username.", "already_exists_email": "Email already exists. Please enter a new email.", "already_exists_citizen_id": "Citizen ID already exists. Please enter a new citizen id.", "please_select_supervisor": "Please select supervisor", "employee_id_length": "The length of the employee id cannot be longer than 200 characters", "position_length": "The length of the position cannot be longer than 200 characters"}, "license": {"tsi_type": "Please select TSI license type"}, "pre_enroll": {"please_select_status": "Please select status", "please_select_course_for_cancel_cost": "Please select the course for cancel with cost"}, "department": {"name_required": "Please enter department name", "name_limit_length": "Department name should be no longer than {0} characters", "code_required": "Please enter department code", "start_with_space": "Department name can't start with space", "end_with_space": "Department name can't end with space", "consecutive_space": "Department name can't have consecutive space", "code_invalid": "Only upper characters (A-Z), number (0-9), and underscore (_) are allowed", "code_duplicated": "Department code has already existed", "code_limit_length": "Department code should be no longer than {0} characters", "description_limit_length": "Description should be no longer than {0} characters", "please_select_department": "Please select to main department"}, "permission_group": {"name": "Please enter permission group name", "name_limit_length": "Permission group name should be no longer than {0} characters", "name_required": "Please enter permission group name", "description_limit_length": "Description should be no longer than {0} characters", "name_duplicated": "Permission group name has already existed"}, "knowledge_content_category": {"title_required": "Please enter knowledge category name", "title_limit_length": "Knowledge category name should be no longer than {0} characters"}, "promote_notification": {"name_required": "Please enter notification name", "name_limit_length": "Notification name should be no longer than {0} character", "name_start_with_space": "Notification name can't start with space", "name_end_with_space": "Notification name can't end with space", "name_consecutive_space": "Notification name can't have consecutive space", "name_duplicated": "This notification name already exists. Please enter a new notification name.", "content_required": "Please select notification content.", "title_required": "Please enter notification title", "title_limit_length": "Notification title should be no longer than {0} character", "title_start_with_space": "Notification title can't start with space", "title_end_with_space": "Notification title can't end with space", "title_consecutive_space": "Notification title can't have consecutive space"}}, "switch": {"enabled": "Enable", "disabled": "Disable"}, "message": {"auth": {"login_success": "login success", "login_error": "something went wrong please try again", "logging_in": "logging in", "logging_out": "logging_out"}, "course": {"not_found_error": "Course not found", "update_curriculum_success": "Saved success", "update_curriculum_error": "Something was wrong. can't to save", "create_part_success": "Created “{0}” success", "create_part_error": "Something was wrong. can't to create", "update_part_success": "Saved Success", "update_part_error": "Something was wrong. can't to save", "delete_part_success": "Deleted “{0}” success", "delete_part_error": "Something was wrong. can't to delete", "delete_part_conflict_error": "Something was wrong. can't to delete, please your delete content before", "create_course_item_success": "Created “{0}” success", "create_course_item_error": "Something was wrong. can't to create", "update_course_item_success": "Saved Success", "update_course_item_error": "Something was wrong. can't to save", "delete_course_item_success": "Deleted “{0}” success", "delete_course_item_error": "Something was wrong. can't to delete"}, "certificate": {"error_add_certificates": "Cannot add multiple certificate, please enable multiple certification", "error_enable_multiple_certificate": "Please enable certification before enable multiple certification", "error_disable_certificate": "Please disable multiple certification before disable certification", "success_delete_certificate": "Certificate successfully deleted", "success_save_sent_mail_certificate": "Save and test send successfully.", "success_save_error_sent": "Save succeeded, but test send failed.", "loading_new_certificate": "The system is currently issuing a new certificate.", "success_new_certificate_and_sent_email": "Issue a new certificate and the email was successfully sent", "success_new_certificate": "Successfully issued a new certificate", "error_unprocessable_new_certificate": "Cannot be reissued Because it has not yet passed the approval process", "error_new_certificate": "Something was wrong. can't to create"}, "pre_enroll": {"update_status_success": "Updated success", "update_status_error": "Something was wrong. can't to edit"}, "department": {"create_department_success": "Created “{0}” success", "create_department_failed": "Something was wrong. can't to create", "update_department_success": "Saved Success", "update_department_failed": "Something was wrong. can't to save", "delete_department_success": "Deleted “{0}” success", "delete_department_failed": "Something was wrong. can't to delete", "fetch_department_failed": "The department was not found"}, "permission": {"no_permission": "No permission"}, "permission_group": {"create_permission_group_success": "Created “{0}” success", "create_permission_group_failed": "Something was wrong. can't to create", "fetch_permission_group_detail_failed": "Not found this Permission group in system", "update_permission_group_success": "Updated “{0}” success", "update_permission_group_failed": "Something was wrong. can't to edit", "name_duplicated": "Permission group name has already existed"}, "announcement": {"create_announcement_success": "Create announcement success", "save_announcement_success": "Saved success", "add_announcement_success": "Add new announcement success", "add_publish_duration_success": "Add publish duration success", "edit_publish_duration_success": "Edit publish duration success", "delete_success": "Delete success", "cancel_publish_success": "Cancel publish success", "create_announcement_error": "Something went wrong, Could not create", "update_status_error": "Something went wrong, Could not update", "not_found_error": "Announcement not found", "delete_error": "Something went wrong, Could not delete"}, "user": {"update_organization_success": "Saved Success", "update_organization_error": "Something was wrong. can't to save"}, "knowledge_content_item": {"create_knowledge_content_item_success": "Create knowledge content item success", "create_knowledge_content_item_error": "Something went wrong, Could not create", "delete_knowledge_content_item_success": "Delete knowledge content item success", "delete_knowledge_content_item_error": "Something went wrong, Could not delete", "edit_knowledge_content_item_success": "Edit knowledge content item success", "edit_knowledge_content_item_error": "Something went wrong, Could not update", "edit_knowledge_content_item_attachment_success": "Edit attachments success", "edit_knowledge_content_item_attachment_error": "Something went wrong, Could not update", "edit_knowledge_content_item_save_empty_article_error": "Unable to save because the article content has no information. If you want to save, you must cancel the publishing.", "edit_knowledge_content_item_save_empty_ebook_error": "Unable to save because there is no pdf file. If you want to save, you must cancel publishing.", "add_publish_duration_success": "Add publish duration success", "edit_publish_duration_success": "Edit publish duration success", "cancel_publish_success": "Cancel publish success", "update_status_error": "Something went wrong, Could not update", "not_found": "Knowledge content item not found"}, "user_group": {"update_user_map_user_group_success": "Saved Success", "update_user_map_user_group_failed": "Something was wrong. can't to save", "update_condition_success": "Saved Success", "update_condition_error": "Something was wrong. can't to save"}, "categories": {"update_course_categories_order_success": "Saved Success", "update_category_courses_success": "Saved Success", "create_category_courses_error": "Something went wrong, Could not create", "update_category_courses_error": "Something went wrong, Could not update", "not_found": "Category not found"}, "knowledge_content_category": {"create_knowledge_content_category_success": "Create knowledge content category success", "create_knowledge_content_category_error": "Something was wrong. Could not create", "delete_knowledge_content_category_success": "Delete knowledge content category success", "delete_knowledge_content_category_error": "Something went wrong, Could not delete", "update_error": "Something went wrong, Could not update", "not_found": "Knowledge content category not found"}, "instructor": {"delete_error": "Something went wrong. can't to delete", "delete_in_use_error": "can't to delete instructor is in use"}, "learning_path": {"not_found": "Learning path not found"}, "enrollment_attachment_additional_type": {"create_enrollment_attachment_additional_type_success": "Create enrollment attachment additional type success", "create_enrollment_attachment_additional_type_failed": "Something was wrong. Could not create", "fetch_enrollment_attachment_additional_type_detail_failed": "Enrollment attachment additional type not found", "update_enrollment_attachment_additional_type_success": "Saved Success", "update_enrollment_attachment_additional_type_failed": "Something went wrong, Could not update", "delete_enrollment_attachment_additional_type_success": "Delete enrollment attachment additional type success"}}, "card_title": {"course": {"curriculum": "Content"}, "enrollment_detail": {"additional_document": "Additional Document", "authen_info": "Verification Information", "retrieved_from_card": "Data Retrieved from ID Card", "user_info": "User Information", "learning_summary": "Learning Summary", "verify_enroll": {"approval_history": "Approval History"}, "activity_log": {"learning_summary": "Learning Summary"}, "attendance_history": "Attendance History", "reduction_document": "Training Hour Reduction Document", "id_card_photo": "ID card photo (Taken through the camera)", "face_photo": "Photo of face", "taken_through_the_camera": "Taken through the camera", "upload_photo": "Upload photo", "remark": "Remark"}, "empty_data": "No data available", "minutes": "minutes", "hours": "hours", "percentage_from_total": "from", "empty_quiz": "No quiz available.", "empty_classroom": "No classroom available.", "score": "score", "times": "times", "quiz_criteria_enable": "(Measure result)", "classroom_criteria_enable": "(Measure result)"}, "card_label": {"total": "Total", "pass": "Pass", "fail": "Fail", "type": "Type", "status": "Status", "enrollment_detail": {"content_detail": {"enroll_date": "Enroll Date", "finish_date": "Finish Date", "expiration_date": "Expiration Date", "course_objective_type": "Objective Type", "course_regulator": "Regulator"}, "learning_progress": {"progress": "Progress (%)", "remaining_time": "Remaining Time", "total_time_spent": "Total Time Spent"}, "license": {"oic_nonlife": "Non-life Insurance License", "oic_life": "Life Insurance License", "tsi": "Investment License"}, "additional_document": {"modal_title": "Request Additional Documents", "due_date": "Due Date", "request_by": "Request By", "requested_document": "Requested Document", "document_list": "Document List (Select up to 5 items)", "select_due_date": "Select Due Date for Document Submission (By 12:00 PM)", "request_reason": "Reason for Document Request", "select_due_date_placeholder": "Select document submission date.", "request_reason_placeholder": "Please enter the reason for document request.", "toast": {"request_success": "Successfully created additional document request list", "request_error": "Something went wrong"}}, "activity_log": {"identity_verification": "Verification", "attention": "Attention"}, "reduction_document": {"status": "Status", "submission_date": "Submission Date", "submission_document": "Submission Document", "qualifications_request_deduct": "Qualifications Request Deduct"}, "credit_usage": {"title": "Credit usage"}}}, "card_sub_title": {"course": {"curriculum": "Create Lesson / Reorder / Create and Edit content"}, "enrollment_detail": {"content_detail": {"course_dates": "Course Dates", "learning_progress": "Learning Progress", "activity_log": "Attendance", "enrollment_info": "Enrollment Information"}, "activity_log": {"in_session": "In Session", "verification_unsuccessful": "Verification unsuccessful"}, "quiz_score": "Quiz Score", "classroom": {"title": "Classroom Detail", "attendance_score": "Attendance Score", "homework_score": "Homework Score"}}}, "tag_sub_title": {"course": {"content_type_e_learning": "e-Learning", "content_type_event": "Event"}}, "general": {"label": {"active": "Active", "inactive": "Inactive"}, "upload_thumbnail": {"fileNotFound": "File Not Found", "choose_file": "Choose <PERSON>", "file_invalid_format": "Please choose to upload the file format .png, .jpg,.jpeg and the size does not exceed 30 MB."}}, "menu": {"learning_reward": {"title": "Learning Reward", "achievement": "Achievement", "badge": "Badge"}, "announcement": {"title": "All announcements and news"}, "badge": {"title": "badge"}, "home": {"title": "Home"}, "department": {"title": "Departments"}, "user_management": {"title": "User Management"}, "user": {"title": "All Users"}, "content": {"title": "Contents", "courses": "All Contents", "bundles": "Bundles", "rounds": "Round Management", "classroom_rounds": "Classroom Rounds", "categories": "Categories", "instructors": "Instructors", "learning_path": "Learning Paths"}, "learning": {"title": "Learnings", "enrollments": "Enrollments", "regular_enrollments": "Regular Enrollments", "oic_enrollments": "OIC Enrollments", "waiting_to_enroll": "Waiting to Enroll", "enrollment_in_progress": "In Progress", "enrollment_approval": "Pending Approval", "classroom_round_enrollments": "Classrooms", "documents": "Documents", "additional_document": "Additional Document", "deduct_document": "Deduct Document", "discussion_board": "Discussion Board"}, "setting": {"title": "Setting", "title_group": "Settings", "back_to_main_menu": "Back to main menu", "bulk_activity_log": "Bulk Activity Logs", "enrollment_file_log": "Enrollment File Logs", "permission_groups": "Permission Groups", "activity_log": "Activity Logs", "user_access_log": "User Access Logs", "report_history": "Report Histories", "theme": "Theme", "enrollment_attachment_additional_type": "Document Management", "contacts": "Contacts", "terms_and_conditions": "Terms and Conditions"}, "knowledge_content": {"title": "Knowledge Content", "contents": "All Knowledge Content", "categories": "Knowledge Categories"}, "user_group": {"title": "User Groups"}, "report": {"title": "Report", "oic": "รายงานวิชาชีพประกันภัย", "tsi": "รายงานวิชาชีพการลงทุน", "enrollment": "รายงานการเรียน", "knowledge_content": "รายงานสื่อความรู้"}, "customer": {"title": "Customer"}, "notification": {"title": "Notification", "promote": "Promote"}, "plan": {"title": "Plan"}}, "dropdown": {"profile": {"admin": "Admin Management", "hr": "HR Management", "regulator": "Regulator Dashboard", "home": "Home Page", "my_learning": "My Courses", "catalog": "Catalog", "skilllane_course": "SkillLane Courses", "documents": "Documents", "profile_setting": "Profile Setting", "categories": "Categories", "my_team": "My Team", "announcement": "News and Announcement", "knowledge_content": "Knowledge Content", "my_knowledge_content": "My Knowledge Content"}}, "select_option": {"enrollment_detail": {"title": "Delete Image Confirmation", "authen_info": {"preview": "Preview", "upload": "Upload", "remove": "Remove"}}}, "table": {"action": "Action", "no_data": "No data", "loading": "Loading...", "total_items": "Total {0} items", "order_column": "Order", "enrollment_detail": {"verify_enroll": {"column": {"order": "Order", "items": "Items", "created_by": "Created By", "date_time": "Date and Time", "remark": "Remark"}}, "activity_log": {"column": {"order": "Order", "type": "Type", "status": "Status", "similarity_score": "Similarity Score", "date_time": "Date and Time", "detail": "Detail"}}, "attendance_history": {"column": {"similarity_score": "Similarity Score", "detail": "Detail"}}, "reset_history": {"column": {"action": {"reset_learning_history": "Reset attendance and learning history", "reset_learning_progress": "Reset learning progress"}, "action_by": "Action By", "detail": "Detail"}}}, "waiting_to_enroll_detail": {"history": {"column": {"date_time": "Date and Time", "detail": "Detail", "edited_by": "Edited by"}}}, "my_team": {"members_title": "The Members ({0} people)", "moderators_title": "The Moderators ({0} people)", "subordinates_title": "The Subordinates ({0} people)"}}, "alert": {"pre_enroll": {"choose_having_cost_not_be_change": "When choosing a paid cancellation and press confirm It will not be able to change back to no cost again", "canceled_and_confirmed_not_be_edit": "When the item status is changed to Canceled and then clicked to confirm. will not be able to fix it anymore", "please_cancel_transaction_from_b2c": "Cannot be canceled on the CPD platform. Please cancel this item from SkillLane Retail (B2C)"}, "enrollment_deduct": {"approve": "อนุมัติเอกสารลดหย่อนชั่วโมงการเรียนแล้ว", "not_sent": "รออนุมัติเอกสารลดหย่อนชั่วโมงการเรียน", "pending_approval": "รออนุมัติเอกสารลดหย่อนชั่วโมงการเรียน", "reject": "ไม่อนุมัติเอกสารลดหย่อนชั่วโมงการเรียน"}}, "checkbox": {"pre_enroll": {"cost_cancellation": "Cost Cancellation"}}, "top_bar": {"announcement": "News and Announcement", "catalog": "Catalog", "my_content": "My Content", "search_placeholder": "search contents", "knowledgeContentItems": "Knowledge Contents"}, "upload": {"pending_title": "Importing", "success_title": "Importing Completed", "total_upload_description": "Importing completed {0} records", "pending_description": "Wait a minute..."}, "transfer": {"search_result": "Search results: {{searchResultTotals}} items (selected {{selectedTotals}} items)", "department": {"transfer_users_title": "Users", "transfer_members_title": "Members", "transfer_moderators_title": "Moderators"}, "permission_group": {"transfer_users_title": "Users", "transfer_members_title": "Members “{0}”"}, "user_group": {"transfer_users_title": "Users", "transfer_members_title": "Members “{0}”"}}, "pre_enrollment": {"notification": {"open": {"message": "Start downloading waiting for enrollment report", "description": "One moment..."}, "downloading": {"message": "Downloading waiting to enroll report", "description": "Downloaded"}, "success": {"processing": {"message": "Processing file", "description": "This may take a while"}, "message": "Successfully downloaded waiting for enrollment report", "description": "Please check the downloaded file"}, "error": {"message": "There was an error downloading the report of of waiting to enrollment", "description": "Please,try again"}}}, "user": {"notification": {"open": {"message": "Start downloading user report ", "description": "One moment..."}, "downloading": {"message": "Downloading user report ", "description": "Downloaded"}, "success": {"processing": {"message": "Processing file", "description": "This may take a while"}, "message": "Successfully downloaded user report", "description": "Please check the downloaded file"}, "error": {"message": "There was an error downloading the report of user", "description": "Please,try again"}}}, "organization_report": {"notification": {"open": {"message": "Start downloading report ", "description": "One moment"}, "success": {"message": "Successfully downloaded report", "description": "This may take a while"}, "error": {"message": "There was an error downloading the report", "description": "Please,try again"}, "system_processing": "The System is processing.", "review_in_report_histories": "Can you check the report by clicking on 'Check' or go to the report creation history page."}, "modal": {"submit": "Download", "cancel": "Cancel"}, "button": {"check": "Check"}}, "user_group": {"drawer": {"title": "Create user group", "submit": "Create", "general": "General Information", "user_group_name": "User group name", "placeholder_user_group_name": "Enter user group name", "description": "Description", "placeholder_description": "Enter description", "require_user_group_name": "Please enter the name of the user group", "duplicate_user_group_name": "User group name has already existed, Please enter the name of another user group", "user_group_name_less_three_char": "The length of the user group name must be longer than 3 characters", "user_group_name_more_two_hundred_char": "The length of the user group name cannot be longer than 200 characters", "start_with_space": "User group name can't start with space", "end_with_space": "User group name can't end with space", "consecutive_space": "User group name can't have consecutive space"}, "toast": {"success": "Create user group success", "error": "Create user group error"}}, "learning_path": {"detail": {"title": "Edit Learning path", "general": {"title": "General", "tab": {"overview": "Overview", "access": "Access", "enrollment": "Enrollment", "learn": "Learn", "certificate": "Certificate"}, "drawer": {"overview": {"title": "Edit: Overview", "description": "Description", "submit": "Save"}, "learning": {"drawer_title": "Edit: Learning setting", "title": "Learning setting", "sub_title": "Study in order of learning content"}, "access": {"drawer_title": "Edit: Access", "access_type_title": "User group Access", "access_type": "Access condition", "public": "Public learning path", "private": "Private learning path", "private_with_user_group": "Learning path specific user group", "user_group": {"title": "User group", "select": "Select user group", "select_placeholder": "Search user group name", "required": "Please select at least 1 user group", "add": "Add user group"}}, "enrollment": {"self_enroll": {"drawer_title": "Edit: Enrollment setting", "is_self_enroll_title": "Enrollment setting", "label": "Learner can self enroll", "switch": "Active"}, "expiry_day": {"drawer_title": "Edit: Expiry day setting", "title": "Expiry day setting", "enabled_expiry_day": "Expiry day enabled", "expiry_day": "Expiry day", "placeholder": "Enter expiry day", "required": "Please fill expiry day", "more_zero": "Expiry day must be greater 0 day", "only_number": "Please fill only number"}, "enroll_type": {"drawer_title": "Edit: Enroll type setting", "title": "Enroll type setting", "label": "Enroll type", "tooltip": "Learning path need round before select this options"}}}, "learning": {"setting": "Learning setting"}, "access": {"title": "Access", "user_group_title": "User group", "total_user_group": "Total {0} record"}, "enrollment": {"self_enroll": {"title": "Enrollment setting"}, "expiry_day": {"title": "Expiry day setting", "is_expiry_day": "Expiry day enabled", "value": "Expiry day"}, "enroll_type": {"title": "Enroll type setting"}}, "save": {"success": "Saved successfully", "error": "Failed to save"}}, "content": {"title": "Contents", "table": {"columns": {"code": "Code", "thumbnail": "<PERSON><PERSON><PERSON><PERSON>", "name": "Name", "status": "Status", "created_at": "Created At", "action": "Manage", "view_detail": "View Detail", "remove_content": "Remove"}}, "edit": {"title": "Edit: Contents", "success": "Success", "error": "Something went wrong", "remove": {"title": "Confirm remove content", "content": "Once an confirmation, The content could be", "content_2": "brought back from the edit menu.", "confirm": "Remove"}}}, "history": {"title": "Version history", "current_published_version": "Current published version", "version": "version", "published_date": "Published at", "by": "by", "all_version": "All versions", "status": {"published": "Published", "draft": "Draft"}, "table": {"version": "Version", "status": "Status", "created_date": "Created at", "published_date": "Published at", "published_by": "Published by"}}, "footer": {"status": "Learning path status", "publish": "Publish", "enabled": "Enabled", "disabled": "Disabled", "save": "Save Status"}, "modal": {"unable_publish": {"title": "Can't publish this learning path", "incomplete_warning": "Due to insufficient information, including", "recommend_line_1": "Please check and complete the above", "recommend_line_2": "Information. before reactivating later.", "confirm": "Confirm", "no_content": "Learning path mush have content at least one", "no_expiry_day": "Learning path mush have expiry day"}}, "toast": {"enable_fail": "Something went wrong, Can not enable learning path", "disable_fail": "Something went wrong, Can not disable learning path", "publish_fail": "Something went wrong, Can not publish learning path", "fetch_fail": "Something went wrong, learning path not found", "enable_success": "Enabled success", "disable_success": "Disabled success", "publish_success": "Published success", "update_success": "Update successfully", "update_error": "Something went wrong, Can not updated", "fetch_user_group_dropdown_fail": "Something went wrong, Can't search user groups", "fetch_user_group_detail_fail": "Something went wrong, Can't add user groups", "not_have_expiry_day": "To choose the schedule,Please setting expiry day"}, "delete_button": "Delete learning path"}, "drawer": {"title": "Create Learning path", "submit": "Create", "general": "General Information"}, "form": {"code": {"label": "Learning path code", "placeholder": "Enter learning path code"}, "randomCode": "Auto fill", "name": {"label": "Learning path name", "placeholder": "Enter learning path name"}}, "validations": {"name": {"required": "Please enter the learning path name", "existed": "Learning path name already existed", "less_than_minimum": "The length of the learning path name must be longer than equal 3 characters", "more_than_maximum": "The length of the learning path name must be less than equal 200 character", "start_with_space": "Learning path name can't start with space", "end_with_space": "Learning path name can't end with space", "consecutive_space": "Learning path name can't have consecutive space"}, "code": {"required": "Please enter the learning path code", "existed": "Learning path code already existed", "invalid": "Only upper characters (A-Z), number (0-9), and underscore (_) are allowed."}}, "modal": {"confirm_delete": {"title": "Do you want to delete this learning path?", "content_line_1": "Learning path “{0}”", "content_line_2": "Once deleted, it can't be revert"}}, "toast": {"error": {"create": "An error occurred. Unable to create a learning path", "code": "An error occurred in generating the automatic learning path code", "delete": "An error occurred. Unable to delete a learning path"}, "success": "Successfully create a learning path", "delete_success": "Successfully delete a learning path"}, "certificate": {"criteria_content": {"tooltip": "Enabled to receive a certificate according to the specified conditions", "approve": "Approve", "columns": {"content_name": "Content name", "criteria": "Criteria", "criteria_enabled": "Enable"}, "placeholder": {"input_number": "Enter the number"}, "validate": {"please_input_number": "Please enter a number.", "invalid_format": "Invalid format Please enter a number."}, "alert": {"closed_all": "If all conditions are disabled, the certificate cannot be enabled. Please enable conditions for at least 1 content."}}, "message": {"refName": {"name": "Please enter a reference name. (Ref Name)", "tooltip": "The reference name will be displayed on the email in which the certificate was received. Example of display “Certificate[Ref Name]”"}, "error": {"no_certificate": "Unable to enable certificate, Please fill out and save the certificate information before enabling it.", "all_criteria_closed": "Unable to enable certificate at least one certificate criteria must be enabled."}}}}, "criteria_content": {"columns": {"content_name": "Course", "criteria_enabled": "Criteria"}, "learning_progress": {"drawer_title": "Edit: Conditions for allowing the use of original learning progress", "title": "Conditions for allowing the use of original learning progress", "tooltip": "Activate to allow learners to use the original learning progress of the course as the learning progress of the learning path."}, "expire_learning": {"title": "Conditions for continuing studies after learning path expiration", "disable": "Can't enable", "tooltip": "Enabled To allow learners to continue their course after the end of their learning path", "tooltip_course_expiry_day": "Can't use this condition, Because this course has an expire date that is not related to the end of the learning path."}}, "knowledge_content_item": {"media": {"drawer": {"article": {"title": "Edit: Article Content", "content_title": "Article Content", "content": "Content"}, "video": {"upload_video": {"button": "Select Video", "error": "Please select the .mp4 file format and a size not exceeding 10GB", "descr": "Select only 1 file, .mp4 format, with a size not exceeding 10GB"}, "is_downloadable_title": "Enable Downloads", "is_downloadable": "Learners can download the media"}, "ebook": {"title": "Edit: PDF File", "content_title": "Attachment", "descr": "Please select only the .pdf file format"}, "podcast": {"title": "Edit: Audio File", "upload_audio": {"title": "Audio File", "descr": "Select only 1 file, .mp3 or .mp4 format, with a size not exceeding 10GB", "button": "Select Audio", "error": "Please select to upload a file in .mp3 or .mp4 format and a size not exceeding 10GB", "no_audio": "No available audio file", "alert": {"success": "The audio is ready to be used", "transcoding": "The audio takes a while to process, depending on the file size. You can close this window while it's processing", "transcode_failed": "Audio transcode failed, please try again", "transcodeSuccess": "Audio transcode success", "uploading": "The audio is currently uploading. Please do not exit this window.", "upload_failed": "Audio upload failed. Please try again."}}, "is_downloadable_title": "Enable Downloads", "is_downloadable": "Learners can download media"}}, "detail": {"ebook": {"title": "PDF File"}, "video": {"title": "Video File", "is_downloadable": "Download Enabled"}, "podcast": {"title": "Audio File"}, "article": {"title": "Article", "content": "Content"}}, "confirm_modal": {"title": "Do you want to discard all the content?", "body": "Once discarded, it cannot be retrieved."}, "confirm_modal_replace_upload": {"video": {"title": "Want to choose to upload a new video? Replace current video?", "content": "When you choose to upload a new video This will result in the current video being canceled and unusable. and was immediately replaced with a new video.", "ok": "Select new video"}, "podcast": {"title": "Want to choose to upload a new audio file? Replace current audio file?", "content": "When choosing to upload a new audio file This will result in the current sound file being canceled and unusable. and was immediately replaced with a new sound file.", "ok": "Select a new audio file."}}, "tooltip_disable_status_publish": "Cannot be edited If Knowledge Content is in the Published or Scheduled status."}, "category": {"title": " Knowledge Content's Categories ({0})", "drawer": {"edit": {"title": "Edit:  Knowledge Content's Categories", "name": "Add Categories", "description": "Add/ Create Categories", "list": {"title": "Knowledge Content's Categories ({0})"}, "dropdown": {"label": "Add/ Create Categories", "placeholder": "Search Categories", "add_button": "Add Categories"}}}, "message": {"success": "Edit Knowledge Content's Categories Success", "error": "Something went wrong"}, "sorting": {"title": "Sorting", "description": "Categories Sorting", "message": {"success": "Success", "error": "Something went wrong"}}}}, "preview_podcast_section": {"title": "Podcast Preview ({{duration}})", "description": "Podcast time (Hour:Minute:Second)"}, "assign_content_card": {"alert": {"round_expired": "The round has expired. Please choose a round."}, "status": {"draft": "Draft", "pending_approval": "Pending Approval", "approved": "Approved"}, "detail": {"round_date": "Started date", "round_expired": "Study until", "user_count": "Members", "enroll_type": "Enrollment Type", "course_count": "Courses", "updated_at": "Last updated"}, "action_button": {"view_assign_detail": "View registration details", "cancel_enrollment": "Cancel enrollment"}}, "assign_content_to_member": {"create_assign_content_button": "Assign Course to My Team", "status": {"draft": "Draft", "pending": "Pending", "in_progress": "In Progress", "passed": "Passed", "error": "Error", "cancel": "Canceled", "rejected": "Rejected", "applied": "Applied"}, "list": {"total_result": "Result {0} Items", "filter": {"content_name": "Content Name", "status": "Status"}, "sort": {"date_desc": "Assign Date (Newest)", "date_asc": "Assign Date (Oldest)", "name_desc": "Course Name (Z-A)", "name_asc": "Course Name (A-Z)"}}}, "home": {"latest_my_learning": "Recently My Learning", "latest_published_courses": "Newest Courses", "course_categories": "Categories", "latest_knowledge_content": "Newest Knowledge Contents"}, "pre_assign_content": {"button": {"delete_assign_content": "Delete registration", "edit_assign_content": "Edit registration", "manage_assign_content": "Manage registration", "cancel_assign_content": "Cancel registration", "re_assign_content": "Registration again"}, "toast": {"delete_assign_content_success": "Deleted Successfully", "save_assign_content_success": "Saved Successfully", "save_draft_assign_content_success": "Saved Draft Successfully", "cancel_assign_content_success": "Cancel Successfully"}, "label": {"title_job_transaction_error_msg": "Reason", "title_assign_content_detail": "Course registration details", "title_enrollment_summary": "Registration information", "label_enrollment_summary_passed": "Passed", "label_enrollment_summary_failed": "Failed", "table_assign_content_title": "Member information that registers", "modal_cancel_assign_content_title": "Do you want to cancel the registration verification?", "modal_cancel_assign_content_description": "Once performed, it cannot be reversed.", "modal_delete_assign_content_title": "Do you want to delete your registration?", "modal_delete_assign_content_description": "Once performed, it cannot be reversed.", "modal_cancel_assign_content_invalid_title": "Unable to cancel verification.", "modal_cancel_assign_content_invalid_description": "Because the registration status has changed from “Pending” to “In-progress", "assign_content_passed_amount": "The list of members who have registered through has {0} item", "please_click_re_assign_button": "Please click the button “Registration again”", "start_time": "(Time 13:00)", "end_time": "(Time 23:59)"}, "modal": {"discard_assign_content_title": "Do you want to discard?", "discard_assign_content_description": "Once discarded, it cannot be retrieved", "confirm_cancel_enrollment_title": "Confirm cancellation of enrollment", "confirm_cancel_enrollment_description": "Once confirmed, it cannot be edited."}}, "search_all": {"tab": {"course": "Course", "knowledge_content": "Knowledge Content", "instructor": "<PERSON><PERSON><PERSON><PERSON>", "announcement": "News and Announcement", "learning_path": "Learning Paths"}, "banner": {"title": "Search Result: ", "title_search_all": "Search All"}, "breadcrumb": {"home": "Home", "search_result": "Search Result"}, "label": {"course_not_found": "Course not found", "knowledge_content_not_found": "Knowledge Content not found", "instructor_not_found": "Instructor not found", "announcement_not_found": "News and Announcement not found", "filter_not_found": "Filter not found", "please_reset_filter": "Please select a different filter or clear the default filter", "filter": "Filter", "search_result_amount": "Total", "search_result_unit": "Items", "view_all": "View All", "course_not_found_title": "Course “{0}” was not found.", "course_not_found_description": "Please check your search terms or explore all content.", "course_not_found_button": "Explore all content", "knowledge_content_not_found_title": "Knowledge content “{0}” was not found.", "knowledge_content_not_found_description": "Please check your search terms or explore all knowledge content.", "knowledge_content_not_found_button": "Explore all knowledge content", "instructor_not_found_title": "Instructor “{0}” was not found.", "instructor_not_found_description": "Please check your search terms", "announcement_not_found_title": "Announcement “{0}” was not found.", "announcement_not_found_description": "Please check your search terms or explore all announcement", "announcement_not_found_button": "Explore all announcement", "instructor_course_not_found_title": "Course of “{0}” was not found.", "instructor_course_not_found_description": "Please check your search words again.", "instructor_knowledge_content_not_found_title": "Knowledge content of “{0}” was not found.", "instructor_knowledge_content_not_found_description": "Please check your search words again.", "keyword_suggestion_title": "Suggestion keyword", "learning_path_not_found_title": "Learning path “{0}” was not found", "learning_path_not_found_title_without_filter": "Learning path was not found", "learning_path_not_found_description": "Please check your search terms"}, "place_holder": {"enter_keyword": "Enter keyword"}}, "customer_partner": {"button": {"add_customer_partner": "Add Customer Partner", "edit_customer_partner": "Edit", "delete_customer_partner": "Delete"}, "toast": {"delete_customer_partner_success": "Delete successfully", "delete_customer_partner_failed": "Something went wrong, Can't delete customer partner", "create_customer_partner_success": "Create successfully", "create_customer_partner_failed": "Something went wrong, Can't create customer partner", "update_customer_partner_success": "Update successfully", "update_customer_partner_failed": "Something went wrong, Can't update customer partner"}, "drawer": {"create_customer_partner_title": "Create: Customer Partner", "edit_customer_partner_title": "Edit: Customer Partner"}, "label": {"customer_partner_info": "Customer Partner Information", "customer_partner_name": "Name", "customer_partner_code": "Code", "status": "Status", "manage": "Manage"}, "form": {"name": "Name", "name_placeholder": "Enter Name", "code": "Code", "code_placeholder": "Enter Code", "enabled": "Enabled", "duplicate_code": "Duplicate Code", "enter_name": "Please enter name", "enter_code": "Please enter code", "enter_code_format_correct": "Only upper characters (A-Z), number (0-9), and underscore (_) are allowed", "invalid_code_length": "Code should be no longer than {0} characters", "invalid_name_length": "Name should be no longer than {0} characters", "invalid_start_with_space": "Name can't start with space", "invalid_end_with_space": "Name can't end with space", "invalid_consecutive_space": "Name can't have consecutive space"}, "modal": {"delete_customer_partner_confirm_title": "Do you want to delete the customer partner", "delete_customer_partner_confirm_description": "Do you confirm that you will delete customer partner “{0}”? <br />Once deleted, it cannot be brought back.", "discard_customer_partner_title": "Do you want to discard?", "discard_customer_partner_description": "Once discarded, it cannot be retrieved."}}, "purchase_order": {"button": {"add_purchase_order": "Add Purchase Order", "edit_purchase_order": "Edit", "delete_purchase_order": "Delete"}, "toast": {"delete_purchase_order_success": "Delete successfully", "delete_purchase_order_failed": "Something went wrong, Can't delete purchase order", "create_purchase_order_success": "Create successfully", "create_purchase_order_failed": "Something went wrong, Can't create purchase order", "update_purchase_order_success": "Update successfully", "update_purchase_order_failed": "Something went wrong, Can't update purchase order", "error_forbidden": "Forbidden error", "error_duplicated": "Duplicated error"}, "drawer": {"create_purchase_order_title": "Create: Purchase Order", "edit_purchase_order_title": "Edit: Purchase Order"}, "tab": {"general": "General", "reserved": "Reserved List"}, "label": {"purchase_order_info": "Purchase Order Information", "purchase_order": "Purchase Order", "purchase_order_id": "Order Id", "purchase_order_item_id": "Item order", "purchase_order_started_at": "Started At", "purchase_order_expired_at": "Expired At", "purchase_order_credit": "Credit", "purchase_order_price": "Price (Exclude VAT)", "purchase_order_note": "Note", "round_date": "Round Date", "total_credit": "Total Credit", "pre_enrollment_reservation_list": "Total Reservation {0} Credit", "edit_minimum_credit_sub_title": "The minimum number of credits that can be edited is {0} credits, as credits are already reserved for the training session"}, "tooltip": {"purchase_order_started_at": "If credits are reserved for the start date, they can be changed no later than the start of the round", "purchase_order_expired_at": "If a credit is reserved, the expiration date can be changed no later than the start of the round", "purchase_order_price": "If it is free credit, enter the price as 0 baht"}, "placeholder": {"purchase_order_id": "Enter order id", "purchase_order_item_id": "Enter item order", "purchase_order_started_at": "Select start date", "purchase_order_expired_at": "Select expiration date", "purchase_order_credit": "Enter credit", "purchase_order_price": "Enter price"}, "form": {"invalid_start_date": "Please check the date is correct", "invalid_pattern_order_id": "Invalid format Please enter a new quotation number", "duplicate_order_id": "The quotation number is the same as in the system. Please enter a new quotation number", "enter_purchase_order_id": "Please enter purchase order", "invalid_format_purchase_order_id": "Please fill in all 9 digits", "invalid_format_number_only": "Please enter numbers only", "enter_started_at": "Please enter the start date", "enter_expired_at": "Please enter the expiration date", "enter_credit": "Please enter the amount of credit", "invalid_amount_credit": "Please enter a credit amount greater than 0", "enter_price": "Please enter price", "invalid_pattern_price": "Please enter the price in the correct format and not more than 100 million, such as 10.50", "invalid_minimum_credit": "Please enter the minimum amount of {0} credits"}, "modal": {"delete_purchase_order_confirm_title": "Do you want to delete the purchase order", "delete_purchase_order_confirm_description": "Do you confirm that you will delete purchase order “{0}”? <br />Once deleted, it cannot be brought back.", "discard_purchase_order_title": "Do you want to discard?", "discard_purchase_order_description": "Once discarded, it cannot be retrieved"}}, "regular_enrollment": {"title": {"bulk_regular_course": "Bulk Enrollment Regular Course", "select_type": "Select bulk type", "bulk_enrollment": "Bulk Enrollment"}, "tooltip": {"please_upload_file_before_import": "Please upload the file before import data"}, "label": {"with_round": "Round", "without_round": "Immediate", "select_round": "Select start round date", "please_select_round": "Please select start round date", "unable_import_file": "Unable to import data. Please try again"}, "placeholder": {"select_round": "Please select start round date"}, "button": {"next": "Next", "import": "Import"}}, "training_enrollment": {"title": {"bulk_training_course": "ลงทะเบียนหลักสูตรเก็บชั่วโมงอบรม"}, "label": {"normal_round": "ลงทะเบียนตามรอบปกติ", "after_round": "ลงทะเบียนหลังเริ่มรอบ"}}, "bulk_upload": {"label": {"upload_file": "Upload File", "please_upload_file": "Please upload file"}, "tooltip": {"please_complete_all_before_import": "Please complete all operations before importing data", "please_upload_before_import": "Please upload the file before pressing import data.", "please_select_information_to_edit": "Please select information to edit"}, "button": {"import": "Import", "download_example": "Download Example", "view_info": "View", "user_profile": "User Profile", "username": "Username", "next": "Next"}, "placeholder": {"select_xlsx_file": "Select or drag the .xlsx file to upload", "limit_record_per_file": "Can be imported no more than {0} items/file"}}, "user_access_log": {"title": "Login Logs", "notification": {"open": {"message": "Start downloading log", "description": "One moment"}, "success": {"message": "Successfully downloaded log", "description": "This may take a while"}, "error": {"message": "There was an error downloading the log", "description": "Please, try again"}}, "device_type": {"all": "All", "desktop": "Desktop", "mobile": "Mobile", "tablet": "Tablet"}, "status": {"all": "All", "success": "Success", "failed": "Failed"}, "loginMethod": {"all": "All"}, "note_message": {"password_not_match": "Password not match", "unauthorized_user_was_suspended": "Unauthorized, Use<PERSON> was suspended", "user_is_inactive": "The account has been inactive."}}, "promote_notification": {"toast": {"create_error": "An error occurred. Unable to create a promote notification", "create_success": "Successfully create a promote notification", "update_error": "An error occurred. Unable to edit a promote notification", "update_success": "Successfully edit a promote notification"}}, "curriculum": {"classroom": {"drawer": {"create_classroom": "Create Classroom", "edit_classroom": "Edit: Classroom"}, "card": {"edit_classroom": "Edit: {0}", "status": "Status", "basic_information": "Basic Information", "classroom_information": "Classroom Information"}, "label": {"name": "Name", "enter_name": "Enter name", "please_enter_name": "Please enter name", "description": "Description", "enter_description": "Enter description", "content": "Content", "classroom_id": "Classroom ID"}}, "attachment": {"have_files": "have attachment {0} files"}}, "classroom": {"title": {"classroom_round_info": "Round Information", "classroom_enroll_info": "Enrollment Information", "classroom_round_location_info": "Round Location Information", "course_info": "Course Information", "edit_transfer_classroom": "Edit: Attendance List", "user_course_classroom": "Users in Course “{0}”", "attendance_list": "Attendance List"}, "tab": {"detail_tab": "General <PERSON>ail", "attendee_tab": "Attendee List"}, "label": {"round_date": "Round Date", "classroom_type": "Type", "location_code": "Location Code", "location_info": "Information about", "instructor": "<PERSON><PERSON><PERSON><PERSON>", "attendee_amount": "Attendance Amount", "waiting_list_amount": "Wait List Amount", "status": "Status", "summary_attendance_amount": "Attendance Amount", "summary_waiting_pre_enroll_amount": "Waiting Amount"}, "toast": {"not_found": "Classroom not found", "update_success": "Update successfully", "update_error": "Something went wrong, Can not updated"}, "notification": {"open": {"message": "Start downloading attendance list", "description": "please wait a moment"}, "success": {"message": "Download attendance list successfully", "description": "Please check the downloaded file."}, "error": {"message": "Unsuccessful download attendance list", "description": "Please download the file again."}}, "button": {"mark_result": "<PERSON>", "edit_mark_result": "<PERSON> <PERSON>", "download": "Download", "edit": "Edit"}, "tooltip": {"mark_result_not_available": "Unable to mark results"}, "modal": {"confirm_transfer_attendee": "Confirm to attendance list"}}, "contact_setting": {"title": "Contact Configuration", "updatedAt": "Date and time of data modification", "updateBy": "By", "tab": {"desktop": "Desktop", "tablet": "Tablet", "mobile": "Mobile"}, "footer_info": {"title_setting": "Footer contact information of website", "title": "Contact for more information", "download_document": "Download document", "drawer": {"title": "Edit: Footer contact information of website", "info1": "Contact information 1", "info2": "Contact information 2", "user_manual": "Upload user manual", "faq": "Upload FAQ"}}, "contact_info": {"title_setting": "Contact information notification", "title": "Are you having trouble verifying your identity? <br>Please contact an agent.", "drawer": {"title": "Edit: Contact information notification", "info": "Contact details"}}, "confirm_email": {"title": "Save theme contact configs"}, "organization_contact": {"user_manual": {"upload_description": "Supported file only {0} and limit size 50MB"}, "faq": {"upload_description": "Supported file only {0} and limit size 50MB"}}}, "terms_and_conditions": {"title": "Terms and Conditions", "updatedAt": "Updated At", "updateBy": "By", "enable": "enable", "disable": "disable", "tab": {"desktop": "Desktop", "tablet": "Tablet", "mobile": "Mobile"}, "modal": {"confirm_email": {"title": "Save terms and conditions configs"}}, "terms": {"title": "Terms", "enable_terms": "Enable Terms", "drawer": {"title": "Edit: Terms"}, "label": {"isEnabled": "Activate Terms", "title": "Title of Terms", "sub_title": "Subtitle of Terms", "content": "Detail"}, "message": {"warning": {"title": "Please enter the title", "sub_title": "Please enter the subtitle", "content": "Please enter the detail"}}, "placeholder": {"title": "Enter the title", "sub_title": "Enter the subtitle", "content": "Enter the detail"}}, "privacy": {"title": "Privacy Policy", "enable_terms": "Enable Privacy Policy", "drawer": {"title": "Edit: Privacy Policy"}, "label": {"isEnabled": "Activate Privacy Policy", "title": "Title of Privacy Policy", "sub_title": "Subtitle of Privacy Policy", "content": "Detail"}, "message": {"warning": {"title": "Please enter the title", "sub_title": "Please enter the subtitle", "content": "Please enter the detail"}}, "placeholder": {"title": "Enter the title", "sub_title": "Enter the subtitle", "content": "Enter the detail"}}}, "toast": {"certificate": {"error": {"no_certificate": "Unable to enable certificate, Please fill out and save the certificate information before enabling it.", "invalid_certificate": "Unable to save data. Please contact the administrator."}}}}, "expired_at": "End date", "page_title.waiting_to_enroll": "Waiting to Enroll", "reset": "Reset", "preview_video_section": {"title": "Video Preview ({{duration}})", "description": "Video display time (Hour:Minute:Second)"}, "d": {"loading": "Loading", "undefined": "Undefined", "gender": {"male": "Male", "female": "Female", "unspecific": "Unspecific"}, "boolean": {"yes": "Yes", "no": "No"}, "value": {"all": "All"}, "article": {"enabled_error_required_duration": "Can't enabled this content, Because the time period for which articles cannot be skipped.", "name": "Article name", "required_name": "Please enter the title of the article.", "status": "Status", "description": "description", "duration": "Time period for which articles cannot be skipped (seconds)", "duration_invalid": "Please enter valid duration.", "required_duration": "Please enter the time period for which articles cannot be skipped.", "out_of_range_duration": "Please fill in the length of time that the article cannot be skipped not less than 1 seconds and not more than 86400 seconds."}, "course": {"objective_type": {"all": "Learning Objectives", "regular": "Regular", "training": "Training"}, "regulator": {"regular": "Regular", "tsi": "TSI", "oic": "OIC"}, "criteria": {"percentage_learning_progress": "Percentage learning progress {{percentage}}% ({{courseItem}}/{{totalCourseItem}})", "percentage_time_spent_course": "Percentage time spent course {0}% (01:12/01:30 Hr.)"}, "content_provider": {"self": "Self", "skilllane_credit": "SkillLane Credit", "skilllane_subscription": "SkillLane Subscription"}}, "label": {"name": "Name {0}", "description": "Description", "course": {"course_item_video": "Video", "course_item_article": "Article", "course_item_survey": "Survey", "course_item_quiz": "Quiz", "course_item_classroom": "Classrooms"}, "quiz": {"quiz": "Quiz", "quiz_error_required": "Please enter the quiz.", "answer": "Answer", "answer_error_required": "Please enter the answer.", "description": "Description", "enabled": "Enabled", "enabled_retest": "Enabled repeat testing", "question": "Question", "question_error_required": "Please enter the question.", "status": "Status", "type": "Type", "type_error_required": "Please select the type of the quiz.", "type_regular": "Regular", "type_pre_test": "Pre-Test", "type_post_test": "Post-Test"}, "course_item_criteria_config": {"quiz": {"name": "Quiz", "pass_score": "Pass score", "max_score": "Max score", "is_enabled": "Enabled"}, "classroom": {"name": "Classroom", "attend_score": "Attend score", "home_work_score": "Homework score", "is_enabled": "Enabled"}}, "survey": {"name": "Survey name", "name_placholder": "Please enter survey name.", "name_error_required": "Please enter survey name.", "description": "Description.", "description_placholder": "Please enter description.", "description_error_required": "Please enter description."}, "video": {"transcoding_line1": "Video Transcoding...", "transcoding_line2": "When Transcode success video will available"}, "podcast": {"transcoding_line1": "Audio Transcoding...", "transcoding_line2": "When Transcode success audio will available"}}, "learning": {"title": "Learnings", "full_name": "Full name", "email": "Email", "citizen_id": "Citizen ID", "courses": "Courses", "round_date": "Round date", "started_at": "Start date", "expired_at": "End date", "learning_progress": "Progress (%)", "customer_code": "Customers", "status": "Status", "column_settings": "Column settings", "reset_column": "Reset column", "in_progress": "In Progress", "total": "Total", "no_authen": "Not Authen", "fail_authen": "Failed <PERSON>then", "passed_authen": "Passed Authen", "passed": "Passed", "view_detail": "View detail", "more_filter": "More filters", "enrollments": "Enrollments", "status_learning": {"not_start": "Not Start", "in_progress": "In Progress", "pending_result": "Pending Result", "passed": "Passed", "completed": "Completed", "pending_approval": "Pending Approval", "expired": "Expired Enrollment", "canceled": "Canceled", "pre_enroll_canceled": "Canceled Waiting Enrollment"}, "enroll_type": "Enrollment Type", "search_results": "Search results", "records": "records", "first_name": "First name", "last_name": "Last name", "remark": "Remark", "enter_remark": "Enter remark", "summary_confirm": "You need", "reset_progress": "Reset progress", "reset_learning_history": "Reset learning history", "reset_learning_progress": "Reset learning Progress", "reset_attendance_and_learning_history": "Reset attendance and learning history", "attendance_and_learning_history": "Attendance and learning history", "reset_learning_progress_count": "Reset Learning Progress {0} records", "total_reset_learning_progress_count": "from a total of {0} records", "confirm_reset_learning_progress": "Confirm learning reset", "select_all_learning_progress": "Select all learning progress", "regular_enrollments": "Regular enrollments", "oic_enrollments": "OIC enrollments", "regular.started_at": "Start date", "report": "Learning summary report"}, "approval": {"summary": {"total": "Total", "pending": "Pending Approval", "verified": "Verified", "approved": "Approved", "rejected": "Rejected"}, "full_name": "Full Name", "email": "Email", "citizen_id": "Citizen ID", "courses": "Courses", "requested_approval": "Submission Date", "approval_date": "Approval Date", "customer_code": "Customers", "status": "Status", "view_detail": "View detail", "column_settings": "Column settings", "started_at": "Start date", "expired_at": "End date", "status_approval": {"pending_approval": "Pending Approval", "verified": "Verified", "approved": "Approved", "rejected": "Rejected", "number_of_verify": " ({0} time)"}, "approve": "Approve", "view_certificate": "View Certificate", "issue_certificate": "ออกประกาศนียบัตร", "issue_certificate_and_send_email": "ออกใบประกาศนียบัตรและส่งอีเมล", "certificate_name": "Certificate {0}", "back_to_verify": "Back to Verify", "report": {"title": "รายงานผลการอบรม", "user_info": {"title": "ข้อมูลประจำตัว", "status": "สถานะ", "full_name": "ชื่อ - สกุล", "email": "อีเมล", "citizen_id": "หมายเลขบัตรประชาชน", "license_number": "เลขที่ใบอนุญาต"}, "enrollment_info": {"title": "ข้อมูลการเรียน", "course": "หลักสูตร", "start_date": "วันที่ลงเรียน", "pass_date": "วันที่เรียนจบ", "request_approval_date": "วันที่ส่งผลการอบรม"}, "learning_history": {"title": "ประวัติการเข้าอบรม", "all": "ทั้งหมด", "records": "รายการ", "passed": "ผ่าน", "failed": "ไม่ผ่าน"}, "activity_history": {"title": "ประวัติระหว่างอบรม", "all": "ทั้งหมด", "records": "รายการ", "passed": "ผ่าน", "failed": "ไม่ผ่าน", "face_verification": "ยืนยันตัวตน", "idle_verification": "ความสนใจ"}}}, "pre_enrollment": {"id": "Pre-Enroll ID", "eid": "EID", "full_name": "Full Name", "email": "Email", "mobile_phone": "Phone No", "citizen_id": "Citizen ID", "business_type": "Business Types", "customer_code": "Customers", "licenses": "Licenses", "tsi_license_no": "TSI License No", "tsi_license_type": "TSI License Type", "tsi_start_date": "TSI Start Date", "tsi_end_date": "TSI End Date", "oic_license_non_life_no": "Non-Life Insurance License No", "oic_non_life_start_date": "OIC Start Date NoN Life", "oic_non_life_end_date": "OIC End Date Non Life", "oic_license_life_no": "Life Insurance License No", "oic_life_start_date": "OIC Start Date Life", "oic_life_end_date": "OIC End Date Life", "courses": "Courses", "course_group": "Courses", "course_code": "Course Code", "productSKU_id": "Product SKU ID", "productSKU_code": "Product SKU Code", "user_status": "User status", "regulator": "Regulators", "is_check_regulator": "OIC Validation", "training_center": "Training Centers", "round_date": "Round date", "status": "Status", "more_filter": "More filters", "view_detail": "View detail", "status_waiting": {"pass": "Waiting to Enroll", "pre_enrollment": "Waiting to Start", "pre_enrollment_rejected": "Rejected", "waiting_verify": "Pending Edit", "edited_after_verify": "Edited and verified", "applied": "Success enrolled", "rejected": "Canceled", "failed_to_apply": "Failed", "customer_partner_code": "Customer Partner Code"}, "enroll_type": "Enrollment Type", "value.not_verified": "Not Verified", "value.verified": "Verified"}, "enrollment": {"started_at": "Started at", "expired_at": "Expired at", "finished_at": "Finished at", "enroll_type": {"label": "Enroll type:", "compulsory": "Compulsory course", "voluntary": "Voluntary course"}, "enroll_type_label": {"compulsory": "Compulsory", "voluntary": "Voluntary"}}, "document": {"status": {"pending": "Pending", "in_review": "In Review", "approve": "Approved", "reject": "Rejected", "canceled": "Canceled"}}, "additional_doc": {"doc_id": "Document ID", "full_name": "Full Name", "email": "Email", "citizen_id": "Citizen ID", "courses": "Courses", "request_date": "Request Date", "due_date": "Submission Date", "approval_date": "Approval Date", "customers": "Customers", "status": "Status"}, "deduct_doc": {"doc_id": "Document ID", "full_name": "Full Name", "email": "Email", "citizen_id": "Citizen ID", "courses": "Courses", "submission_date": "Submission Date", "approval_date": "Approval Date", "started_at": "Start Date", "licenses": "Licenses", "licenses_type": "Licenses Type", "customer_code": "Customers", "status": "Status", "total": "Total", "oic_license_non_life_no": "Non-Life Insurance License No", "oic_license_life_no": "Life Insurance License No"}, "compare": {"more_than": "more than", "less_than": "less than"}, "duration": {"hours": "hours", "minutes": "minutes", "seconds": "seconds"}, "si_number": {"kilo": "K", "million": "M", "billion": "B"}, "discussion_board": {"title": "All Discussions", "course_detail": "Content Detail", "view_course": "View Content", "view_course_edit": "View and Edit Content", "all": "All", "resolved": "Resolved", "unresolved": "Unresolved", "change_to_resolved": "Change to resolved", "change_to_unresolved": "Change to unresolved", "status": "Status", "content": "Content", "course_name": "Course name", "objective_type": "Objective Type", "regulator": "Regulator", "latest_activity_date": "Last Activity Date", "hide": "<PERSON>de", "show": "Show", "reply": "Reply", "replies": "Replies", "content_placeholder": "Reply/Answer", "content_required": "Please enter Reply/Answer", "see_more": "See more", "view": "View", "create_discussion": "Discussion board", "comment_not_found": "Comment not found", "comment_placeholder": "Leave comments", "cannot_get_discussion": "Unable to load discussion", "cannot_create_discussion": "Unable to add discussion", "cannot_reply_discussion": "Unable to add answer", "feedback": {"resolved_success": "Resolved Success", "resolved_error": "Unable to resolved", "unresolved_success": "Unsolved Success", "unresolved_error": "Unable to unresolved"}}, "curriculum": {"course_item": {"article": "Article", "current_time": "Current time", "quiz_post_test": "Quiz (Post-Class Quiz)", "quiz_pre_test": "Quiz (Pre-Class Quiz)", "quiz_regular": "Quiz (General Quiz)", "quiz_post_test_evaluated": "Quiz (Evaluated: Post-Class Quiz)", "quiz_pre_test_evaluated": "Quiz (Evaluated: Pre-Class Quiz)", "quiz_regular_evaluated": "Quiz (Evaluated: General Quiz)", "survey": "Survey", "timeSpent": "Time spent", "progress": "Progress", "do_not_skip": "Do not skip content", "classroom": "classroom", "classroom_evaluated": "Classroom (Evaluated)"}}, "document_detail": {"title": "Document Details", "tab": "Verification History", "infor_request": {"title": "Additional Information Requested", "reason_request": "Reason for the Request"}, "attached_document": {"title": "Attached Documents", "qualification": "Qualification Document for Use Instead while Waiting for the Issuance of Original Document"}, "learning_information": {"title": "Learning Information"}, "table_history": {"column": {"reason": "Reason"}}, "privacy": {"title": "Privacy", "banner": {"title": "Personal Data Protection Policy"}}, "no_permission": {"title": "No permission to access", "description_1": "We are sorry, but you are not permitted to look at this page.", "description_2": "To obtain access, contact your admin."}}, "modal_compare": {"attendance_information": "Attendance Information"}, "document_status": {"title": "Document Status", "title_deduct": "Deduct Document Status", "submit_date": "Submit date", "date": "Date"}, "confirm_modal": {"content_modal": "By confirming, your request will be canceled and you will not be able to make any further changes.", "additional_document": {"form_modal": {"not_sent": {"title": "Request Cancellation Confirmation", "label": "Reason for Cancellation", "placeholder": "For example, incorrect requested documents.", "validate": "Please enter a reason for cancellation."}, "reject": {"title": "Reject Document Confirmation", "label": "Reason for Reject Document", "placeholder": "For example, incorrect requested documents.", "validate": "Please enter a reason for rejection."}, "approval": {"title": "Approval Document Confirmation", "label": "Reason for Approval", "placeholder": "For example, \"Document is complete and accurate.\""}, "default": {"validate": "Please enter a reason "}}}, "deduct_document": {"form_modal": {"reject": {"title": "Decline Approval Confirmation", "label": "Reason for Document Decline Approval (Visible to Learner<PERSON>)", "placeholder": "For example, \"Documents do not meet the selected criteria.\"", "validate": "Please enter a reason for rejection."}, "approval": {"title": "Approval Document Confirmation", "label": "Reason for Document Approval (Visible to Learner<PERSON>)", "placeholder": "For example, \"Documents are complete.\"", "validate": "Please enter a reason for approval."}}}, "message": {"success": "Success"}, "btn": {"reject": "Reject", "view_document": "View Original Document"}}, "deduct": {"entitlement": {"title": "Training Hour Deduction Entitlement", "title_user_info": "User Information", "reduce_hours": "Reduce Hours", "issuing_authority": "Issuing Authority", "effective_date": "Effective Date (B.E.)", "expiration_date": "Expiration Date (B.E.)", "lecturing_experience": "Lecturing Experience within 5 Years Prior to License Expiration", "end_date": "End Date", "start_date": "Start Date", "course_reference": "Course Reference (ต4/น4)", "program": "Program", "number_of_hours": "Number of Hours", "full_name": "Full Name", "complete": "Completion of Master's Degree or Higher Education from Domestic or International Institutions", "domestic_institution": "Domestic Institution", "foreign_institution": "Foreign Institution", "study_field": "Study Field", "completion_date": "Completion Date (B.E.)", "certifi": {"title": "No. 7 (1) สอบผ่านคุณวุฒิที่ใช้ในการประกอบวิชาชีพประกันภัย", "FChFP": "FChFP : (Fellows Chartered Financial Practitioner) Conversion Course", "CFP": "CFP : (Certified Financial Planner)", "AFPT": "AFPT : (Associate Financial Planner Thai)", "DNLI": "Diploma in Non-Life Insurance", "CII": "Cert. CII : (The Chartered Insurance Institute)"}, "educate_master_degree": {"title": "No. 7 (2) Completion of Master's Degree or Higher Education from Domestic or International Institutions"}, "instructor": {"title": "No. 7 (3) เป็นหรือเคยเป็นวิทยากร ผู้บรรยายความรู้ หรือเป็นอาจารย์ประจำ หรืออาจารย์พิเศษใน สถาบัน สมาคม หรือองค์กร ในหลักสูตรที่สำนักงานคปภ. ให้ความเห็นชอบภายในระยะเวลา 5 ปี ก่อนใบอนุญาตสิ้นอายุ"}}}, "activity_log": {"column": {"module": "<PERSON><PERSON><PERSON>", "event_name": "Event Name", "event_description": "Event Description", "full_name": "Full name", "permission_group": "Permission Group", "date": "Date", "manage": {"title": "Manage", "detail": "Detail"}}, "filter": {"module": "Module:", "event_name": "Event Name:", "full_name": "Full name", "date": "Date", "from_date": "From Date", "to_date": "To Date", "search": "Search"}, "module": {"user_management": "User Management", "user_management_old": "User Management (Old)", "content_management": "Content Management"}}, "knowledge_content": {"type": {"all": "All", "article": "Article", "video": "Video", "ebook": "Ebook", "podcast": "Podcast"}}, "knowledge_content_item": {"code": "Code", "thumbnail": "<PERSON><PERSON><PERSON><PERSON>", "title": "Title", "type": "Type", "category_name": "Knowledge hub category", "instructor": "<PERSON><PERSON><PERSON><PERSON>", "status": "Status", "publishedStartAt": "Publication date", "publishedEndAt": "Expiration date", "updatedAt": "Edit date", "isDownloadEnabled": "Download enabled"}, "user_group": {"name": "Name", "isEnabled": "Status", "totalUsers": "Total Users", "updatedAt": "Updated Date", "enable": "Enable", "disable": "Disable", "all": "All", "action": "Action", "user_full_name": "First name/Last name", "course": "course", "condition": {"name": "Information", "operator": "Condition", "value": "Condition value", "number": "Number", "date_suffix": {"day": "Day", "month": "Month", "year": "Year"}}, "condition_operator": {"eq": "Equal to", "lt": "Less than", "gt": "More than", "lt_range": "Younger than equal to", "gt_range": "Older than equal to", "in_eq": "All must match", "in_partial": "One must match", "in_enrollment_eq": "Passing all studies must match", "in_enrollment_partial": "Passing one of the studies must match", "not_in_enrollment_eq": "All classes not passed must match", "not_in_enrollment_partial": "Failure to pass any of the studies must match", "passed_duration_gte_range": "Having studied more than equal to", "passed_duration_lte_range": "Having studied less than equal to"}}, "my_team": {"enrollment_detail": {"learning_detail": {"title": "Learning Detail"}, "learning_progress": {"title": "Learning Progress"}}, "report": {"name": "Name"}}, "learning_path": {"detail": {"general": {"tab": {"overview": {"code": "Learning path code", "name": "Learning path name", "description": "Learning path description", "thumbnail": {"label": "Cover", "size": "Size: {0}"}}, "learning": {"is_sequence_enabled": "Study in order of learning content", "is_enabled": "Enabled", "is_disabled": "Disabled"}, "access": {"access_type": "Access  user group", "type": {"public": "Public learning path", "private": "Private learning path", "private_with_user_group": "Learning path specific user group"}, "user_group": {"name": "User group name", "total_member": "Total member", "status": "Status", "action": "Action", "remove": "Remove"}}, "enrollment": {"self_enroll": {"is_self_enroll": "Learner can self enroll", "enabled": "Active", "disabled": "Inactive"}, "enroll_type": {"label": "Enroll type", "pre_enroll": "Schedule", "immediate": "Immediate"}, "round": {"title": "Learning path round date", "add_round_button": "Bulk Rounds", "status": {"label": "Status", "all": "All", "active": "Active", "inactive": "Inactive"}, "first_registration_date": "First Registration Date", "last_registration_date": "Last Registration Date", "expired_date": "End learning path date", "no_expired_date": "No expiry date", "total": "Total {0} records"}}}}}, "tag": {"enable": "Enable", "disabled": "Disable"}}, "permission-groups": {"trainee": "Trainee"}, "promote_notification": {"content_type": {"course": "Course", "learning_path": "Learning Path", "knowledge_content": "Knowledge Content", "announcement": "Announcement"}}, "user_access_log": {"column": {"login_date": "Timestamp of Login", "username": "Username", "name": "Full Name", "ip_address": "IP Address", "user_agent": "User Agent", "device_type": "Device Type", "browser": "Browser Type", "location": "Location", "login_method": "Login Method", "status": "Login Status", "note": "Note on Failure"}, "filter": {"search": "Username / Full Name", "date": "Timestamp of Login", "ip_address": "IP Address", "device_type": "Device Type", "browser": "Browser Type", "location": "Location", "login_method": "Login Method", "status": "Status"}}, "user_validation": {"pre_enrollment_title": "Bulk user validation (ลงทะเบียนตามรอบปกติ)", "enrollment_title": "Bulk user validation (ลงทะเบียนหลังเริ่มรอบ)", "form": {"label": {"round_date": "Select round date", "customer": "Select company", "enrollment_type": "Enrollment type", "file_upload": "Upload file"}, "placeholder": {"round_date": "Select the started round date", "customer": "Select company", "customerPartner": "Choose partner company", "enrollment_type": "Choose enrollment type"}, "message": {"round_date": "Please select the started round date", "customer": "Please select company", "customer_partner": "Please select partner company", "enrollment_type": "Please select enrollment type", "file_upload": "Please upload file", "upload_row_limit": "Import data up to 10,000 items/file."}, "warning": {"title": "Insufficient credit detected", "total_credit_reserve": "Total number of credits required {0} credit", "total_credit_remain": "Number of available credits {0} credit", "total_credit_require": "Required number of credit {0} เครดิต"}, "error": {"fail_upload": "Unable to import data, Please try again after editing the file"}}, "button": {"submit_file": "Submit", "template_download": "Download sample file"}, "tooltip": {"require_file_upload": "Please upload the file before submitting", "require_customer": "Please select company before downloading the sample file.", "require_enrollment_type": "Please select enrollment type."}}, "classroom": {"enrollment_status": "Status", "round_date": "Round Date", "location": "Location", "total_attended": "Total Attended", "score_homework": "Score Homework", "remark": "Remark"}, "classroom_location_enrollment": {"status": {"not_register": "Not Register", "pre_enroll": "Pre-Enrollment", "waiting_pre_enroll": "Waiting Pre-Enrollment", "in_progress": "In-Progress", "pending_result": "Pending Result", "passed": "Passed", "not_pass": "Not Pass", "canceled": "Canceled"}}, "course_item": {"type": {"video": "Video", "article": "Article", "quiz": "Quiz", "survey": "Survey", "classroom": "Classroom"}}, "badge": {"detail": {"title": "Badge: {0}", "card": {"general": {"title": "General information", "name": "Badge Name", "description": "Description", "image": "Badge Image"}}, "button": {"delete": "Delete Badge"}}}, "enrollment_progress_history": {"criteria": "Criteria", "no_criteria": "No Criteria", "total_attended": "Total Attendance", "score_homework": "Homework Score", "quiz_description": "{{courseItemName}} ({{criteria}}, Score: {{userPoint}}/{{totalPoint}} ({{scorePercents}}%), Time Spent: {{timeSpent}} seconds)", "video_description": "Video: {{courseItemName}} (Progress: {{percentage}}%)", "article_description": "Article: {{courseItemName}}", "article_description_progress": "Article: {{courseItemName}} (Progress: {{percentage}}%)", "survey_description": "Survey: {{courseItemName}}", "classroom_description": "Classroom: {{courseItemName}} ({{eventText}}: {{classroomLocationType}}, {{classroomLocationCode}})", "classroom_mark_result_description": "Classroom: {{courseItemName}} ({{eventText}}: {{classroomLocationType}}, {{classroomLocationCode}}, {{criteriaText}}", "reset_face_activity_detect": "Reset face and course item history", "reset_face": "Reset face history", "reset_activity_detect": "Reset course item history", "reset_course_item": "Course Item: {{resetTotal}} / {{courseItemTotal}}", "start_course_item": "Start Learning", "in_progress_course_item": "In Progress", "completed_course_item": "Completed", "not_pass_course_item": "Not Passed", "revisit_course_item": "Review Lesson", "register_classroom": "Register for Classroom", "cancel_classroom": "Cancel Classroom Registration", "start_learning": "Enter Learning Page", "leave_learning": "Leave Learning Page", "identity_verification": "Identity Verification Before Learning", "identity_verification_description": "Identity verification similarity score: {{percentSimilarity}}%", "attention_idle": "Attention Verification", "attention_face": "Face Verification", "reset_enrollment_progress": "Reset Enrollment Information", "learning": "Learning", "mark_result": "<PERSON>", "register_classroom_self": "Self Classroom Registration", "register_classroom_admin": "Admin Classroom Registration", "register_classroom_system": "Automatic Classroom Registration", "cancel_classroom_self": "Self Classroom Cancellation", "cancel_classroom_admin": "Admin Classroom Cancellation"}}, "course_catalog": {"all_content_name": "All {0} content", "search_result": {"breadcrumb": {"homepage": "Home", "search_result": "Result"}, "title": {"result": "Result"}}, "banner": {"title": "Course Catalog", "description": "Discover our contents that will help develop the skills that you desire"}, "filter": "Filter", "input_search": "Search Course Name", "reset": "Reset", "submit": "Submit", "objective_types": {"title": "Learning Objectives", "oic": "OIC", "regular": "REGULAR", "tsi": "TSI", "learning_path": "Learning Path"}, "regulator": {"regular": "Training", "tsi": "TSI", "oic": "OIC"}, "course_properties": {"title": "Properties", "certificate": "Certificate", "quiz": "Quiz", "article": "Article"}, "course_card_list": {"result_amount": "Result", "result_unit": "items.", "certificate": "Certificate", "loading": "Loading...", "no_content": "Content not found", "number_of_course": "{0} course", "filter_not_match": {"reset_button": "<PERSON><PERSON><PERSON>", "description": "Please select another filter or reset filter", "title": "Filter not match."}, "search_not_found": {"all_course_button": "Course Catalog", "description": "Please recheck your search keyword or discover the course catalog", "title": "Search not found"}}}, "department": {"sorting": "Sorting", "collapse": {"expand": "Expand All", "collapse": "Collapse All"}, "filter": {"name": "Department Name", "code": "Department Code", "moderator": "Moderator", "email": "Email", "telephoneNo": "Phone No."}, "column": {"name": "Department Name", "code": "Department Code", "order": "Order", "total_moderator": "Total Moderators", "total_user": "Total Users", "manage": "Manage"}, "moderator_column": {"name": "Full Name", "email": "Email", "mobile_phone_number": "Phone No.", "is_active": "Status", "view_detail": "Detail"}}, "toast.network.unstable_try_again": "Network unstable, please try again.", "toast.enrollment_detail.image_crop.success": "Image uploaded successfully", "toast.enrollment_detail.image_crop.file_invalid_format": "Invalid file format. Please upload a JPG or PNG file with a maximum size of 10 MB.", "toast.enrollment_detail.image_crop.invalid_format": "Invalid facial photo.", "toast.enrollment_detail.image_crop.detect_face": "Please upload your own facial photo without including others.", "toast": {"upload_image": {"success": "Uploaded image successfully", "progress": "Upload image in progress", "error": "Uploaded image error"}, "enrollment_detail": {"authen_info": {"success": "Image deleted successfully."}, "terms": {"title": "Terms", "banner": {"title": "Terms and Conditions", "description": "Online course training system for trainees"}}, "fail": "Something went wrong."}, "privacy": {"title": "Privacy", "banner": {"title": "Personal Data Protection Policy"}}}, "terms": {"title": "Terms", "banner": {"title": "Terms and Conditions", "description": "Online course training system for trainees"}}, "privacy": {"title": "Privacy", "banner": {"title": "Personal Data Protection Policy"}}, "no_permission": {"title": "No permission to access", "description_1": "We are sorry, but you are not permitted to look at this page.", "description_2": "To obtain access, contact your admin."}, "setting": {"jobs": {"title": "Bulk Activity History", "label": {"activity_date": "Activity Date", "activity_types": {"title": "Activity Types", "import_user": "Bulk import users", "edit_user": "Bulk edit user information", "change_username": "Bulk change username", "enrollment": "Bulk enrollment", "enrollment_bundle": "Bulk enrollment bundle", "assgin_plan_package_license": "Bulk assgin plan package license", "transfer_plan_package_license": "Bulk transfer plan package license", "change_citizenId": "Bulk change Citizen ID (Deprecated)", "change_email": "Bulk change email (Deprecated)", "enrollment_history": "Enrollment history", "edit_learning_path_expire_date": "Bulk edit learning path expire date", "pre_assign_learning_path_enrollment": "Bulk Pre-assign learning path enrollment", "assign_learning_path_enrollment": "Assign learning path enrollment", "classroom_mark_result": "classroom mark result", "cancel_pre_enrollment": "Cancel pre enrollment for a course", "cancel_pre_assign_learning_path": "Cancel pre assign for learning path", "update_course_category": "Update course category"}, "created_by": "Created By", "system": "System", "status": {"title": "Status", "list": {"all": "All", "success": "Success", "processing": "Processing", "fail": "Fail", "cancel": "Cancel"}}, "total_records": "Total Records", "round_status": "Round status", "action": "Action"}, "btn": {"view_detail": "View detail"}, "bulk_operation": {"title": "Manage Import Data", "edit_user_title": "Bulk edit users", "result": {"error": {"title": "Importing Failed", "description_1": "Importing completed {0} records and", "description_2": "failed {0} records", "description_3": "Please check them to import again."}, "cancel": {"title": "Cancel Success", "description_1": "Cancel importing completed {0} records", "label": {"cancel_enrollment_history_total": "Canceled enrollment history {0} records"}}, "detail": {"title_error": "Importing failed {0} records", "title_completed": "Importing success {0} records", "title_result": "Result", "title_result_error": "Result failed", "label": {"user_name": "username:", "email": "email:", "citizen_id": "citizen id", "old_username": "old username:", "new_username": "new username:", "course_code": "course code", "productsku_code": "productsku code", "ref_code": "ref code", "learning_path_code": "learning path code:", "user_sender": "user sender:", "user_receiver": "user receiver:"}}, "summary": {"plan": "plan", "success_assign_plan_package_license_to_new_user": "success assign plan package license to {0} new users", "success_assign_plan_package_license_to_old_user": "success assign plan package license to {0} old users", "error_assign_plan_package_license_to_new_user": "error assign plan package license to {0} new users", "error_assign_plan_package_license_to_old_user": "error assign plan package license to {0} old users", "success_assign_plan_package_license_new": "new license {0}", "success_assign_plan_package_license_old": "old license {0}", "success_transfer_plan_package_license_to_new_user": "success transfer plan package license to {0} new users", "success_transfer_plan_package_license_to_old_user": "success transfer plan package license to {0} old users", "success_transfer_plan_package_license": "old license {0}", "error_transfer_plan_package_license_to_new_user": "error transfer plan package license to {0} new users", "error_transfer_plan_package_license_to_old_user": "error transfer plan package license to {0} old users"}, "footer": {"import_date": "Created at: {0} ", "import_by": "By {0}", "by_system": "System"}}, "buttons": {"download": "Download"}}, "error": {"already_cancel_enrollment_history": "Already cancel enrollment history", "job_pending": "Job pending", "job_pending_with_user": "Job pending with user", "system_failed": "System error"}}, "permissions": {"admin-dashboard": {"module": "แดชบอร์ดผู้ดูแลระบบ", "read": "เข้าถึงแดชบอร์ดผู้ดูแลระบบ"}, "announcement": {"module": "ข่าวสารและการประกาศ", "read": "เข้าถึงข่าวสารและการประกาศ", "create": "สร้างข่าวสารและการประกาศ", "update": "แก้ไขข่าวสารและการประกาศ", "delete": "ลบข่าวสารและการประกาศ"}, "badge": {"module": "สัญลักษณ์รางวัล", "read": "ดูรายละเอียดสัญลักษณ์รางวัล", "create": "สร้างสัญลักษณ์รางวัล", "update": "แก้ไขสัญลักษณ์รางวัล", "delete": "ลบสัญลักษณ์รางวัล"}, "bundle": {"module": "กลุ่มหลักสูตร", "read": "เข้าถึงกลุ่มหลักสูตร [<PERSON><PERSON><PERSON><PERSON>]", "update": "แก้ไขกลุ่มหลักสูตร [<PERSON><PERSON><PERSON><PERSON>]"}, "certificate-thumbnail": {"module": "ใบประกาศนียบัตร", "read": "เข้าถึงภาพขนาดย่อของใบประกาศนียบัตร"}, "certificate": {"module": "ใบประกาศนียบัตร", "read": "เข้าถึงใบประกาศนียบัตร", "create": "สร้างใบประกาศนียบัตร", "update": "แก้ไขใบประกาศนียบัตร", "delete": "ลบใบประกาศนียบัตร"}, "comment-reply": {"module": "การแสดงความคิดเห็น", "create": "สร้างตอบกลับความคิดเห็น"}, "comment": {"module": "การแสดงความคิดเห็น", "read": "เข้าถึงแสดงความคิดเห็น", "create": "สร้างแสดงความคิดเห็น", "update": "แก้ไขแสดงความคิดเห็น"}, "course-category": {"module": "หลักสูตร", "read": "เข้าถึงหมวดวิชา", "create": "สร้างหมวดวิชา", "update": "แก้ไขหมวดวิชา", "delete": "ลบหมวดวิชา"}, "course-dropdown": {"module": "หลักสูตร", "read": "เข้าถึง Dropdown หลักสูตร"}, "course-instructor": {"module": "หลักสูตร", "update": "แก้ไขอาจารย์ประจำหลักสูตร"}, "course-learning": {"module": "หลักสูตร", "update": "แก้ไขหลักสูตรการเรียนรู้"}, "course-quiz-config": {"module": "หลักสูตร", "read": "เข้าถึงรายละเอียดเนื้อหา - เงื่อนไขการผ่าน", "create": "สร้างรายละเอียดเนื้อหา - เงื่อนไขการผ่าน", "update": "แก้ไขรายละเอียดเนื้อหา - เงื่อนไขการผ่าน"}, "course-report": {"module": "หลักสูตร", "update": "แก้ไขรายงานละเอียดหลักสูตร"}, "course": {"module": "หลักสูตร", "read": "เข้าถึงหลักสูตร", "create": "สร้างหลักสูตร", "update": "แก้ไขหลักสูตร", "delete": "ลบหลักสูตร"}, "curriculum": {"module": "หลักสูตร", "read": "เข้าถึงเนื้อหา"}, "customer-partner": {"module": "ลูกค้า", "create": "สร้างรายละเอียดพันธมิตรลูกค้า [<PERSON><PERSON><PERSON><PERSON>]", "update": "แก้ไขรายละเอียดพันธมิตรลูกค้า [<PERSON><PERSON><PERSON><PERSON>]", "delete": "ลบรายละเอียดพันธมิตรลูกค้า [<PERSON><PERSON><PERSON><PERSON>]"}, "customer": {"module": "ลูกค้า", "read": "เข้าถึงรายละเอียดบริษัท [<PERSON><PERSON><PERSON><PERSON>]", "create": "สร้างรายละเอียดบริษัท [<PERSON><PERSON><PERSON><PERSON>]", "update": "แก้ไขรายละเอียดบริษัท [<PERSON><PERSON><PERSON><PERSON>]", "delete": "ลบรายละเอียดบริษัท [<PERSON><PERSON><PERSON><PERSON>]"}, "data-sensitive": {"module": "ข้อมูลละเอียดอ่อน", "read": "เข้าถึงข้อมูลละเอียดอ่อน"}, "debounce-email": {"module": "อีเมล", "read": "เข้าถึง Debounce Email"}, "department": {"module": "แผนผังองค์กร", "read": "เข้าถึงแผนผังองค์กร", "create": "สร้างแผนผังองค์กร", "update": "แก้ไขแผนผังองค์กร", "delete": "ลบแผนผังองค์กร"}, "enrollment-approve": {"module": "ลงทะเบียน", "read": "เข้าถึงอนุมัติการลงทะเบียน", "update": "แก้ไขอนุมัติการลงทะเบียน"}, "enrollment-attachment-additional-types": {"module": "ลงทะเบียน", "read": "เข้าถึงประเภทเอกสาร", "create": "สร้างประเภทเอกสาร", "update": "แก้ไขประเภทเอกสาร", "delete": "ลบประเภทเอกสาร"}, "enrollment-attachment-summaries": {"module": "ลงทะเบียน", "read": "เข้าถึงสรุปเอกสารแนบการลงทะเบียน"}, "enrollment-attachment": {"module": "ลงทะเบียน", "read": "เข้าถึงเอกสารแนบการลงทะเบียน", "create": "สร้างเอกสารแนบการลงทะเบียน", "update": "แก้ไขเอกสารแนบการลงทะเบียน"}, "enrollment-bulk-bundle": {"module": "ลงทะเบียน", "create": "สร้างลงทะเบียนผู้เรียนแบบกลุ่ม [<PERSON><PERSON><PERSON><PERSON>]"}, "enrollment-bulk": {"module": "ลงทะเบียน", "create": "สร้างลงทะเบียนผู้เรียน"}, "enrollment-certificate": {"module": "ลงทะเบียน", "update": "ออกใบประกาศใหม่อีกครั้ง"}, "enrollment-face-compare": {"module": "ลงทะเบียน", "read": "เข้าถึงข้อมูลรูปภาพการยืนยันตัวตน"}, "enrollment-reset": {"module": "ลงทะเบียน", "update": "รีเซ็ตการเรียน"}, "enrollment-summary": {"module": "ลงทะเบียน", "read": "เข้าถึงรายการเรียน"}, "enrollment-upload-image": {"module": "ลงทะเบียน", "update": "อัปโหลดรูปที่ใช้ในการยืนยันตัวตน"}, "enrollment-user-agreement": {"module": "ลงทะเบียน", "update": "ลูกค้ากดยืนยัน PDPA"}, "enrollment-verify-image": {"module": "ลงทะเบียน", "read": "เข้าถึงอัปโหลดหลักฐานการยืนยันตัวตนโดยผู้ดูแลระบบ", "create": "สร้างอัปโหลดหลักฐานการยืนยันตัวตนโดยผู้ดูแลระบบ"}, "enrollment-verify": {"module": "ลงทะเบียน", "update": "แก้ไขตรวจสอบการลงทะเบียน"}, "enrollment-history": {"module": "ลงทะเบียน", "create": "สร้างประวัติการเรียน", "delete": "ยกเลิกประวัติการเรียน"}, "enrollment": {"module": "ลงทะเบียน", "create": "สร้างการลงทะเบียน", "read": "เข้าถึงการลงทะเบียน", "update": "แก้ไขการลงทะเบียน", "export": "ดาวน์โหลดข้อมูลการเรียน"}, "file-report": {"module": "รายงานไฟล์", "read": "เข้าถึงรายงานไฟล์"}, "instructor": {"module": "ผู้สอน", "read": "เข้าถึงผู้สอน", "create": "สร้างผู้สอน", "update": "แก้ไขผู้สอน", "delete": "ลบผู้สอน"}, "invoice-item": {"module": "ใบแจ้งหนี้", "read": "เข้าถึงรายการใช้งานเครดิต [<PERSON><PERSON><PERSON><PERSON>]"}, "invoice": {"module": "ใบแจ้งหนี้", "read": "เข้าถึงรายการใช้งานเครดิต [<PERSON><PERSON><PERSON><PERSON>]"}, "job": {"module": "การจัดการข้อมูลแบบกลุ่ม", "read": "เข้าถึงการจัดการข้อมูลแบบกลุ่ม", "update": "แก้ไขการจัดการข้อมูลแบบกลุ่ม"}, "learning-progress": {"module": "ความก้าวหน้าในการเรียนรู้", "create": "สร้างความก้าวหน้าในการเรียนรู้", "update": "แก้ไขความก้าวหน้าในการเรียนรู้"}, "log-activity-detect": {"module": "การยืนยันตัวตนระหว่างการเรียน", "read": "เข้าถึงข้อมูลการยืนยันตัวตนระหว่างการเรียน", "create": "สร้างข้อมูลการยืนยันตัวตนระหว่างการเรียน"}, "logs-actions": {"module": "ประวัติการใช้งานระบบ", "read": "เข้าถึงประวัติการใช้งานระบบ", "create": "สร้างประวัติการใช้งานระบบ", "update": "แก้ไขประวัติการใช้งานระบบ", "delete": "ลบประวัติการใช้งานระบบ"}, "me": {"module": "การเรียนของฉัน", "read": "เข้าถึงการเรียนของฉัน", "update": "แก้ไขการเรียนของฉัน"}, "media-transcode": {"module": "ไฟล์มีเดีย", "read": "เข้าถึงการแปลงไฟล์มีเดีย", "create": "สร้างการแปลงไฟล์มีเดีย", "update": "แก้ไขการแปลงไฟล์มีเดีย", "delete": "ลบการแปลงไฟล์มีเดีย"}, "media": {"module": "ไฟล์มีเดีย", "read": "เข้าถึงไฟล์มีเดีย", "create": "สร้างไฟล์มีเดีย", "update": "แก้ไขไฟล์มีเดีย", "delete": "ลบไฟล์มีเดีย"}, "organization-storage": {"module": "องค์กร", "read": "เข้าถึงที่เก็บข้อมูลขององค์กร", "create": "สร้างที่เก็บข้อมูลขององค์กร", "update": "แก้ไขที่เก็บข้อมูลขององค์กร", "delete": "ลบที่เก็บข้อมูลขององค์กร"}, "organization": {"module": "องค์กร", "read": "เข้าถึงข้อมูลขององค์กร"}, "permission-group": {"module": "กลุ่มสิทธิ์การใช้งาน", "read": "เข้าถึงสิทธิ์ของผู้ใช้งาน", "create": "สร้างสิทธิ์ของผู้ใช้งาน", "update": "แก้ไขสิทธิ์ของผู้ใช้งาน", "delete": "ลบสิทธิ์ของผู้ใช้งาน"}, "permission": {"module": "สิทธิ์ของผู้ใช้งาน", "read": "เข้าถึงข้อมูลขององค์กร"}, "point-history": {"module": "เครดิต", "read": "เข้าถึงประวัติรายการใช้งานเครดิต [<PERSON><PERSON><PERSON><PERSON>]", "create": "สร้างประวัติรายการใช้งานเครดิต [<PERSON><PERSON><PERSON><PERSON>]"}, "pre-enrollment-history": {"module": "ลงทะเบียนเรียนล่วงหน้า", "read": "เข้าถึงประวัติรายการลงทะเบียนเรียนล่วงหน้า [<PERSON><PERSON><PERSON><PERSON>]"}, "pre-enrollment-reservation": {"module": "ลงทะเบียนเรียนล่วงหน้า", "read": "เข้าถึงการจองการลงทะเบียนเรียนล่วงหน้า [<PERSON><PERSON><PERSON><PERSON>]", "create": "ลงทะเบียนการเรียนล่วงหน้า [<PERSON><PERSON><PERSON><PERSON>]", "update": "แก้ไขการจองการลงทะเบียนเรียนล่วงหน้า [<PERSON><PERSON><PERSON><PERSON>]", "export": "ดาวน์โหลดข้อมูลการลงทะเบียนล่วงหน้า [<PERSON><PERSON><PERSON><PERSON>]"}, "pre-enrollment": {"module": "ลงทะเบียนเรียนล่วงหน้า", "read": "เข้าถึงรายการลงทะเบียนเรียนล่วงหน้า [<PERSON><PERSON><PERSON><PERSON>]", "create": "แก้ไขรายการลงทะเบียนเรียนล่วงหน้า [<PERSON><PERSON><PERSON><PERSON>]", "update": "แก้ไขรายการลงทะเบียนเรียนล่วงหน้า [<PERSON><PERSON><PERSON><PERSON>]"}, "product-sku": {"module": "เนื้อหา", "read": "เข้าถึงรายละเอียดเนื้อหา [<PERSON><PERSON><PERSON><PERSON>]", "create": "สร้างรายละเอียดเนื้อหา [<PERSON><PERSON><PERSON><PERSON>]"}, "purchase-order-reservation": {"module": "รายการสั่งซื้อ", "read": "เข้าถึงข้อมูลจำนวนเครดิตที่ถูกจอง [<PERSON><PERSON><PERSON><PERSON>]"}, "purchase-order": {"module": "รายการสั่งซื้อ", "read": "เข้าถึงรายการสั่งซื้อ [<PERSON><PERSON><PERSON><PERSON>]", "create": "สร้างรายการสั่งซื้อ [<PERSON><PERSON><PERSON><PERSON>]", "update": "แก้ไขรายการสั่งซื้อ [<PERSON><PERSON><PERSON><PERSON>]", "delete": "ลบรายการสั่งซื้อ [<PERSON><PERSON><PERSON><PERSON>]"}, "quiz-answer": {"module": "แบบทดสอบ", "read": "เข้าถึงแบบทดสอบ", "create": "สร้างแบบทดสอบ", "update": "แก้ไขแบบทดสอบ"}, "report-history": {"module": "ประวัติรายงาน", "read": "ดู Point ใน PO [SkillLane]"}, "rounds": {"module": "รอบการลงทะเบียน", "read": "เข้าถึงรอบการลงทะเบียน", "create": "สร้างรอบการลงทะเบียน", "update": "แก้ไขรอบการลงทะเบียน", "delete": "ลบรอบการลงทะเบียน"}, "university": {"module": "มหาวิทยาลัย", "read": "ส่งเอกสารลดหย่อน"}, "user-bulk-validate": {"module": "ลงทะเบียนผู้เรียน", "create": "สร้างรายการลงทะเบียนผู้เรียน"}, "user-first-time-password": {"module": "ผู้ใช้งานระบบ", "update": "กำหนดรหัสผ่านครั้งแรกของผู้ใช้"}, "user-permission": {"module": "ผู้ใช้งานระบบ", "read": "เข้าถึงการอนุญาตผู้ใช้งานระบบ", "update": "แก้ไขการอนุญาตผู้ใช้งานระบบ"}, "user": {"module": "ผู้ใช้งานระบบ", "read": "เข้าถึงผู้ใช้งานระบบ", "create": "สร้างผู้ใช้งานระบบ", "update": "แก้ไขผู้ใช้งานระบบ", "delete": "ลบผู้ใช้งานระบบ"}, "user-group": {"module": "User Group", "read": "View User Group", "create": "Create User Group", "update": "Update User Group", "delete": "Delete User Group"}, "other": {"module": "Other"}, "user-access-log": {"module": "User Access Logs", "read": "View login access logs", "export": "Download login access logs"}, "classroom-round": {"module": "Classroom Round", "read": "View Classroom Round", "create": "Create Classroom Round", "update": "Update Classroom Round", "delete": "Delete Classroom Round"}, "notification": {"module": "Notification", "read": "View Notification Details", "create": "Create Nofification", "update": "Update Nofification", "delete": "Delete Nofification"}, "organization-contact": {"module": "Contact", "read": "View Contact Details", "create": "Create Contact", "update": "Update Contact", "delete": "Delete Contact"}, "organization-theme-config": {"module": "Theme", "read": "View Theme Details", "update": "Update Theme Details"}, "learning-path-enrollment": {"module": "Learning Path Enrollment", "read": "View Learning Path Enrollment", "update": "Update Learning Path Enrollment"}, "classroom-location-enrollment": {"module": "Classroom Location Enrollment", "read": "View Classroom Location Enrollment", "update": "Update Classroom Location Enrollment", "export": "Export Classroom Location Enrollment"}, "enrollment-attachment-additional-type": {"module": "Enrollment Attachment Additional Type", "read": "View Enrollment Attachment Additional Type", "create": "Create Enrollment Attachment Additional Type", "update": "Update Enrollment Attachment Additional Type", "delete": "Delete Enrollment Attachment Additional Type"}, "organization-terms-and-conditions": {"module": "Terms and Conditions", "read": "View Terms and Conditions", "update": "Update Terms and Conditions"}, "plan": {"module": "Plan", "read": "View Plan", "update": "Update Plan"}}, "permission_groups": {"title": "Permission Groups", "name": "Group Name", "description": "Description", "access_rights": "Access Rights", "total_members": "Total Members", "updatedAt": "Updated At", "button": {"create_permission_group": "Create Permission Group", "delete_permission_group": "Delete Permission Group", "edit": "Edit"}, "card": {"title": {"general": "General Information", "permission": "Permissions"}}, "detail": {"tab": {"general": "General", "members": "Members"}}}, "user_validation": {"title": "Enrollment File Logs", "file_name": "File Name", "submission_date": "Submission Date", "total_records": "Total Records", "customers": "Customers", "created_by": "Created By", "status": "Status", "status_options": {"pending": "Pending", "in_progress": "In Progress", "passed": "Passed", "waiting": "Waiting", "error": "Error", "cancel": "Cancel", "applied": "Applied", "rejected": "Rejected"}, "system": "System", "options": "Options", "view_detail": "View detail", "download": "Download", "reject": "Reject file", "modal": {"title": "Reject Using File Confirmation", "content": "Do you confirm rejecting the use of the file “{{fileName}}” or not?"}, "message": {"success": "File “{{fileName}}” RejectedSuccessfully."}}, "report_history": {"title": "Report Histories", "form": {"label": {"file_name": "File Name", "created_at": "Date", "file_type": "Type", "user_name": "Username", "status": "Status"}, "placeholder": {"file_name": "File Name", "submission_date": "Date", "file_type": "Type", "user_name": "Username", "status": "Status"}}, "status": {"all": "All", "pending": "Pending", "success": "Success", "failed": "Failed"}, "download": "Download", "download_report": "Report", "download_deduct_document": "Deduct Document", "options": "Options", "type": {"regulator": "Regulator", "approval": "Approval", "deduct": "Deduct", "oic_pre_report": "Report sending names of training participants (summary of the number of trainees)", "oic_regulator_pre_report": "Report sending names of training participants (per course)", "oic_post_report": "Report result of training participants (summary of the number of trainees)", "oic_regulator_post_report": "Report result of training participants (per course)", "survey_submission": "Survey Submission Report", "customer_credit_balance": "Customer Credit Balance Report", "learner_report": "Learner Report", "enrollment_compulsory_report": "Enrollment Compulsory Report"}, "notification": {"deduct_document": {"open": {"message": "Start downloading deduct document", "description": "One moment"}, "success": {"message": "Successfully downloaded deduct document", "description": "This may take a while"}, "error": {"message": "There was an error downloading the deduct document", "description": "Please,try again"}}}}, "organization_theme_config": {"title": "Theme Configuration", "updated_at": "Updated At: {0}", "updated_by": "By {0}", "alert_upload_login_background_error": "Please upload a .png, .jpg, .jpeg file format and ensure the size is no larger than 5 MB.", "alert_upload_email_banner_error": "Please upload a .png, .jpg, .jpeg file format and ensure the size is no larger than 5 MB.", "confirm_email": {"title": "Save Theme Configs"}, "appearance": {"edit_title": "Edit: Appearance", "title": "Appearance", "primary_color": "Primary Color", "logo": "Logo", "favicon": "Icon", "alert_upload_image_error": "Please upload a .png file format and ensure the size is no larger than 2 MB."}, "login": {"background": "Login <PERSON>", "login_title": "Login Title", "login_content": "Login Description", "edit_title": "Edit: <PERSON><PERSON>", "header": "<PERSON><PERSON>", "title": {"title": "Login Title", "label": "Title for <PERSON><PERSON>", "error_required": "Please enter login title", "error_length": "Login title must be under 200 characters", "placeholder": "Enter a title for the login page."}, "content": {"label": "Login Description for Login Page", "tip": "Login Description must be under 200 characters", "error_required": "Please enter Login Description", "error_length": "Login Description must be under 200 characters", "placeholder": "Enter the content text for the login page."}}, "email": {"title": "Email", "label": {"banner": "Banner", "signature": "Signature"}, "edit_title": "Edit: <PERSON><PERSON>"}}, "enrollment_attachment_additional_type": {"title": "Enrollment Attachment Additional Type", "button": {"create": "Create Enrollment Attachment Additional Type", "delete": "Delete Enrollment Attachment Additional Type"}, "form": {"label": {"name": "Enrollment Attachment Additional Document", "description": "Description"}, "placeholder": {"name_filter": "Enrollment Attachment Additional Document", "name": "Enter enrollment attachment additional document", "description": "Enter description"}, "message": {"name_required": "Please enter additional document", "description_required": "Please enter description", "duplicate": {"name": "Enrollment attachment additional type already existed, Please try again"}, "name_limit_length": "Enrollment attachment additional document should be no longer than {0} characters"}}, "detail": {"header": "Enrollment Attachment Additional Type", "title": "General", "description": "description"}, "modal": {"delete": {"title": "Do you want to delete an enrollment attachment additional type?", "warning_delete_content": "Enrollment attachment additional type “{0}” <br />it cannot be brought back"}, "button": {"cancel": "Canceled"}}, "drawer": {"create": {"title": "Create enrollment attachment additional type"}, "edit": {"title": "Edit: General Information"}}, "table": {"name": "Enrollment Attachment Additional Document", "description": "description"}}}, "course_category": {"category_code": "Category Code", "category_name": "Category Name", "category_order": "Category Order", "total_contents": "Total Content", "category_general": "General Information", "sub_category": "Sub Categories", "all_categories": "All Categories", "hierarchy": "Hierarchy", "drawer": {"all": "All {0}"}, "tab": {"tab_general": "General Information", "tab_list": "Courses", "tab_popular_courses": "Most Popular Courses", "tab_new_courses": "New Courses"}, "create_category": {"general_info": {"description_label": "Description", "placeholder_name": "Enter category name", "placeholder_code": "Enter category code", "placeholder_descritpion": "<PERSON><PERSON> desc<PERSON>"}, "hierarchy": {"label": "Category Hierarchy", "placeholder": "Select hierarchy"}, "validations": {"name": {"required": "Please enter a category name.", "start_with_space": "Category name can't start with space", "end_with_space": "Category name can't end with space", "consecutive_space": "Category name can't have consecutive space", "name_limit_length": "Category name should be no longer than {0} characters"}, "code": {"required": "Please enter category code.", "existed": "This code already existed.", "invalid": "Only upper characters (A-Z), number (0-9), and underscore (_) are allowed."}, "hierarchy": {"required": "Please select a hierarchy."}}}, "modal_delete": {"title": "Do you want to delete a category?", "category": "Category", "content": "Once deleted, it cannot be retrieved"}, "edit_category": {"general": {"title": "Edit: General Information"}, "order": {"title": "Edit: Category Order"}}, "buttons": {"create_category": "Create Category", "sorting_category": "Sorting", "delete_category": "Delete Category", "save_status": "Save Status", "see_all_category": "See All", "bulk_edit": "Bulk Edit"}, "status": {"label": "Status", "active": "Active", "inactive": "Inactive"}}, "instructor": {"create_instructor": {"title": "Create Instructor", "label_field_name": "Instructor name", "duplicated_name": "This instructor name already existed.", "placeholder_first_name": "Enter instructor first name", "placeholder_last_name": "Enter instructor last name", "label_field_phone": "Phone number", "label_field_biology": "Instructor profile", "validate_name": "Enter the instructor's name", "label_field_instructor_description": "Instructor description", "placeholder_instructor_description": "Enter a brief instructor description", "profile_phote": "Profile photo", "create_success": "Create instructor success", "create_failed": "Something went wrong, Can't create instructor"}, "edit_instructor": {"title": "Edit: <PERSON><PERSON><PERSON><PERSON>"}, "avatar": "Avatar", "full_name": "Full name", "email": "Email", "phone": "Phone number", "amount": "Amount of content taught", "updated_at": "Last modified date", "description": "Instructor description", "biology": "Biology", "buttons": {"create_instructor": "Create instructor", "delete_instructor": "Delete instructor"}, "label_full_name": "Full name", "modal": {"confirm_delete_instructor": {"title": "Do you want to delete an instructor?", "content_line_1": "Instructor \"{0}\"", "content_line_2": "Once deleted, it cannot be retrieved."}}, "edit": {"buttons": {"save": "Save"}}, "detail": {"title": "Instructor detail", "tab": {"general": {"title": "General", "card": {"title": "General information"}}, "courses": {"title": "Courses taught"}}}}, "bundle": {"search": {"place_holder": "Search ID, name or code"}, "list": {"product_sku_id": "ProductSKU ID", "name": "Bundle Name", "product_skus": "Courses", "product_sku_code": "ProductSKU Code", "point": "Points (Credits)", "manage": "Manage"}, "tab": {"detail": "Detail", "product_skus": "ProductSKUs"}, "edit": {"title": "Edit Bundle: {0}", "detail": "Bundle Detail", "name": "Bundle Name", "product_sku_code": "ProductSKU Code", "point_detail": "Bundle Points (Credits)", "point": "Bundle Points", "totalCredit": "{0} credits", "modal": {"detail": "Edit: B<PERSON><PERSON> Detail", "required": "Please enter a ProductSKU Code.", "invalid_pattern": "Please enter A-Z, 0-9 or _ only", "placeholder": "Enter ProductSKU Code", "success": "Success", "error": {"default": "Something went wrong, Cannot update bundle", "duplicated": "This ProductSKU Code already existed."}}}, "course": {"product_sku_id": "ProductSKU ID", "name": "Course Name", "status": "Course Status", "enabled": "Enabled", "disabled": "Disabled"}}, "customer": {"title": "Customer", "search": "Search code or name", "button": {"create": "Create", "download": "Download"}, "customer_name": "Customer Name", "customer_code": "Customer Code", "customer_certificate": "Certificate", "customer_certificate_active": "Active", "customer_certificate_inactive": "Inactive", "customer_certificate_logo": "Logo", "customer_certificate_message": "Certificate Message", "table": {"customer_name": "Customer Name", "customer_code": "Customer Code", "available_credit": "Available Credit", "unused_credit": "Unused Credit", "refunded_credit": "Refunded Credit", "reserved_credit": "Reserved Credit ", "not_started_credit": "Not Started Credit", "used_credit": "Used Credit", "actions": "Actions"}, "message": {"create_success": "Create Success", "update_success": "Update Success", "create_error": "Create Error", "export_error": "Export Error", "update_error": "Update Error", "customer_name_required": "customer_name_required", "customer_code_required": "customer_code_required", "customer_code_existed": "customer_code_existed", "customer_code_invalid": "Only upper characters (A-Z), number (0-9), and underscore (_) are allowed.", "customer_certificate_message_required": "Certificate Message Required", "customer_certificate_logo_required": "Certificate Logo Required", "customer_certificate_logo_invalid": "Please choose to upload the file format .png and the size does not exceed 1 MB.", "invalid_code_length": "Code should be no longer than {0} characters", "invalid_name_length": "Name should be no longer than {0} characters", "invalid_start_with_space": "Name can't start with space", "invalid_end_with_space": "Name can't end with space", "invalid_consecutive_space": "Name can't have consecutive space"}, "drawer": {"create": {"title": "Create", "info": "Customer", "customer_name": "Customer Name", "customer_code": "Customer Code", "customer_name_placeholder": "Enter Customer Name", "customer_code_placeholder": "Enter Customer Code", "button": {"create": "Create"}}, "edit": {"title": "Edit: Customer", "info": "Customer", "customer_name": "Customer Name", "customer_code": "Customer Code", "customer_name_placeholder": "Enter Customer Name", "customer_certificate": "Certificate Active", "customer_certificate_logo": "Certificate Logo", "customer_certificate_logo_message1": "Only (.png)", "customer_certificate_logo_message2": "Recommended height resolution is at least 260 px", "customer_certificate_logo_message3": "The file size should be less than 1MB and the image element should be centered.", "customer_certificate_message": "Certificate Message", "customer_certificate_placeholder": "Example: <PERSON>ll<PERSON><PERSON>"}}, "modal": {"discard_title": "Do you want to discard all the data?", "discard_message": "Once discarded, it cannot be retrieved."}, "tab": {"general": "Customer", "partner": "Customer Partner", "purchase_order": "Purchase Order", "invoice": "Invoice"}}, "customer_purchase_order": {"title": "Purchase Order", "search_status": "Status", "all_status": "ALL", "status": {"not_started": "Not Started", "available": "Available", "out_of_points": "Out of Points", "expired": "Expired"}, "button": {"create": "Create"}, "table": {"purchase_order_no": "Purchase Order No", "remain_credit": "Remain Credit", "total_credit": "Total Credit", "start_date": "Start Date", "expire_date": "Expire Date", "status": "Status", "actions": "Actions", "view": "View"}}, "invoice": {"title": "Invoice", "search_invoice_no": "Invoice No", "search_invoice_date": "Invoice Date", "table": {"invoice_no": "Invoice No", "total_amount": "Total Amount", "total_credit": "Total Credit", "invoice_date": "Invoice Date", "actions": "Actions", "view": "View"}, "invoice_detail": {"title": "Invoice No. ", "issue_date": "Issue Date: ", "summary": {"total_credit_usage_list": "Total credit usage list", "total_credit_spent": "Total credit spent"}, "filter": {"keyword": "Course/ Bundle ID or Name"}, "table": {"manage": "Actions", "view_detail": "View", "column": {"product_sku_id": "ProductSKU ID", "course_list": "Course List", "price": "Credits", "credit_spent_list": "Credit expense", "total_credit_spent": "Total credit spent"}}, "invoice_item": {"general": "General <PERSON>ail", "product_sku_id": "ProductSKU ID", "course_bundle_name": "Course/Bundle Name", "course_list": "Course List", "point_history_list": {"title": "Credit Usage List", "filter": {"fullname": "Full Name", "email": "email"}}}, "point_history": {"title": "Credit Usage:", "general": "General <PERSON>ail", "enroll_date": "Enroll Date", "fullname": "Full Name", "email": "Email", "product_sku_id": "ProductSKU ID", "course_list": "Course List", "credit_spent": "Total Credit Spent", "refund": {"button": "Refund", "modal": {"title": "Do you want to request a refund?", "content": "If you request a refund for a bundle, The credit will be returned for a course in the bundle only."}, "success": "Request refund success", "error": "Something went wrong"}}}}, "two_factor_register": {"title": "Set up 2 Factor authentication", "description": "Scan QRCode With Google Authenticator", "fill_code": "and enter the verification code", "back_to_login": "Back to login", "how_to_use": "How to use", "description_1": "Download application Google Authenticator on your mobile device", "description_2": "Open app and scan QRCode or enter this code manually:", "description_3": "In Google Authenticator", "description_4": "Enter the 6-digit code from the Google Authenticator app. To confirm your login identity", "description_5": "If you lose your device, Please contact the system administrator."}, "two_factor_login": {"title": "Verify 2 Factor Code", "to_login": "to login", "description": "Please enter the 6 digit code from Google Authenticator", "content_admin": "If you lose your device, Please contact the administrator.", "back_to_login": "Back to login"}, "two_factor_form": {"place_holder": "Fill verification code", "validate_empty": "Please enter verification code", "validate_wrong_code": "The authentication code is incorrect, Please try again", "something_wrong": "Something was wrong, Please contact the administrator using the details below.", "login": "<PERSON><PERSON>"}, "add_course_dropdown": {"placeholder": "Search Course Code/Name", "submit": "Add Content", "error": {"no_input": "Please enter a keyword"}}, "learning_path_trainee": {"list_page": {"no_content": "Learning path not found", "banner": {"title": "All Learning path", "description": "Learning plans that will help develop skills and learning. that you can search as you want"}}, "filter": {"title": "Filter", "result_amount": "amount", "result_unit": "records", "no_content": "Not have learning path content", "no_result": {"title": "The selected filter was not found", "description": "Please select a different filter or clear the default filter"}, "type": "Course types in the learning path", "received": "Received", "certificate": "Certificate"}, "learning_path_card": {"learning_path": "Learning Paths", "certificate": "Certificate"}, "detail": {"title": "Learning Path", "about": "About the learning path", "description": "Details", "curriculum": "Learning path curriculum", "show_description": "Show description", "hide_description": "Hide description", "course_number": "Course {0}", "properties": {"certificate": "Have a certificate", "no_certificate": "No certificate", "limited_time": "Limited learn time", "no_limited_time": "No learn time limit", "curriculum_sequence": "It is compulsory to learn according to the curriculum sequence.", "no_curriculum_sequence": "It is not compulsory to learn in sequence.", "number_of_courses": "{0} course", "number_of_video": "{0} vedio"}, "event": {"cancel_enroll": "Cancel enrollment", "start_at": "Start learn {0}", "learning": "Learning", "learning_complete": "Learning complete", "congratulations_certificate": "Congratulations, you received your certificate.", "receive_certificate": "You receive a certificate of learning path based on the required academic criteria.", "view_certificate": "View certificate", "criteria_certificate": "Conditions for receiving a certificate", "congratulations_learning_complete": "Congratulations, you have completed your learning path.", "new_enroll": "New enroll", "enrollment_expired": "Because the learning path has expired.", "cannot_enroll": "Cannot enroll", "expired_contact_admin": "Learning path expire Please contact the staff.", "because_canceled": "Because the learning path was cancelled.", "enroll_canceled": "The learning path has been cancelled.", "canceled_contact_admin": "If you want to take a learning path, please contact the staff.", "enroll_learning_path": "Enroll for a learning path", "select_round": "Select round", "no_self_enroll": "Unable to learn by myself", "no_self_enroll_contact_admin": "If you want to take a learning path, please contact the staff.", "criteria": "criteria", "criteria_complete_all_course": "You will complete the learning path. Upon completion of all courses in the learning path", "expiry": "Learning path expired", "cannot_canceled_pre_enrollment": "If you want to canceled, please content the staff."}, "certificate_criteria": {"collapse_title": "You will receive a certificate. Upon completion of the course in the learning path according to the specified learning criteria.", "show": "Show more", "hide": "Show less", "course": "Course", "criteria": "Criteria", "approve": "Approve"}}}, "regular_pre_enrollment_detail": {"pre_enrollment_detail": "Pre-Enrollment Detail", "pre_enrollment_id": "Pre-Enrollment ID", "enrollment_id": "Enrollment ID", "objective_type": "Objective Type", "status_title": "Status: ", "business_type": "Business Type", "customer_code": "Customer Code", "bundle_course_list": "Bundle course list", "pre_enrollment_date_detail": "Pre-Enrollment date", "round_date": "Round date", "end_date": "End date", "edit": "Edit", "action": {"reject": "Reject", "retry": "Retry", "edit_cost": "Edit cost", "edit_status": "Edit status"}, "status": {"edited_after_verify": "Edited after verify", "is_having_cost": "Rejected (Cost)", "is_not_having_cost": "Rejected(No Cost)", "waiting_verify": "Waiting verify", "failed_to_apply": "Failed to apply", "passed": "Passed", "applied": "Applied"}, "alert_message": {"success": "Success", "error": "Error"}, "notification": {"default": "System is automatically create pre-enrollment, check bulk-enrollment for further detail", "not_found": "Pre-Enrollment not found", "bulk_conflict_job": "Another job is processing, please try again later", "insufficient_credit": "Insufficient credit", "round_expired": "Round has expired, please select round again", "default_error": "Error, please try again.", "enroll_failed": "Create enrollment failed", "message": "working on retry create pre-enrollment", "message_error": "retry create pre-enrollment failed", "button_text": "Verify"}, "not_found": "Error, pre-enrollment not found"}, "user_notification": {"c": {"title": {"notification": "Notifications"}, "subtitle": {"latest": "Lates"}, "tab": {"all": "All", "announcement": "Announcement", "my_learning": "My learning", "my_team": "My Team", "knowledge_content": "Knowledge Content"}, "button": {"read_all": "Read All"}, "empty": {"title": "There are no notifications at this time.", "subtitle": "“All” notifications within the system will be displayed here."}}}, "promote_notification": {"title": "Promote notification", "button": {"create_notification": "Create Notification", "delete": "Delete", "send_notification": "Send Notification", "create_new_copy": "Create a new copy", "cancel_send_notification": "Cancel sending notifications", "edit_send_notification": "Edit the date and time notifications are sent."}, "name": "Name", "content_type": "Type", "published_at": "Published at", "status": "Status", "content_amount": "Content amount", "created_at": "Created at", "updated_at": "Updated at", "action": "Action", "edit_title": "Edit Promote Notification: {0}", "content_detail": {"general_title": "General", "content_type": "Notification Type", "name": "Notification name", "promote_content": "Promotional content", "content_title": "Title", "content_description": "Description", "set_notification_conditions": "Set Notification Conditions", "notification_conditions": {"header": "Conditions for notifying courses to user groups", "condition": "Condition", "user_group": "User groups", "all_users": "All Users"}}, "footer": {"status": "Status:"}, "modal": {"confirm_delete": {"title": "Want to delete promoted content?", "content_line_1": "You confirm that you will delete the Promoted Content", "content_line_2": "“{0}”", "content_line_3": "Once deleted, it cannot be brought back."}, "clone": {"description": "Please enter a new notification name to create a copy of this notification."}, "publish": {"title": "Confirm notification sending", "edit_title": "Edit the date and time notifications are sent", "description": "You can choose to send notifications immediately or set a date and time for notifications to be sent in advance.", "sending_label": "Sending notifications", "sending_immediate": "Send notifications immediately", "sending_scheduled": "Set the date and time to send the notification in advance", "required_date": "Please select the date the notification will be sent", "placeholder_date": "Choose the date the notification will be sent", "required_time": "Please select the time to send notifications", "placeholder_time": "Choose when to send notifications", "discard": "Discard", "submit": "Confirm sending notification", "warning_discard": "Want to discard all data?", "warning_discard_content": "Once discarded, it cannot be returned.", "warning_cancel": "Confirm to cancel sending notifications", "warning_cancel_content": "Do you want to confirm that you want to cancel sending notifications?"}, "error_validate": {"title": "This notification cannot be sent.", "content_line_1": "Due to incomplete information including:", "content_line_2": "Promotional content", "content_line_3": "Set notification conditions", "content_line_4": "Please check and complete the information before sending the notification again later."}}, "drawer": {"set_up_content": "Notification conditions {0} to user groups", "add_condition": "Add {0}", "remove_condition": "Remove Condition"}, "dropdown": {"all_user_groups": "All user groups", "all_users": "All Users", "user_groups": "User groups", "placeholder_content": "Select {0}", "placeholder_target_type": "Select conditions", "placeholder_user_groups": "Select user groups", "error_content": "Please select {0}", "error_target_type": "Please select conditions", "error_user_groups": "Please select user groups"}, "toast": {"delete_success": "Delete success", "delete_error": "Something went wrong, Can't delete", "success": "Saved successfully", "error": "Something went wrong", "action": "Action", "fetch_fail": "Something went wrong, promote notification not found"}}, "classroom_round": {"title": "Manage Classroom Rounds", "title_page": "Classroom Rounds", "bulk_create": "Bulk Create Classroom Rounds", "edit": "Edit", "tab": {"general": "Details", "video": "Record"}, "field": {"name": "Classroom Name", "code": "Code", "location_code": "Location Code", "round_date": "Round Date", "type": "Type", "detail": "Details", "maximum_user": "Capacity", "is_enabled_waitlist": "Waitlist", "instructors": "<PERSON><PERSON><PERSON><PERSON>", "status": "Status"}, "status": {"all": "All", "available": "Available", "expired": "Expired", "canceled": "Canceled"}, "location_type": {"all": "All", "virtual": "Virtual", "onsite": "Onsite", "remote": "Remote"}, "is_enabled_waitlist": {"all": "All", "enabled": "Enabled", "disabled": "Disabled"}, "maximum_user": {"unlimited": "Unlimited"}, "detail": {"title": "Information", "code": "Code", "classroom_name": "Name", "round_date": "Round Date", "title_location": "Location"}, "footer": {"status": "Status", "cancel": "Cancel Classroom Round"}, "drawer": {"edit_round": {"title": "Edit: Overview", "validate": {"start_date_required": "start date is required", "start_time_required": "start time is required", "end_time_required": "end time is required", "start_time_more_than_current_time": "please select a start time at least 1 hour longer than the current time.", "end_time_more_than_start_time": "please select an end time rather than a start time."}}, "edit_location": {"title": "Edit: Location", "sub_title": "Edit: {0}", "type": "Type", "location": "Location", "placeholder_location": "Enter location", "setting_maximum_user": "Eetting maximum user", "enable_maximum_user": "Enable maximum user", "maximum_user": "Maximum user (number)", "placeholder_maximum_user": "Enter maximum user", "enable_waitlist": "Enable waitlist", "tooltip_enable_waitlist": "Enable waitlist if there are more than the specified number of applicants", "instructor": "Instructor ({0})", "sort_instructor": "Sort instructor", "create_instructor": "Create instructor", "placeholder_instructor": "<PERSON><PERSON><PERSON><PERSON>", "add_instructor": "Add instructor", "validate": {"type_required": "type is required", "location_required": "location is required", "location_more_than_maximum": "location should be no more than {0} characters long.", "maximum_user_required": "maximum user is required", "maximum_user_only_number": "maximum user only number", "maximum_user_more_than_zero": "maximum user greater than 0", "maximum_user_more_than_user_enrollment": "maximum user cannot be changed to less than {0} คน", "maximum_user_before_enable_waitlist": "please enter the maximum user before enable waitlist", "search_instructor_required": "please enter instructor"}}}, "modal": {"confirm_delete_round": {"title": "Confirm delete", "description_line_1": "Confirm delete “{0}”", "description_line_2": "Round {0}", "description_line_3": "Are you sure to delete this operation cannot be brought back."}}, "confirm_cancle_round": {"title": "Confirm cancle"}, "confirm_delete_location": {"title": "Confirm delete", "description_line_1": "Confirm delete “{0}”", "description_line_2": "Are you sure to delete this operation cannot be brought back."}, "message": {"bulk_success": "Bulk classroom round success", "cancel_success": "Cancel classroom round success", "cancel_error": "Something went wrong, cannot cancel classroom round", "edit_error": "Something went wrong, cannot update", "delete_round_success": "Delete classroom round success", "delete_location_success": "Delete classroom location success", "delete_round_error": "Something went wrong, cannot delete classroom round", "delete_location_error": "Something went wrong, cannot delete classroom location"}, "record": {"tab_title": "Classroom Record", "title": "Video", "subtitle_1": "The video will be available for uploading after the classroom round is completed.", "subtitle_2": "Only the users who passed the class can watch the classroom record.", "disabled": "The video is not available for uploading because the classroom round is not completed."}}, "classroom_round_enrollment": {"title": "Classrooms", "manage": "Manage", "bulk_mark_result": "Bulk <PERSON>", "view_detail": "View Detail", "field": {"name": "Classroom Name", "code": "Code", "round_date": "Round Date", "type": "Type", "detail": "Details", "total_enrollment": "Total Attendees", "maximum_user": "Capacity", "is_enabled_waitlist": "Waitlist", "instructors": "<PERSON><PERSON><PERSON><PERSON>", "status": "Status"}, "status": {"all": "All", "pre_enroll": "Pre-Enroll", "in_progress": "In Progress", "pending_result": "Pending Result", "completed": "Completed", "canceled": "Canceled"}, "location_type": {"all": "All", "virtual": "Virtual", "onsite": "Onsite", "remote": "Remote"}, "is_enabled_waitlist": {"all": "All", "enabled": "Enabled", "disabled": "Disabled"}, "maximum_user": {"unlimited": "Unlimited"}}, "video_uploader": {"title": "Video", "no_video": "No available video", "select_video": "Select Video", "error": "Please select the mp4 file format and a size not exceeding 10GB", "description": "Select only 1 mp4 file and a size not exceeding 10GB", "replace": {"title": "Replace Video?", "content": "Once the action is completed, the video will be replaced.", "ok": "Select new video"}, "transcode_status": {"in_progress": "Transcoding procress might take up to 10-20 minutes long, You can proceed to another page while the transcoding is in process.", "failed": "Video transcoding process failed. Please try again.", "uploading": "Uploading video, do not close the window.", "upload_failed": "Video upload failed. Please try again."}}, "enrollment_classroom_history": {"title": "Classroom Progress", "field": {"order": "Order", "id": "Code", "name": "Name", "round_date": "Round Date", "type": "Type", "detail": "Detail", "status": "Status", "total_attendance": "Total Attendance", "homework_score": "Homework Score", "location_code": "Location Code"}, "evaluated": "Evaluated", "action": {"register": "Register", "view_detail": "View Detail"}}, "learning_sidebar": {"tab": {"learning": "My Learning", "discussion": "Discussion"}, "new_message_alert": "New Comment", "filter": {"my_comment": "My Comments", "all_comment": "All Comments", "latest_comment": "Newest"}, "discussion": {"title": "Discussion", "no_more_comment": "No more comment"}}, "term_and_condition": {"read_detail_and_accept": "Please scroll down to read and accept.", "accept_condition": "I understand the terms and conditions.", "accept": "Accept"}, "plan": {"name": "Plan or Package", "status": "Status", "start_date": "Start Date", "end_date": "End Date", "table": {"plan": {"id": "id", "name": "Plan Name", "status": "Status"}, "sub_package": {"id": "id", "name": "Package Name", "content_type": "Content Type", "start_date": "Start Date", "gracing_date": "Gracing Date", "end_date": "End Date", "total_usage_day": "Total Usage Day", "total_course": "Total Course", "total_license": "Total License", "total_transfer_license": "Total License Transfer"}}, "status_plan": {"not_started": "Not Started", "available": "Available", "expired": "Expired"}, "sub_package": {"type": {"platform": "Platform", "content": "Content", "credit": "Credit"}, "content_model_type": {"subscription": "Subscription", "custom": "Custom"}, "content_type": {"skilllane_plus": "SkillLane Plus", "onward_academy": "Onward Academy", "academic": "Academic"}}, "plan_package_license": {"status": {"assigned": "Assigned", "transferred": "Transferred", "canceled": "Canceled"}, "manage": "Actions", "user": {"status": {"not_started": "Not Started", "available": "Available", "expired": "Expired", "transferred": "Transferred", "canceled": "Canceled"}, "plan_date": "Start - end date of the plan", "package_date": "Start - end date of the package", "license_date": "Start - end date of the license", "total_usage_day": "Total usage days", "day": "Day", "duration": "Duration", "history": "View license history"}, "detail": {"select_all": "Select all plans", "plan_date": "Start - end date of the plan", "package_date": "Start - end date of the package", "remain_license": "Remaining licenses", "remain_license_available": "Remaining licenses available"}, "assigned": {"title": "All plans", "option_mode": "Option mode", "old_first": "Old first", "new_always": "New always", "description": "You can choose to add licenses in 2 ways: add a new license that has never been used before or add a license that has been partially used. If you choose to add licenses that have been used until the number is full, the system will automatically add new licenses for the user.", "confirm": {"title": "Are you sure you want to assign licenses", "detail": "After assign the licenses\nyou can manage them at any time."}, "tooltip": {"existing_license": "Cannot be selected because the user already has this license.", "no_remain_license": "Cannot be selected because there are no remaining licenses.", "no_remain_license_available": "Cannot be selected because there are no remaining licenses available", "no_select_license": "Cannot save because no license assign data has been selected"}}, "transferred": {"title": "The plan to which you want to transfer the license", "remain_license": "Transfer remaining licenses", "user_detail": {"title": "User information", "not_found": "User data not found", "name": "First Name / Last Name", "email": "Email", "status": "User status", "license": "All licenses"}, "search": {"title": "Search user information", "placeholder": "User email"}, "confirm": {"title": "Are you sure you want to transfer licenses", "detail": "After transfer the licenses\nyou can manage them at any time."}, "tooltip": {"existing_license": "Cannot be selected because the user already has this license.", "no_remain_license": "Cannot be selected because there are no remaining transferable licenses.", "no_select_license": "Cannot save because no license transfer data has been selected.", "no_transfer": "Cannot save because the license could not be transferred to the user. Please search for another user."}, "alert": "The license cannot be transferred to the current user. Please search for a new user."}, "canceled": {"title": "Plan for License cancellation", "confirm": {"title": "Are you sure you want to cancel licenses", "detail": "After cancel the licenses\nyou can manage them at any time."}, "tooltip": {"no_select_license": "Cannot save because no license cancellation data has been selected."}}, "form": {"name": "Plan or Package", "status_license": "License Status", "started_at": "License Start Date", "expired_at": "License End Date", "status": {"not_started": "Not Started", "available": "Available", "expired": "Expired", "transferred": "Transferred", "canceled": "Canceled"}}, "plan_package_license_history": {"drawer": {"title": "License History", "table": {"order": "Order", "created_date": "Created Date", "package_name": "Package Name", "detail": "Detail"}, "status": {"assigned": "Assigned", "transferred": "Transferred", "canceled": "Canceled"}}}}}}