import { GenericID, Nullable } from '@iso/constants/commonTypes';
import { date } from '@iso/helpers/dateUtils';
import { ContentProviderTypeEnum, CourseObjectiveTypeEnum } from '@iso/lms/enums/course.enum';
import { DBCollectionEnum } from '@iso/lms/enums/dbCollection.enum';
import { OrganizationStorageTypeEnum } from '@iso/lms/enums/organizationStorage.enum';
import { PackageContentModelTypeEnum, PackageTypeEnum } from '@iso/lms/enums/packages.enum';
import {
  PromoteContentAccessTypeEnum,
  PromoteNotificationContentTypeEnum,
} from '@iso/lms/enums/promoteNotification.enum';
import { UserNotificationApplicationEnum } from '@iso/lms/enums/userNotification.enum';
import { Media } from '@iso/lms/models/media.model';
import { Organization } from '@iso/lms/models/organization.model';
import { OrganizationStorage } from '@iso/lms/models/organizationStorage.model';
import { PromoteNotification } from '@iso/lms/models/promoteNotification.model';
import { UserNotification } from '@iso/lms/models/userNotification.model';
import { getPlanPackageLicenseAvailableToday } from '@iso/lms/services/planPackageLicense.service';
import { CourseObjectiveTypeTextMapper, CourseRegulatorTypeTextMapper } from '@iso/lms/types/course.type';
import { KnowledgeContentItemTypeTextMapper } from '@iso/lms/types/knowledgeContentItem.type';
import { PackageParams } from '@iso/lms/types/package.type';
import { PlanPackageParams } from '@iso/lms/types/planPackage.type';
import { PlanPackageLicenseParams } from '@iso/lms/types/planPackageLicense.type';
import { PromoteNotificationContentParams } from '@iso/lms/types/promoteNotification.type';
import { UserNotificationPayloadParams } from '@iso/lms/types/userNotification.type';
import { Inject, Injectable, Logger } from '@nestjs/common';
import { chain, Dictionary, has, isEmpty, isNil, isNull, keyBy, partition } from 'lodash';

import { Code } from '@core/instances/code';
import { Exception } from '@core/instances/exception';

import {
  AnnouncementDIToken,
  CourseDIToken,
  InstructorDIToken,
  KnowledgeContentDIToken,
  LearningPathDIToken,
  MaterialMediaDIToken,
  NotificationDIToken,
  OrganizationDIToken,
  PlanDIToken,
  UserDIToken,
  UserGroupDIToken,
} from '@applications/di/domain';
import { InfrastructuresServiceDIToken } from '@applications/di/infrastructures/services';

import { MailPayloadPromoteContentParams, PromoteContentParams } from '@constants/types/mailer.type';
import {
  OperatePromoteNotificationParams,
  PromoteContentInAppNotificationParams,
  PromoteContentItemParams,
  PromoteNotificationContentTypeMapUserNotificationTypeEnum,
  PublishPromoteNotificationParams,
} from '@constants/types/promoteNotification.type';
import { UserContentNotificationParams } from '@constants/types/user.type';
import { UserGroupContentParams } from '@constants/types/userGroup.type';

import {
  IAnnouncementRepository,
  ICourseRepository,
  IInstructorRepository,
  IKnowledgeContentItemRepository,
  ILearningPathRepository,
  IMediaRepository,
  IOrganizationRepository,
  IOrganizationStorageRepository,
  IPackageRepository,
  IPlanPackageLicenseRepository,
  IPlanPackageRepository,
  IPlanRepository,
  IPromoteNotificationRepository,
  IUserGroupRepository,
  IUserNotificationRepository,
  IUserRepository,
} from '@interfaces/repositories';
import { IMaterialMediaService } from '@interfaces/services/materialMedia.service.interface';
import { INotificationService } from '@interfaces/services/notification.service.interface';
import { IPublishPromoteNotificationUseCase } from '@interfaces/usecases/notification.interface';

import { formatToShortThaiDate } from '@domains/utils/date.util';
import { isURL } from '@domains/utils/validate.util';

const organizationKey = 'organization';

@Injectable()
export class PublishPromoteNotificationUseCase implements IPublishPromoteNotificationUseCase {
  private readonly batchSize = 500;
  private readonly logger = new Logger(PublishPromoteNotificationUseCase.name);

  constructor(
    @Inject(NotificationDIToken.PromoteNotificationRepository)
    private readonly promoteNotificationRepository: IPromoteNotificationRepository,
    @Inject(OrganizationDIToken.OrganizationRepository)
    private readonly organizationRepository: IOrganizationRepository,
    @Inject(OrganizationDIToken.OrganizationStorageRepository)
    private readonly organizationStorageRepository: IOrganizationStorageRepository,
    @Inject(LearningPathDIToken.LearningPathRepository)
    private readonly learningPathRepository: ILearningPathRepository,
    @Inject(CourseDIToken.CourseRepository)
    private readonly courseRepository: ICourseRepository,
    @Inject(KnowledgeContentDIToken.KnowledgeContentItemRepository)
    private readonly knowledgeContentItemRepository: IKnowledgeContentItemRepository,
    @Inject(AnnouncementDIToken.AnnouncementRepository)
    private readonly announcementRepository: IAnnouncementRepository,
    @Inject(UserGroupDIToken.UserGroupRepository)
    private readonly userGroupRepository: IUserGroupRepository,
    @Inject(UserDIToken.UserRepository)
    private readonly userRepository: IUserRepository,
    @Inject(UserDIToken.UserNotificationRepository)
    private readonly userNotificationRepository: IUserNotificationRepository,
    @Inject(InstructorDIToken.InstructorRepository)
    private readonly instructorRepository: IInstructorRepository,
    @Inject(MaterialMediaDIToken.MediaRepository)
    private readonly mediaRepository: IMediaRepository,
    @Inject(PlanDIToken.PlanRepository)
    private readonly planRepository: IPlanRepository,
    @Inject(PlanDIToken.PlanPackageRepository)
    private readonly planPackageRepository: IPlanPackageRepository,
    @Inject(PlanDIToken.PlanPackageLicenseRepository)
    private readonly planPackageLicenseRepository: IPlanPackageLicenseRepository,
    @Inject(PlanDIToken.PackageRepository)
    private readonly packageRepository: IPackageRepository,

    // service
    @Inject(InfrastructuresServiceDIToken.NotificationService)
    private readonly notificationService: INotificationService,
    @Inject(MaterialMediaDIToken.MaterialMediaService)
    private readonly materialMediaService: IMaterialMediaService,
  ) {}

  async execute(params: PublishPromoteNotificationParams): Promise<void> {
    const { promoteNotificationId } = params;

    this.logger.log(`[START] publish User Notification By Promote Notification`, promoteNotificationId);

    const promoteNotification = await this.promoteNotificationRepository.findOne({ id: promoteNotificationId });

    if (!promoteNotification) {
      throw Exception.new({
        code: Code.ENTITY_NOT_FOUND_ERROR,
        message: 'promote notification not found',
        data: { promoteNotificationId },
      });
    }

    if (isNull(promoteNotification.publishedAt)) {
      throw Exception.new({
        code: Code.ENTITY_NOT_FOUND_ERROR,
        message: 'promote notification has not publish yet',
        data: { promoteNotificationId },
      });
    }

    const { contentType, targetUserGroups, organizationId, content } = promoteNotification;

    const contentIds = targetUserGroups.map(({ id }) => id);

    const organization = await this.organizationRepository.findOne({ id: organizationId });

    if (!organization) {
      throw Exception.new({
        code: Code.ENTITY_NOT_FOUND_ERROR,
        message: 'organization is not found',
        data: { organizationId },
      });
    }

    const organizationStorage = await this.organizationStorageRepository.findOne({
      organizationId,
      storageType: OrganizationStorageTypeEnum.RESOURCE,
    });

    let contents = await this.getContentItemByContentTypeAndContentIds(contentType, contentIds);
    if (isEmpty(contents)) {
      this.logger.log('Have no enable contents to promote notification', promoteNotification.id);
      return;
    }

    try {
      const mediaIds = contents.map((val) => val?.thumbnailMediaId).filter((id) => !!id);

      const medias = isEmpty(mediaIds) ? [] : await this.mediaRepository.find({ id: { $in: mediaIds } });

      // inject media to content
      contents = contents.map((val) => ({
        ...val,
        media: medias.find((media) => media.id === val.thumbnailMediaId) ?? null,
      }));

      const { COURSE, KNOWLEDGE_CONTENT } = PromoteNotificationContentTypeEnum;
      const isThereInstructor = [COURSE, KNOWLEDGE_CONTENT].includes(contentType);

      // inject instructor to content
      if (isThereInstructor) {
        const instructorIds = contents.flatMap((val) => val.instructorIds);
        const isNotEmptyInstructor = !isEmpty(instructorIds);
        const instructors = isNotEmptyInstructor
          ? await this.instructorRepository.find({ id: { $in: instructorIds } })
          : [];

        contents = contents.map((val) => ({
          ...val,
          instructors: instructors
            .filter(({ id }) => val.instructorIds.includes(id))
            .map((instructor) => `${instructor.firstname ?? ''} ${instructor?.lastname ?? ''}`.trim())
            .filter((instructor) => instructor),
        }));
      }

      const inApplicationPayloadNotifications: PromoteContentInAppNotificationParams[] = contents.map((val) => {
        const payload = this.buildInApplicationNotificationPayload(contentType, val);

        const { message, url } = payload || {};
        const inApplicationPayload = {
          mediaId: val.thumbnailMediaId ?? null,
          message,
          url,
        };

        return {
          contentId: val.id,
          inApplicationPayload,
        };
      });

      const contentUserNotificationsGroupByContentId = keyBy(inApplicationPayloadNotifications, 'contentId');

      const contentsGroupById = keyBy(contents, 'id');
      const userGroupContents: UserGroupContentParams[] = targetUserGroups
        .filter(({ id }) => has(contentsGroupById, id))
        .map(({ id: contentId, userGroupIds }) => {
          const contentData = contentsGroupById[contentId];

          if (isEmpty(userGroupIds)) {
            return [
              {
                contentId,
                userGroupId: organizationKey,
                accessType: contentData?.accessType ?? PromoteContentAccessTypeEnum.PUBLIC,
              },
            ];
          }

          return userGroupIds.map((userGroupId) => ({
            contentId,
            userGroupId,
            accessType: contentData?.accessType ?? PromoteContentAccessTypeEnum.PUBLIC,
          }));
        })
        .flat();

      if (isEmpty(userGroupContents)) {
        this.logger.log('There is no match content to promote notification', promoteNotification.id);
        return;
      }

      const matchingUserGroupContents = await this.getUserGroupMatchContents(userGroupContents, contentType);

      const isSendAllUserInOrganization = matchingUserGroupContents.some(
        ({ userGroupId, accessType }) =>
          accessType === PromoteContentAccessTypeEnum.PUBLIC && userGroupId === organizationKey,
      );

      let productSKUCourseIds: GenericID[] = [];
      let contentPlanPackageIds: GenericID[] = [];
      let contentPlanPackagesGroupById: Dictionary<PlanPackageParams> = {};
      let subscriptionPackagesGroupById: Dictionary<PackageParams> = {};

      if (contentType === PromoteNotificationContentTypeEnum.COURSE) {
        productSKUCourseIds = contents
          .filter((item) => item?.contentProviderType === ContentProviderTypeEnum.EXTERNAL)
          .map((item) => item.productSKUCourseId);

        if (productSKUCourseIds.length > 0) {
          const organizationContentPlanPackageIds =
            await this.planRepository.findAvailableContentPlanPackageIdsByOrganizationId(organizationId);

          const contentPlanPackages = await this.findAvailableContentPlanPackage(
            productSKUCourseIds,
            organizationContentPlanPackageIds,
          );

          const packageIds = contentPlanPackages.map((item) => item.packageId);
          const subscriptionPackages = await this.packageRepository.find({
            id: { $in: packageIds },
            contentModeType: PackageContentModelTypeEnum.SUBSCRIPTION,
          });

          contentPlanPackageIds = contentPlanPackages.map((item) => item.id);
          contentPlanPackagesGroupById = keyBy(contentPlanPackages, 'id');
          subscriptionPackagesGroupById = keyBy(subscriptionPackages, 'id');
        }
      }

      const operateNotificationParams: OperatePromoteNotificationParams = {
        promoteNotification,
        emailContent: content,
        organization,
        organizationStorage,
        contentType,
        contentsGroupById,
        contentUserNotificationsGroupByContentId,
        userGroupContents: matchingUserGroupContents,
        productSKUCourseIds,
        contentPlanPackageIds,
        contentPlanPackagesGroupById,
        subscriptionPackagesGroupById,
      };

      if (isSendAllUserInOrganization) {
        await this.operateUserNotificationInOrganization(operateNotificationParams);
      } else {
        await this.operateUserNotificationInUserGroup(operateNotificationParams);
      }
      this.logger.log('[SUCCESS] send user notification by promote notification ', promoteNotification.id);
    } catch (error) {
      this.logger.error(`[FAILED] publish User Notification By PromoteNotification ${error} `, promoteNotification.id);
    }
  }

  private async getContentItemByContentTypeAndContentIds(
    contentType: PromoteNotificationContentTypeEnum,
    contentIds: GenericID[],
  ): Promise<PromoteContentItemParams[]> {
    switch (contentType) {
      case PromoteNotificationContentTypeEnum.ANNOUNCEMENT: {
        const announcements = await this.announcementRepository.findPublishAnnouncementByIds(contentIds);
        return announcements.map((announcement) => ({
          id: announcement.id,
          name: announcement.title,
          thumbnailMediaId: announcement.thumbnailMediaId,
          thumbnailUrl: announcement?.thumbnailUrl,
          instructors: announcement.authors ?? [],
          publishedStartAt: announcement.publishedStartAt,
        }));
      }
      case PromoteNotificationContentTypeEnum.KNOWLEDGE_CONTENT: {
        const knowledgeContentItems =
          await this.knowledgeContentItemRepository.findPublishKnowledgeContentItemsByIds(contentIds);
        return knowledgeContentItems.map((knowledgeContentItem) => ({
          id: knowledgeContentItem.id,
          name: knowledgeContentItem.title,
          thumbnailMediaId: knowledgeContentItem?.thumbnailMediaId ?? null,
          thumbnailUrl: knowledgeContentItem?.thumbnailUrl,
          type: knowledgeContentItem.type,
          instructorIds: knowledgeContentItem.instructorIds,
        }));
      }
      case PromoteNotificationContentTypeEnum.LEARNING_PATH: {
        const learningPaths = await this.learningPathRepository.findEnablePublishLearningPathByIds(contentIds);
        return learningPaths.map((learningPath) => ({
          id: learningPath.id,
          name: learningPath.learningPathVersion?.name,
          thumbnailMediaId: learningPath.thumbnailMediaId,
          thumbnailUrl: learningPath?.thumbnailUrl,
          accessType:
            PromoteContentAccessTypeEnum[learningPath.accessType as keyof typeof PromoteContentAccessTypeEnum],
          code: learningPath.code,
          totalContent: learningPath.learningPathVersion?.contents.length ?? 0,
        }));
      }
      case PromoteNotificationContentTypeEnum.COURSE: {
        const courses = await this.courseRepository.findEnabledPublishedCourseWithVersionByIds(contentIds);
        return courses.map((course) => ({
          id: course.id,
          thumbnailMediaId: course.thumbnailMediaId,
          thumbnailUrl: course?.thumbnailUrl,
          name: course.courseVersion.name,
          contentProviderType: course.contentProviderType,
          productSKUCourseId: course.productSKUCourseId,
          accessType: PromoteContentAccessTypeEnum[course.accessType as keyof typeof PromoteContentAccessTypeEnum],
          courseUrl: course.url,
          instructorIds: course.courseVersion?.instructorIds ?? [],
          objectiveType: course.objectiveType,
          regulator: course.regulatorInfo.regulator,
        }));
      }
      default: {
        return [];
      }
    }
  }

  private buildInApplicationNotificationPayload(
    contentType: PromoteNotificationContentTypeEnum,
    content: PromoteContentItemParams,
  ): Nullable<Pick<UserNotificationPayloadParams, 'message' | 'url'>> {
    const { id, code, name, courseUrl } = content;
    switch (contentType) {
      case PromoteNotificationContentTypeEnum.ANNOUNCEMENT: {
        return {
          message: `ประชาสัมพันธ์ข่าวสารและการประกาศ “${name}”`,
          url: { id },
        };
      }
      case PromoteNotificationContentTypeEnum.COURSE: {
        return {
          message: `ประชาสัมพันธ์หลักสูตร “${name}”`,
          url: { courseUrl },
        };
      }
      case PromoteNotificationContentTypeEnum.KNOWLEDGE_CONTENT: {
        return {
          message: `ประชาสัมพันธ์สื่อความรู้ “${name}”`,
          url: { id },
        };
      }
      case PromoteNotificationContentTypeEnum.LEARNING_PATH: {
        return {
          message: `ประชาสัมพันธ์แผนการเรียนรู้ “${name}”`,
          url: { code },
        };
      }
      default: {
        return null;
      }
    }
  }

  private async getUserGroupMatchContents(
    userGroupContents: UserGroupContentParams[],
    contentType: PromoteNotificationContentTypeEnum,
  ): Promise<UserGroupContentParams[]> {
    const [privateContentUserGroups, publishContents] = partition(
      userGroupContents,
      (userGroupContent) => userGroupContent.accessType === PromoteContentAccessTypeEnum.PRIVATE,
    );

    const publishContentUserGroups = publishContents.filter(
      (userGroupContent) => userGroupContent.userGroupId !== organizationKey,
    );

    const allUserGroupIdsInPublishContent = chain(publishContentUserGroups).map('userGroupId').uniq().value();

    const enablePublishContentUserGroups = await this.userGroupRepository.find({
      id: { $in: allUserGroupIdsInPublishContent },
      isEnabled: true,
    });

    const enablePublishContentUserGroupIds = new Set(enablePublishContentUserGroups.map(({ id }) => id));

    const isNotEmptyPrivateContentUserGroup = !isEmpty(privateContentUserGroups);
    let enabledPrivateContentUserGroups = [];
    if (isNotEmptyPrivateContentUserGroup) {
      const [privateAllContentUserGroups, privateSpecificContentUserGroups] = partition(
        privateContentUserGroups,
        (v) => v.userGroupId === organizationKey,
      );

      const privateAllContentIdsInUserGroup = privateAllContentUserGroups.map(({ contentId }) => contentId);
      const privateSpecificContentIdsInUserGroup = privateSpecificContentUserGroups.map(({ contentId }) => contentId);

      if (contentType === PromoteNotificationContentTypeEnum.COURSE) {
        const [enabledPrivateAllContentUserGroups, enabledPrivateSpecificContentUserGroups] = await Promise.all([
          this.userGroupRepository.findEnabledUserGroupByCourseIds(privateAllContentIdsInUserGroup),
          this.userGroupRepository.findEnabledUserGroupByIdsAndCourseIds(
            privateSpecificContentUserGroups,
            privateSpecificContentIdsInUserGroup,
          ),
        ]);

        enabledPrivateContentUserGroups = [
          ...enabledPrivateAllContentUserGroups,
          ...enabledPrivateSpecificContentUserGroups,
        ];
      } else if (contentType === PromoteNotificationContentTypeEnum.LEARNING_PATH) {
        const [enabledPrivateAllContentUserGroups, enabledPrivateSpecificContentUserGroups] = await Promise.all([
          this.userGroupRepository.findEnabledUserGroupByLearningPathIds(privateAllContentIdsInUserGroup),
          this.userGroupRepository.findEnabledUserGroupByIdsAndLearningIds(
            privateSpecificContentUserGroups,
            privateSpecificContentIdsInUserGroup,
          ),
        ]);

        enabledPrivateContentUserGroups = [
          ...enabledPrivateAllContentUserGroups,
          ...enabledPrivateSpecificContentUserGroups,
        ];
      }
    }

    const enabledPrivateContentUserGroupsGroupById = enabledPrivateContentUserGroups.reduce((acc, cur) => {
      const key = cur.id;
      acc[key] = has(acc, key) ? [...acc[key], ...cur.contentIds] : cur.contentIds;
      return acc;
    }, {});

    const filteredUserGroupContents = userGroupContents.filter((userGroupContent) => {
      const isPublishAccess = userGroupContent.accessType === PromoteContentAccessTypeEnum.PUBLIC;

      if (isPublishAccess && userGroupContent.userGroupId === organizationKey) {
        return true;
      }

      if (isPublishAccess && userGroupContent.userGroupId !== organizationKey) {
        return enablePublishContentUserGroupIds.has(userGroupContent.userGroupId);
      }

      const enabledPrivateContentUserGroup = enabledPrivateContentUserGroupsGroupById[userGroupContent.userGroupId];
      if (isNil(enabledPrivateContentUserGroup)) return false;

      const isAccessContent = enabledPrivateContentUserGroup.includes(userGroupContent.contentId);
      return isAccessContent;
    });

    return filteredUserGroupContents;
  }

  private async operateUserNotificationInOrganization(params: OperatePromoteNotificationParams): Promise<void> {
    const {
      promoteNotification,
      organization,
      organizationStorage,
      emailContent,
      contentType,
      userGroupContents,
      contentUserNotificationsGroupByContentId,
      contentsGroupById,
      productSKUCourseIds,
      contentPlanPackageIds,
      contentPlanPackagesGroupById,
      subscriptionPackagesGroupById,
    } = params;

    this.logger.log('[START] operate user notification in organization', promoteNotification.id);

    const users = [];
    const { id: organizationId } = organization;

    const cursor = this.userRepository.aggregateStream<UserContentNotificationParams>(
      [
        {
          $match: {
            organizationId,
            isTerminated: false,
          },
        },
        ...this.buildQueryFilterUserActivePlatformLicense(),
        {
          $project: {
            _id: 0,
            guid: 1,
            email: 1,
            fullName: { $concat: ['$profile.firstname', ' ', '$profile.lastname'] },
            isActivePlatformLicense: 1,
          },
        },
      ],
      { cursor: { batchSize: this.batchSize } },
    );

    const userGroupIds = userGroupContents.map(({ userGroupId }) => userGroupId);
    const isNotEmptyUserGroupIds = !isEmpty(userGroupIds);

    while (!cursor.isClosed()) {
      const isNextDocument = await cursor.hasNext();

      if (users.length >= this.batchSize || (!isNextDocument && !isEmpty(users))) {
        const userIds = users.map((val) => val?.guid);
        const userGroups = isNotEmptyUserGroupIds
          ? await this.userGroupRepository.findUserInUserGroupByUserIds(userGroupIds, userIds, organizationId)
          : [];

        const contentPlanPackageLicenses = await this.findActiveContentPlanPackageLicense(
          userIds,
          contentPlanPackageIds,
        );

        const contentLicensesGroupByUserIdAndProductSKUCourseId =
          this.groupPlanPackageLicensesByUserIdAndProductSKUCourseId(
            productSKUCourseIds,
            contentPlanPackagesGroupById,
            subscriptionPackagesGroupById,
            contentPlanPackageLicenses,
          );

        const userGroupIdsGroupByUserId = userGroups.reduce((acc, { _id: userId, userGroupIds: _userGroupIds }) => {
          acc[userId] = has(acc, userId) ? [...acc[userId], ..._userGroupIds] : _userGroupIds;
          return acc;
        }, {});

        // Build in user notification
        const userMapContentNotifications = this.getUserMapContentNotification(
          userGroupIdsGroupByUserId,
          users,
          userGroupContents,
          contentType,
          contentsGroupById,
          contentLicensesGroupByUserIdAndProductSKUCourseId,
        );

        await this.sendUserNotificationInApplication({
          promoteNotification,
          userMapContentNotifications,
          organizationId,
          contentType,
          contentUserNotificationsGroupByContentId,
        });

        await this.sendEmailNotification({
          promoteNotification,
          userMapContentNotifications,
          emailContent,
          contentsGroupById,
          organization,
          organizationStorage,
          contentType,
        });

        users.splice(0, this.batchSize);
      }

      if (isNextDocument) {
        const data = await cursor.next();
        users.push(data);
      } else {
        await cursor.close();
        break;
      }
    }
    this.logger.log('[SUCCESS] operate user notification in organization', promoteNotification.id);

    this.logger.log('[START] update sending content promote notification', promoteNotification.id);
    await this.updateSendContentTargetUserGroupsInPromoteNotification(promoteNotification, userGroupContents);
    this.logger.log('[SUCCESS] update sending content promote notification', promoteNotification.id);
  }

  private async operateUserNotificationInUserGroup(params: OperatePromoteNotificationParams): Promise<void> {
    const {
      promoteNotification,
      emailContent,
      organization,
      organizationStorage,
      userGroupContents,
      contentType,
      contentUserNotificationsGroupByContentId,
      contentsGroupById,
      productSKUCourseIds,
      contentPlanPackageIds,
      contentPlanPackagesGroupById,
      subscriptionPackagesGroupById,
    } = params;

    this.logger.log('[START] operate user notification in user group', promoteNotification.id);

    const usersInUserGroups = [];
    const { id: organizationId } = organization;

    const userGroupIds = chain(userGroupContents).map('userGroupId').uniq().value();

    const cursor = this.userGroupRepository.aggregateStream<{
      _id: string;
      userGroupIds: GenericID[];
    }>(
      [
        {
          $match: {
            id: { $in: userGroupIds },
            isEnabled: true,
          },
        },
        {
          $unwind: '$users',
        },
        {
          $replaceRoot: {
            newRoot: { $mergeObjects: [{ id: '$id' }, '$users'] },
          },
        },
        {
          $group: {
            _id: '$userId',
            userGroupIds: { $addToSet: '$id' },
          },
        },
      ],
      { cursor: { batchSize: this.batchSize } },
    );

    while (!cursor.isClosed()) {
      const isNextDocument = await cursor.hasNext();

      if ((!isNextDocument && !isEmpty(usersInUserGroups)) || usersInUserGroups.length === this.batchSize) {
        const userIds = usersInUserGroups.map(({ _id }) => _id);
        const users = await this.userRepository.aggregate<UserContentNotificationParams>([
          {
            $match: {
              guid: { $in: userIds },
              isTerminated: false,
            },
          },
          ...this.buildQueryFilterUserActivePlatformLicense(),
          {
            $project: {
              guid: 1,
              email: 1,
              fullName: { $concat: ['$profile.firstname', ' ', '$profile.lastname'] },
              isActivePlatformLicense: 1,
            },
          },
        ]);

        const userGroupIdsGroupByUserId = usersInUserGroups.reduce(
          (acc, { _id: userId, userGroupIds: _userGroupIds }) => {
            acc[userId] = has(acc, userId) ? [...acc[userId], ..._userGroupIds] : _userGroupIds;
            return acc;
          },
          {},
        );

        const contentPlanPackageLicenses = await this.findActiveContentPlanPackageLicense(
          userIds,
          contentPlanPackageIds,
        );

        const contentLicensesGroupByUserIdAndProductSKUCourseId =
          this.groupPlanPackageLicensesByUserIdAndProductSKUCourseId(
            productSKUCourseIds,
            contentPlanPackagesGroupById,
            subscriptionPackagesGroupById,
            contentPlanPackageLicenses,
          );

        // Build in user notification
        const userMapContentNotifications = this.getUserMapContentNotification(
          userGroupIdsGroupByUserId,
          users,
          userGroupContents,
          contentType,
          contentsGroupById,
          contentLicensesGroupByUserIdAndProductSKUCourseId,
        );

        await this.sendUserNotificationInApplication({
          promoteNotification,
          userMapContentNotifications,
          organizationId,
          contentType,
          contentUserNotificationsGroupByContentId,
        });

        await this.sendEmailNotification({
          promoteNotification,
          userMapContentNotifications,
          emailContent,
          contentsGroupById,
          organizationStorage,
          organization,
          contentType,
        });

        usersInUserGroups.splice(0, this.batchSize);
      }

      if (isNextDocument) {
        const document = await cursor.next();
        usersInUserGroups.push(document);
      } else {
        await cursor.close();
        break;
      }
    }
    this.logger.log('[SUCCESS] operate user notification in user group', promoteNotification.id);

    this.logger.log('[START] update sending content promote notification', promoteNotification.id);
    await this.updateSendContentTargetUserGroupsInPromoteNotification(promoteNotification, userGroupContents);
    this.logger.log('[SUCCESS] update sending content promote notification', promoteNotification.id);
  }

  private getUserMapContentNotification(
    userGroupIdsGroupByUserId: Record<GenericID, GenericID[]>,
    users: UserContentNotificationParams[],
    userGroupContents: UserGroupContentParams[],
    contentType: PromoteNotificationContentTypeEnum,
    contentsGroupById: Dictionary<PromoteContentItemParams>,
    contentLicensesGroupByUserIdAndProductSKUCourseId: Record<GenericID, Record<GenericID, PlanPackageLicenseParams[]>>,
  ): UserContentNotificationParams[] {
    return users
      .map((user) => {
        const { guid } = user;
        const userGroupIdsInUser = userGroupIdsGroupByUserId[guid] ?? [];
        const userContentLicensesGroupByProductSKUCourseId =
          contentLicensesGroupByUserIdAndProductSKUCourseId[guid] ?? {};

        const matchedContentIds = chain(userGroupContents)
          .filter(({ userGroupId, accessType, contentId }) => {
            const content = contentsGroupById[contentId];

            if (
              contentType === PromoteNotificationContentTypeEnum.COURSE &&
              content?.contentProviderType === ContentProviderTypeEnum.EXTERNAL
            ) {
              const contentLicenses = userContentLicensesGroupByProductSKUCourseId[content.productSKUCourseId] ?? [];
              if (contentLicenses.length === 0) return false;
            }

            const isAllUserInOrganization =
              accessType === PromoteContentAccessTypeEnum.PUBLIC && userGroupId === organizationKey;
            if (isAllUserInOrganization) return true;

            const isUserInTargetUserGroup = userGroupIdsInUser.includes(userGroupId);
            return isUserInTargetUserGroup;
          })
          .map('contentId')
          .uniq()
          .value();

        return { ...user, contentIds: matchedContentIds };
      })
      .filter((user) => user.contentIds.length > 0);
  }

  private async updateSendContentTargetUserGroupsInPromoteNotification(
    promoteNotification: PromoteNotification,
    userGroupContents: UserGroupContentParams[],
  ): Promise<void> {
    const userGroupContentsMapByContentId = userGroupContents.reduce((acc, cur) => {
      const { userGroupId, contentId } = cur;
      if (has(acc, contentId)) {
        acc[contentId] = [...acc[contentId], userGroupId];
      } else {
        acc[contentId] = userGroupId === organizationKey ? [] : [userGroupId];
      }
      return acc;
    }, {});

    const updateTargetUserGroups = Object.keys(userGroupContentsMapByContentId).map((contentId) => {
      const userGroupIds = userGroupContentsMapByContentId[contentId];
      return {
        id: contentId,
        userGroupIds,
      };
    });

    promoteNotification.targetUserGroups = updateTargetUserGroups;

    await this.promoteNotificationRepository.save(promoteNotification);
  }

  private async sendUserNotificationInApplication(params: {
    promoteNotification: PromoteNotification;
    userMapContentNotifications: UserContentNotificationParams[];
    organizationId: GenericID;
    contentType: PromoteNotificationContentTypeEnum;
    contentUserNotificationsGroupByContentId: Dictionary<PromoteContentInAppNotificationParams>;
  }): Promise<void> {
    const {
      promoteNotification,
      userMapContentNotifications,
      organizationId,
      contentType,
      contentUserNotificationsGroupByContentId,
    } = params;
    this.logger.log('[START] send in application user notification content', promoteNotification.id);

    const userNotificationType = PromoteNotificationContentTypeMapUserNotificationTypeEnum[contentType];

    const userNotifications = (
      await Promise.all(
        userMapContentNotifications.map(async ({ guid, contentIds }) => {
          const notifications = await Promise.all(
            contentIds.map(async (contentId) => {
              const userNotificationPayload = contentUserNotificationsGroupByContentId[contentId];

              const promoteUserNotification = await UserNotification.new({
                userId: guid,
                organizationId,
                type: userNotificationType,
                payload: userNotificationPayload.inApplicationPayload,
              });

              return promoteUserNotification;
            }),
          );

          return notifications;
        }),
      )
    ).flat();

    if (!isEmpty(userNotifications)) {
      await this.userNotificationRepository.saveMany(userNotifications);

      for (const userNotification of userNotifications) {
        const payload = {
          payload: { message: userNotification.payload.message },
          application: UserNotificationApplicationEnum.LMS,
          groupId: organizationId,
        };

        this.notificationService.sendInApplicationNotification(payload);
      }
    }

    this.logger.log(
      `[SUCCESS] send in application user notification content number: ${userNotifications.length ?? 0}`,
      promoteNotification.id,
    );
  }

  private async sendEmailNotification(params: {
    promoteNotification: PromoteNotification;
    userMapContentNotifications: UserContentNotificationParams[];
    emailContent: PromoteNotificationContentParams;
    contentsGroupById: Dictionary<PromoteContentItemParams>;
    organization: Organization;
    organizationStorage: OrganizationStorage;
    contentType: PromoteNotificationContentTypeEnum;
  }): Promise<void> {
    const {
      promoteNotification,
      userMapContentNotifications,
      emailContent,
      contentsGroupById,
      organization,
      organizationStorage,
      contentType,
    } = params;

    this.logger.log('[START] send in email notification', promoteNotification.id);

    const allContentEmailUrlMapContentType = {
      [PromoteNotificationContentTypeEnum.ANNOUNCEMENT]: {
        path: 'announcements',
        textButton: 'ดูข่าวสารและการประกาศทั้งหมด',
      },
      [PromoteNotificationContentTypeEnum.COURSE]: {
        path: 'catalog',
        textButton: 'ดูหลักสูตรทั้งหมด',
      },
      [PromoteNotificationContentTypeEnum.LEARNING_PATH]: {
        path: 'learningPaths',
        textButton: 'ดูแผนการเรียนรู้ทั้งหมด',
      },
      [PromoteNotificationContentTypeEnum.KNOWLEDGE_CONTENT]: {
        path: 'knowledgeContents',
        textButton: 'ดูสื่อความรู้ทั้งหมด',
      },
    };

    for (const userMatchContentNotification of userMapContentNotifications) {
      const { contentIds, ...user } = userMatchContentNotification;

      const contents = contentIds.map((contentId) => contentsGroupById[contentId]).filter(Boolean);
      const { email, fullName } = user;

      const { path, textButton } = allContentEmailUrlMapContentType[contentType];
      const payload: MailPayloadPromoteContentParams = {
        title: emailContent?.title || '',
        description: emailContent?.description || '',
        contents: contents.map((content) => {
          const emailPayloadContent = this.getEmailPayloadContentFromContentType(
            content,
            contentType,
            organizationStorage,
          );

          const titleLength = 72;
          const subtitle2Length = 39;
          const { imageUrl, subtitle1, subtitle2, url } = emailPayloadContent || {};
          const ellipsisTitle =
            content.name.length > titleLength ? `${content.name.slice(0, titleLength - 3).trim()}...` : content.name;
          const ellipsisSubtitle2 =
            subtitle2.length > subtitle2Length ? `${subtitle2.slice(0, subtitle2Length - 3).trim()}...` : subtitle2;
          return {
            imageUrl,
            title: ellipsisTitle,
            subtitle1,
            subtitle2: ellipsisSubtitle2,
            url,
          };
        }),
        fullName,
        url: path,
        textButton,
      };

      this.notificationService.notifyPromoteContent(email, payload, organization);
    }

    this.logger.log(
      `[SUCCESS] send in email notification number: ${userMapContentNotifications.length}`,
      promoteNotification.id,
    );
  }

  private getEmailPayloadContentFromContentType(
    content: PromoteContentItemParams,
    contentType: PromoteNotificationContentTypeEnum,
    organizationStorage: OrganizationStorage,
  ): Nullable<PromoteContentParams> {
    const imageUrl = this.getImageUrl(organizationStorage, content?.media ?? null, content?.thumbnailUrl);
    switch (contentType) {
      case PromoteNotificationContentTypeEnum.COURSE: {
        return {
          imageUrl,
          subtitle1: [
            content.objectiveType === CourseObjectiveTypeEnum.REGULAR
              ? CourseObjectiveTypeTextMapper[content.objectiveType]
              : CourseRegulatorTypeTextMapper[content.regulator],
          ],
          subtitle2: content.instructors.join(', '),
          url: `courses/${content.courseUrl}`,
        };
      }

      case PromoteNotificationContentTypeEnum.ANNOUNCEMENT:
        return {
          imageUrl,
          subtitle1: ['ข่าวสารและการประกาศ', '•', formatToShortThaiDate(content.publishedStartAt)],
          subtitle2: content.instructors.join(', '),
          url: `announcements/${content.id}`,
        };
      case PromoteNotificationContentTypeEnum.KNOWLEDGE_CONTENT: {
        return {
          imageUrl,
          subtitle1: ['สื่อความรู้', '•', KnowledgeContentItemTypeTextMapper[content.type]],
          subtitle2: content.instructors.join(', '),
          url: `knowledgeContents/${content.id}`,
        };
      }
      case PromoteNotificationContentTypeEnum.LEARNING_PATH: {
        return {
          imageUrl,
          subtitle1: ['แผนการเรียนรู้'],
          subtitle2: `จำนวนหลักสูตร ${content.totalContent}`,
          url: `learningPaths/${content.code}`,
        };
      }
      default: {
        return null;
      }
    }
  }

  private getImageUrl(organizationStorage: OrganizationStorage, media: Nullable<Media>, thumbnailUrl?: string) {
    const isValidThumbnailUrl = isURL(thumbnailUrl ?? '');

    if (isNil(media) && isValidThumbnailUrl) {
      return thumbnailUrl;
    }

    return this.materialMediaService.getMediaObjectBucketUrl(organizationStorage, media?.path);
  }

  private buildQueryFilterUserActivePlatformLicense() {
    const dateNow = date().toDate();
    return [
      {
        $lookup: {
          from: DBCollectionEnum.PLAN_PACKAGE_LICENSES,
          let: { userId: '$guid' },
          pipeline: [
            {
              $match: {
                $expr: { $eq: ['$userId', '$$userId'] },
              },
            },
            {
              $lookup: {
                from: DBCollectionEnum.PLAN_PACKAGES,
                let: { planPackageId: '$planPackageId' },
                pipeline: [
                  {
                    $match: {
                      $expr: { $eq: ['$id', '$$planPackageId'] },
                    },
                  },
                ],
                as: 'planPackage',
              },
            },
            {
              $match: {
                $and: [
                  { 'planPackage.type': PackageTypeEnum.PLATFORM },
                  {
                    $or: [
                      {
                        startedAt: null,
                        expiredAt: null,
                        'planPackage.startDate': { $lte: dateNow },
                        'planPackage.endDate': { $gte: dateNow },
                      },
                      {
                        startedAt: { $lte: dateNow },
                        expiredAt: { $gte: dateNow },
                      },
                    ],
                  },
                ],
              },
            },
            {
              $project: {
                _id: 0,
                planPackage: 0,
              },
            },
          ],
          as: 'planPackageLicenses',
        },
      },
      {
        $addFields: {
          isActivePlatformLicense: {
            $gt: [{ $size: '$planPackageLicenses' }, 0],
          },
        },
      },
      {
        $match: {
          isActivePlatformLicense: true,
        },
      },
      {
        $project: {
          planPackageLicenses: 0,
        },
      },
    ];
  }

  private async findAvailableContentPlanPackage(
    productSKUCourseIds: GenericID[],
    contentPlanPackageIds: GenericID[],
  ): Promise<PlanPackageParams[]> {
    const customContentPlanPackages =
      await this.planPackageRepository.findAvailableCustomContentPlanPackagesByProductSKUCourseIds(
        productSKUCourseIds,
        contentPlanPackageIds,
      );
    const subscriptionContentPlanPackages =
      await this.planPackageRepository.findAvailableSubscriptionContentPlanPackagesByProductSKUCourseIds(
        productSKUCourseIds,
        contentPlanPackageIds,
      );

    return customContentPlanPackages.concat(subscriptionContentPlanPackages);
  }

  private async findActiveContentPlanPackageLicense(
    userIds: GenericID[],
    contentPlanPackageIds: GenericID[],
  ): Promise<PlanPackageLicenseParams[]> {
    const pendingContentPlanPackageLicenses =
      await this.planPackageLicenseRepository.findPlanPackageLicenseMultipleUserSOPendingActive(
        userIds,
        contentPlanPackageIds,
      );

    const approvedContentPlanPackageLicenses =
      await this.planPackageLicenseRepository.findPlanPackageLicenseMultipleUserSOApprovedActive(
        userIds,
        contentPlanPackageIds,
      );

    const contentPlanPackageLicenses = pendingContentPlanPackageLicenses.concat(approvedContentPlanPackageLicenses);
    return getPlanPackageLicenseAvailableToday(contentPlanPackageLicenses);
  }

  private groupPlanPackageLicensesByUserIdAndProductSKUCourseId(
    promoteProductSKUCourseIds: GenericID[],
    contentPlanPackagesGroupById: Dictionary<PlanPackageParams>,
    subscriptionPackagesGroupById: Dictionary<PackageParams>,
    planPackageLicenseContents: PlanPackageLicenseParams[],
  ): Record<GenericID, Record<GenericID, PlanPackageLicenseParams[]>> {
    const userContentLicenseMap: Record<GenericID, Record<GenericID, PlanPackageLicenseParams[]>> = {};

    for (const license of planPackageLicenseContents) {
      const { userId } = license;
      const planPackage = contentPlanPackagesGroupById[license.planPackageId];

      let productSKUCourseIds: GenericID[] = [];

      if ('model' in planPackage.content && planPackage.content.model === PackageContentModelTypeEnum.CUSTOM) {
        productSKUCourseIds = planPackage.content.productSKUCourseIds ?? [];
      } else if (
        'model' in planPackage.content &&
        planPackage.content.model === PackageContentModelTypeEnum.SUBSCRIPTION
      ) {
        productSKUCourseIds = subscriptionPackagesGroupById[planPackage.packageId]?.productSKUCourseIds ?? [];
      }

      const availableProductSKUCourseIds = productSKUCourseIds.filter((id) => promoteProductSKUCourseIds.includes(id));

      if (!userContentLicenseMap[userId]) userContentLicenseMap[userId] = {};

      for (const productSKUCourseId of availableProductSKUCourseIds) {
        if (!userContentLicenseMap[userId][productSKUCourseId]) {
          userContentLicenseMap[userId][productSKUCourseId] = [];
        }
        userContentLicenseMap[userId][productSKUCourseId].push(license);
      }
    }

    return userContentLicenseMap;
  }
}
