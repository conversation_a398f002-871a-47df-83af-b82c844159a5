import { GenericID, Nullable } from '@iso/constants/commonTypes';
import { PlanPackageHistoryStatusEnum } from '@iso/lms/enums/planPackageLicenseHistory.enum';
import { ReportHistoryStatusEnum } from '@iso/lms/enums/reportHistory.enum';
import { UserStatusEnum } from '@iso/lms/enums/user.enum';
import { ReportHistoryFile } from '@iso/lms/models/reportHistory.model';
import { PermissionGroupParams } from '@iso/lms/types/permissionGroup.type';
import { PlanPackageLicenseParams } from '@iso/lms/types/planPackageLicense.type';
import { Inject, Injectable } from '@nestjs/common';
import clone from 'lodash/clone';
import groupBy from 'lodash/groupBy';
import intersection from 'lodash/intersection';
import isEmpty from 'lodash/isEmpty';
import isNil from 'lodash/isNil';
import orderBy from 'lodash/orderBy';
import partition from 'lodash/partition';
import union from 'lodash/union';
import uniq from 'lodash/uniq';
import uniqBy from 'lodash/uniqBy';

import { ILogger } from '@infrastructures/services/logger/interfaces';

import { Code } from '@core/instances/code';
import { Exception } from '@core/instances/exception';

import { PermissionGroupDIToken, PlanDIToken, ReportDIToken, UserDIToken } from '@applications/di/domain';
import { DepartmentDIToken } from '@applications/di/domain/department.di';
import { InfrastructuresPersistenceDIToken } from '@applications/di/infrastructures/persistence';
import { InfrastructuresServiceDIToken } from '@applications/di/infrastructures/services';

import { StorageProviderEnum, UploadFileTypeEnum } from '@constants/enums/infrastructures/storage.enum';
import { ResultFindPlanPackageLicenseUserIdByUserIdsAndStatusesParams } from '@constants/types/planPackageLicenseHistory.type';
import {
  FilterExportLicenseUsageReportHistoryParams,
  LicenseUsageDepartmentInfoParams,
  LicenseUsageReportPlanPackageLicenseParams,
  LicenseUsageUserInDepartmentParams,
} from '@constants/types/reportHistory.type';

import {
  IDepartmentRepository,
  IPlanPackageLicenseHistoryRepository,
  IPlanPackageLicenseRepository,
  IPlanPackageRepository,
  IPlanRepository,
  IReportHistoryRepository,
  IUserRepository,
} from '@interfaces/repositories';
import { IPermissionGroupRepository } from '@interfaces/repositories/permissionGroup.repository.interface';
import { ILicenseUsageReportService } from '@interfaces/services/report.service.interface';
import { IStorageRepositoryFactory } from '@interfaces/storage/storage';
import { IExportLicenseUsageReportUseCase } from '@interfaces/usecases/report.interface';

import { arrayToHashMapByKey } from '@domains/utils/collection.util';

@Injectable()
export class ExportLicenseUsageReportUseCase implements IExportLicenseUsageReportUseCase {
  constructor(
    // Infrastructure
    @Inject(InfrastructuresPersistenceDIToken.StorageRepositoryFactory)
    private readonly storageRepositoryFactory: IStorageRepositoryFactory,
    @Inject(InfrastructuresServiceDIToken.LoggerApplication)
    private readonly logger: ILogger,
    // Repository
    @Inject(ReportDIToken.ReportHistoryRepository) private readonly reportHistoryRepository: IReportHistoryRepository,
    @Inject(PlanDIToken.PlanPackageRepository) private readonly planPackageRepository: IPlanPackageRepository,
    @Inject(UserDIToken.UserRepository) private readonly userRepository: IUserRepository,
    @Inject(DepartmentDIToken.DepartmentRepository) private readonly departmentRepository: IDepartmentRepository,
    @Inject(PlanDIToken.PlanRepository) private readonly planRepository: IPlanRepository,
    @Inject(PlanDIToken.PlanPackageLicenseRepository)
    private readonly planPackageLicenseRepository: IPlanPackageLicenseRepository,
    @Inject(PlanDIToken.PlanPackageLicenseHistoryRepository)
    private readonly planPackageLicenseHistoryRepository: IPlanPackageLicenseHistoryRepository,
    @Inject(PermissionGroupDIToken.PermissionGroupRepository)
    private readonly permissionGroupRepository: IPermissionGroupRepository,

    // Service
    @Inject(ReportDIToken.LicenseUsageReportService)
    private readonly licenseUsageReportService: ILicenseUsageReportService,
  ) {}

  async execute(params: FilterExportLicenseUsageReportHistoryParams): Promise<void> {
    const reportHistory = await this.reportHistoryRepository.findById(params.reportHistoryId);

    if (!reportHistory) {
      throw Exception.new({
        code: Code.ENTITY_NOT_FOUND_ERROR,
        message: 'Report does not exist.',
        data: { reportHistoryId: params.reportHistoryId },
      });
    }

    if (reportHistory.status !== ReportHistoryStatusEnum.PENDING) {
      throw Exception.new({
        code: Code.ENTITY_VALIDATION_ERROR,
        message: 'Report status is not pending',
        data: { reportHistoryId: params.reportHistoryId },
      });
    }

    const isFilterDepartment = !isNil(params.departmentIds);
    const isFilterUserIds = !isEmpty(params.userIds);

    const isFilterPlan = !isEmpty(params.planIds);
    const isFilterPackage = !isEmpty(params.packageIds);
    const isFilterPlanPackage = isFilterPlan || isFilterPackage;
    const isFilterUserStatus =
      !isEmpty(params.userStatus) &&
      !Object.values(UserStatusEnum).every((status) => params.userStatus.includes(status));

    const filteredPlanPackageIds: GenericID[] = [];

    let filteredUserIds: GenericID[] = params.userIds;
    let isFirstFilterUserIds = !isFilterUserIds;

    if (isFilterDepartment) {
      const filteredUserIdsDepartment = await this.departmentRepository.findUserIdsByIdsAndOrganizationId(
        params.departmentIds,
        params.organizationId,
      );

      filteredUserIds = isFirstFilterUserIds
        ? clone(filteredUserIdsDepartment)
        : intersection(filteredUserIds, filteredUserIdsDepartment);

      filteredUserIdsDepartment.splice(0, filteredUserIdsDepartment.length);
      isFirstFilterUserIds = false;
    }

    if (isFilterUserStatus) {
      const activePlatformPlanPackageIds = await this.planRepository.findActivePlatformPlanPackageIdsByOrganizationId(
        params.organizationId,
      );

      const activeLicenseUserId =
        await this.planPackageLicenseRepository.findActiveLicenseUserIdsByPlanPackageIds(activePlatformPlanPackageIds);

      if (params.userStatus.includes(UserStatusEnum.IN_ACTIVE)) {
        const inActiveLicenseUserIds = await this.userRepository.findIdsByNotEqualIdsAndOrganizationId(
          activeLicenseUserId,
          params.organizationId,
        );

        filteredUserIds = isFirstFilterUserIds
          ? clone(inActiveLicenseUserIds)
          : intersection(filteredUserIds, inActiveLicenseUserIds);

        inActiveLicenseUserIds.splice(0, inActiveLicenseUserIds.length);
      } else {
        filteredUserIds = isFirstFilterUserIds
          ? clone(activeLicenseUserId)
          : intersection(filteredUserIds, activeLicenseUserId);
      }

      activeLicenseUserId.splice(0, activeLicenseUserId.length);
      activePlatformPlanPackageIds.splice(0, activePlatformPlanPackageIds.length);
      isFirstFilterUserIds = false;
    }

    if (isFilterPlanPackage) {
      const planPackageIds = await this.planPackageRepository.findIdsByPackageTypePlanIdAndPackageIds({
        planIds: params.planIds,
        packageIds: params.packageIds,
      });

      const filteredUserIdPlanPackageLicenseIds =
        await this.planPackageLicenseRepository.findUserIdsByPlanPackageIds(planPackageIds);

      const filteredUserIdsPlanedPackageLicenseHistoryIds =
        await this.planPackageLicenseHistoryRepository.findUserIdsByPlanPackageLicenseIdsAndStatus(
          filteredUserIdPlanPackageLicenseIds,
          [PlanPackageHistoryStatusEnum.TRANSFERRED],
        );

      const userIdsInPlanPackageLicenseIds = union(
        filteredUserIdPlanPackageLicenseIds,
        filteredUserIdsPlanedPackageLicenseHistoryIds,
      );

      filteredUserIds = isFirstFilterUserIds
        ? clone(userIdsInPlanPackageLicenseIds)
        : intersection(filteredUserIds, userIdsInPlanPackageLicenseIds);

      filteredPlanPackageIds.push(...planPackageIds);
      isFirstFilterUserIds = false;

      planPackageIds.splice(0, planPackageIds.length);
      userIdsInPlanPackageLicenseIds.splice(0, userIdsInPlanPackageLicenseIds.length);
      filteredUserIdPlanPackageLicenseIds.splice(0, filteredUserIdPlanPackageLicenseIds.length);
      filteredUserIdsPlanedPackageLicenseHistoryIds.splice(0, filteredUserIdsPlanedPackageLicenseHistoryIds.length);
    }

    const isThereFilter = !isFirstFilterUserIds;
    const isFilterFoundUserIds = !isThereFilter || (isThereFilter && !isEmpty(filteredUserIds));

    const users = isFilterFoundUserIds
      ? await this.userRepository.findUserForLicenseUsageReportByIds(filteredUserIds, params.organizationId)
      : [];

    const userIds = users.map((user) => user.guid);
    const permissionGroupIds = users.flatMap((user) => user.permissionGroupIds);
    const [departments, permissionGroups, planPackageLicenses, activeUserIdsPlanPackagePlatformLicenses] =
      await Promise.all([
        this.findUserDepartmentAndDepartmentInfo(userIds),
        this.permissionGroupRepository.findByIdsWithSelectors(permissionGroupIds, ['id', 'name']),
        this.findPlanPackageLicense(userIds, isFilterPlanPackage ? new Set(filteredPlanPackageIds) : null),
        this.planPackageLicenseRepository.findActivePlatformPackageLicenseUserIdsByUserIds(userIds),
      ]);

    const [userDepartments, departmentInfos] = departments;
    const userInDepartmentByUserId = arrayToHashMapByKey(userDepartments, 'userId');
    const departmentById = arrayToHashMapByKey(departmentInfos, 'id');

    const permissionGroupById = arrayToHashMapByKey<PermissionGroupParams, GenericID>(permissionGroups, 'id');
    const planPackageLicenseGroupByUserId = new Map(Object.entries(groupBy(planPackageLicenses, 'userId')));

    const activeUserIds = new Set(activeUserIdsPlanPackagePlatformLicenses);

    userIds.splice(0, userIds.length);
    permissionGroupIds.splice(0, permissionGroupIds.length);
    filteredPlanPackageIds.splice(0, filteredPlanPackageIds.length);

    departments.splice(0, departments.length);
    permissionGroups.splice(0, permissionGroups.length);
    planPackageLicenses.splice(0, planPackageLicenses.length);
    activeUserIdsPlanPackagePlatformLicenses.splice(0, activeUserIdsPlanPackagePlatformLicenses.length);

    const rawData = this.licenseUsageReportService.buildReportRawData(users, {
      userInDepartmentByUserId,
      departmentById,
      permissionGroupById,
      planPackageLicenseGroupByUserId,
      activeUserIds,
    });

    departmentById.clear();
    permissionGroupById.clear();
    userInDepartmentByUserId.clear();
    planPackageLicenseGroupByUserId.clear();
    activeUserIds.clear();

    try {
      const buffer = await this.licenseUsageReportService.buildReportFile(rawData);
      const filePath = `xlsx/reports/${reportHistory.fileName}.xlsx`;
      const reportHistoryFile = new ReportHistoryFile({
        fileName: reportHistory.fileName,
        filePath,
      });
      reportHistory.files.push(reportHistoryFile);

      rawData.splice(0, rawData.length);

      const storageRepository = this.storageRepositoryFactory.getInstance(StorageProviderEnum.AWS);
      await storageRepository.uploadStream({
        raw: buffer,
        path: filePath,
        fileType: UploadFileTypeEnum.XLSX,
      });

      reportHistory.status = ReportHistoryStatusEnum.COMPLETED;
      await this.reportHistoryRepository.save(reportHistory);
      this.logger.log(`Successfully ${reportHistory.type} reportHistory ID ${reportHistory.id}`);
    } catch (error) {
      this.logger.error(
        `| Something wrong, ${reportHistory.type} : reportHistory ID ${reportHistory.id} found error: ${error}`,
      );
      reportHistory.status = ReportHistoryStatusEnum.ERROR;
      await this.reportHistoryRepository.save(reportHistory);
      throw error;
    }
  }

  private async findPlanPackageLicense(
    userIds: GenericID[],
    planPackageIdsSet: Nullable<Set<GenericID>>,
  ): Promise<LicenseUsageReportPlanPackageLicenseParams[]> {
    const [soApprovedPlanPackageLicenses, nonSOApprovedPlanPackageLicenses] = await Promise.all([
      this.findSOAprovedPlanPackageLicenses(userIds, planPackageIdsSet),
      this.findNonSOApprovedPlanPackageLicenses(userIds, planPackageIdsSet),
    ]);

    const userPlanPackageLicenses = soApprovedPlanPackageLicenses
      .concat(nonSOApprovedPlanPackageLicenses)
      .sort((a, b) => a.createdAt.getTime() - b.createdAt.getTime());

    return userPlanPackageLicenses;
  }

  private async findSOAprovedPlanPackageLicenses(
    userIds: GenericID[],
    planPackageIdsSet: Nullable<Set<GenericID>>,
  ): Promise<LicenseUsageReportPlanPackageLicenseParams[]> {
    const userPlanPackageLicenseHistories =
      await this.planPackageLicenseHistoryRepository.findPlanPackageLicenseUserIdByUserIdsAndStatuses(userIds, [
        PlanPackageHistoryStatusEnum.ASSIGNED,
        PlanPackageHistoryStatusEnum.TRANSFERRED,
      ]);

    const packageLicenseHistoryIds = userPlanPackageLicenseHistories
      .flatMap((userPlanPackageLicense) => userPlanPackageLicense.planPackageLicenses)
      .map((planPackageLicense) => planPackageLicense.id);

    const [userPlanPackageLicenses, userReceivedPlanPackageLicensesHistory] = await Promise.all([
      this.planPackageLicenseRepository.findSoApproveLicenseGroupUserByUserIds(userIds),
      this.planPackageLicenseRepository.findByIdsWithSelectors(packageLicenseHistoryIds, ['id', 'planPackageId']),
    ]);

    packageLicenseHistoryIds.splice(0, packageLicenseHistoryIds.length);

    const userReceivedPlanPackageLicenseHistoryById = arrayToHashMapByKey<PlanPackageLicenseParams, GenericID>(
      userReceivedPlanPackageLicensesHistory,
      'id',
    );

    const userLatestPlanPackageLicensesHistory = this.mapPlanPackageLicenseHistoryWithPlanPackage(
      userPlanPackageLicenseHistories,
      userReceivedPlanPackageLicenseHistoryById,
    );

    userReceivedPlanPackageLicenseHistoryById.clear();
    userPlanPackageLicenseHistories.splice(0, userPlanPackageLicenseHistories.length);

    const planPackageIdsInLicenses = userPlanPackageLicenses
      .flatMap((userPlanPackageLicense) => userPlanPackageLicense.planPackageLicenses)
      .filter(
        (planPackageLicense) => isNil(planPackageIdsSet) || planPackageIdsSet.has(planPackageLicense.planPackageId),
      )
      .map((planPackageLicense) => planPackageLicense.planPackageId);

    const planPackageIdsInTransferLicenses = userLatestPlanPackageLicensesHistory
      .flatMap((userLicense) => userLicense.transferredPlanPackageLicenses)
      .filter(
        (planPackageLicense) => isNil(planPackageIdsSet) || planPackageIdsSet.has(planPackageLicense.planPackageId),
      )
      .map((planPackageLicense) => planPackageLicense.planPackageId);

    const planPackageIds = uniq([...planPackageIdsInLicenses, ...planPackageIdsInTransferLicenses]);
    const planPackages = await this.planPackageRepository.findByIdsWithSelectors(planPackageIds, [
      'id',
      'name',
      'planId',
    ]);

    const planIds = uniq(planPackages.map((planPackage) => planPackage.planId));
    const plans = await this.planRepository.findByIdsWithSelectors(planIds, ['id', 'name']);

    planIds.splice(0, planIds.length);
    planPackageIds.splice(0, planPackageIds.length);

    const planById = arrayToHashMapByKey(plans, 'id');
    const planPackageById = arrayToHashMapByKey(planPackages, 'id');

    const userPlanPackageLicensesByUserId = arrayToHashMapByKey(userPlanPackageLicenses, 'userId');
    const userPlanPackageLicensesHistoryByUserId = arrayToHashMapByKey(userLatestPlanPackageLicensesHistory, 'userId');

    plans.splice(0, plans.length);
    planPackages.splice(0, planPackages.length);

    const distinctLatestUserLicensesInPlanPackage = userIds
      .map((userId) => {
        const userPlanPackageLicense = userPlanPackageLicensesByUserId.get(userId);
        const planPackageLicenseInUsers = userPlanPackageLicense?.planPackageLicenses ?? [];

        const userPlanPackageLicenseHistory = userPlanPackageLicensesHistoryByUserId.get(userId);

        const assignedPlanPackageLicenses = userPlanPackageLicenseHistory?.assignedPlanPackageLicenses ?? [];
        const transferredPlanPackageLicenses = userPlanPackageLicenseHistory?.transferredPlanPackageLicenses ?? [];

        const planPackageIdsInTransferredPlanPackageLicense = transferredPlanPackageLicenses.map(
          (transferredPlanPackageLicense) => transferredPlanPackageLicense.planPackageId,
        );

        const planPackageIdsInUserPlanPackageLicense = planPackageLicenseInUsers.map(
          (planPackageLicense) => planPackageLicense.planPackageId,
        );

        const allPlanPackageIds = union(
          planPackageIdsInTransferredPlanPackageLicense,
          planPackageIdsInUserPlanPackageLicense,
        );

        const planPackageLicenseInUsersByPlanPackageId = arrayToHashMapByKey(
          planPackageLicenseInUsers,
          'planPackageId',
        );

        const assignedPlanPackageLicenseById = arrayToHashMapByKey(assignedPlanPackageLicenses, 'id');

        const transferredPlanPackageLicensesByPlanPackageId = arrayToHashMapByKey(
          transferredPlanPackageLicenses,
          'planPackageId',
        );

        planPackageIdsInUserPlanPackageLicense.splice(0, planPackageIdsInUserPlanPackageLicense.length);
        planPackageIdsInTransferredPlanPackageLicense.splice(0, planPackageIdsInTransferredPlanPackageLicense.length);

        return allPlanPackageIds
          .map((planPackageId) => {
            const planPackageLicense = planPackageLicenseInUsersByPlanPackageId.get(planPackageId);
            if (!isNil(planPackageLicense)) {
              const assignPlanPackageLicense = assignedPlanPackageLicenseById.get(planPackageLicense.id);
              return {
                ...planPackageLicense,
                userId,
                startedAt: assignPlanPackageLicense?.startedAt ?? planPackageLicense.startedAt,
              };
            }

            const transferredPlanPackage = transferredPlanPackageLicensesByPlanPackageId.get(planPackageId);
            if (!isNil(transferredPlanPackage)) {
              return { ...transferredPlanPackage, userId };
            }
            return null;
          })
          .filter((v) => !isNil(v));
      })
      .flat();

    userPlanPackageLicensesByUserId.clear();
    userPlanPackageLicensesHistoryByUserId.clear();

    const results: LicenseUsageReportPlanPackageLicenseParams[] = distinctLatestUserLicensesInPlanPackage
      .filter((userPlanPackageLicense) => planPackageById.has(userPlanPackageLicense.planPackageId))
      .map((userPlanPackageLicense) => {
        const planPackage = planPackageById.get(userPlanPackageLicense.planPackageId);
        const plan = planById.get(planPackage.planId);

        return {
          licenseId: userPlanPackageLicense.id,
          userId: userPlanPackageLicense.userId,
          planName: plan.name,
          packageName: planPackage.name,
          startedDate: userPlanPackageLicense.startedAt,
          expiredDate: userPlanPackageLicense.expiredAt,
          createdAt: userPlanPackageLicense.createdAt,
        };
      });
    return results;
  }

  private async findNonSOApprovedPlanPackageLicenses(
    userIds: GenericID[],
    planPackageIdsSet: Nullable<Set<GenericID>>,
  ): Promise<LicenseUsageReportPlanPackageLicenseParams[]> {
    const userPlanPackageLicenses =
      await this.planPackageLicenseRepository.findNonSoApproveLicenseGroupUserByUserIds(userIds);

    const planPackageIds = uniq(
      userPlanPackageLicenses
        .flatMap((userPlanPackageLicense) => userPlanPackageLicense.planPackageLicenses)
        .filter(
          (planPackageLicense) => isNil(planPackageIdsSet) || planPackageIdsSet.has(planPackageLicense.planPackageId),
        )
        .map((planPackageLicense) => planPackageLicense.planPackageId),
    );

    const planPackages = await this.planPackageRepository.findByIdsWithSelectors(planPackageIds, [
      'id',
      'name',
      'planId',
      'startDate',
      'endDate',
    ]);

    const planIds = uniq(planPackages.map((planPackage) => planPackage.planId));
    const plans = await this.planRepository.findByIdsWithSelectors(planIds, ['id', 'name']);

    planIds.splice(0, planIds.length);
    planPackageIds.splice(0, planPackageIds.length);

    const planById = arrayToHashMapByKey(plans, 'id');
    const planPackageById = arrayToHashMapByKey(planPackages, 'id');

    const results = userPlanPackageLicenses
      .map((userPlanPackageLicense) => {
        return userPlanPackageLicense.planPackageLicenses
          .filter((planPackageLicense) => planPackageById.has(planPackageLicense.planPackageId))
          .map((planPackageLicense) => {
            const planPackage = planPackageById.get(planPackageLicense.planPackageId);
            const plan = planById.get(planPackage.planId);
            return {
              licenseId: planPackageLicense.id,
              userId: userPlanPackageLicense.userId,
              planName: plan.name,
              packageName: planPackage.name,
              startedDate: planPackage.startDate,
              expiredDate: planPackage.endDate,
              createdAt: planPackageLicense.createdAt,
            };
          });
      })
      .flat();

    return results;
  }

  private async findUserDepartmentAndDepartmentInfo(
    userIds: GenericID[],
  ): Promise<[Array<LicenseUsageUserInDepartmentParams>, Array<LicenseUsageDepartmentInfoParams>]> {
    const departments = await this.departmentRepository.findDepartmentUserIdsByUserIds(userIds);

    const userDepartments = departments
      .map((department) => {
        return department.userIds.map((userId) => ({ userId, departmentId: department.id }));
      })
      .flat();

    const departmentInfos = departments.map((department) => ({ id: department.id, name: department.name }));

    return [userDepartments, departmentInfos];
  }

  private mapPlanPackageLicenseHistoryWithPlanPackage(
    userPlanPackageLicenseHistory: ResultFindPlanPackageLicenseUserIdByUserIdsAndStatusesParams[],
    planPackageLicenseById: Map<GenericID, PlanPackageLicenseParams>,
  ): Array<{
    userId: GenericID;
    assignedPlanPackageLicenses: Array<{
      id: GenericID;
      planPackageId: GenericID;
      status: PlanPackageHistoryStatusEnum;
      startedAt: Date;
      expiredAt: Date;
      createdAt: Date;
    }>;
    transferredPlanPackageLicenses: Array<{
      id: GenericID;
      planPackageId: GenericID;
      status: PlanPackageHistoryStatusEnum;
      startedAt: Date;
      expiredAt: Date;
      createdAt: Date;
    }>;
  }> {
    const userPlanPackageLicense = userPlanPackageLicenseHistory.map((userLicense) => {
      const planPackageLicenses = userLicense.planPackageLicenses
        .filter((planPackageLicense) => planPackageLicenseById.has(planPackageLicense.id))
        .map((planPackageLicense) => ({
          ...planPackageLicense,
          planPackageId: planPackageLicenseById.get(planPackageLicense.id).planPackageId,
        }));

      const [assignedPlanPackageLicenses, transferPlanPackageLicense] = partition(
        planPackageLicenses,
        (planPackageLicense) => planPackageLicense.status === PlanPackageHistoryStatusEnum.ASSIGNED,
      );

      const descCreatedAssignedPlanPackageLicenses = orderBy(assignedPlanPackageLicenses, 'createdAt', 'desc');
      const latestCreatedAtAssignedPlanPackageLicenses = uniqBy(
        descCreatedAssignedPlanPackageLicenses,
        'planPackageId',
      );

      const descCreatedAtTransferPlanPackageLicenses = orderBy(transferPlanPackageLicense, 'createdAt', 'desc');
      const latestCreatedAtTransferPlanPackageLicenses = uniqBy(
        descCreatedAtTransferPlanPackageLicenses,
        'planPackageId',
      );

      return {
        userId: userLicense.userId,
        assignedPlanPackageLicenses: latestCreatedAtAssignedPlanPackageLicenses,
        transferredPlanPackageLicenses: latestCreatedAtTransferPlanPackageLicenses,
      };
    });

    return userPlanPackageLicense;
  }
}
