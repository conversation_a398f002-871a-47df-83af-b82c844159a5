import { GenericID } from '@iso/constants/commonTypes';
import { DateFormat } from '@iso/helpers/dateUtils';
import {
  ApplicantTypeTHEnum,
  LicenseRenewalEnum,
  LicenseRenewalTHEnum,
  LicenseTypeEnum,
} from '@iso/lms/enums/course.enum';
import {
  PreEnrollmentTransactionEnrollByEnum,
  PreEnrollmentTransactionStatusEnum,
} from '@iso/lms/enums/preEnrollmentTransaction.enum';
import { CourseSubjectParams } from '@iso/lms/types/course.type';
import { Injectable } from '@nestjs/common';
import { isNull, uniq } from 'lodash';

import { ExcelBuilderService } from '@infrastructures/services/files/excelBuilder.service';

import { PreEnrollmentTransactionStatusTHMapperEnum } from '@constants/enums/preEnrollmentTransaction.enum';
import {
  KeyColumnHeaderOICPreReportEnum,
  KeyColumnOICPreReportEnum,
  ReportLicenseTypeTHEnum,
} from '@constants/enums/report.enum';
import {
  CourseIdSubjectCodeKey,
  InputOICPreReportResourceMappingDataParams,
  InputOICReportFilterMatchingRelationalDataParams,
  InputOICReportFilterMatchingResourceRelationalDataParams,
  InputOICReportFilterRegistrationCourseRelationalDataParams,
  InputOICReportPreEnrollmentTransactionParams,
  KeyColumnHeaderOICPreReportParams,
  KeyColumnHeaderOICRegulatorPreReportParams,
  OICPreReportDataParams,
  OICPreReportOptionParams,
  OICRegulatorPreReportDataParams,
  OICRegulatorReportSubjectValuesParams,
  OICReportCourseSubjectCodeNameParams,
  OICReportSubjectNameParams,
} from '@constants/types/reportHistory.type';

import { IOICPreReportService } from '@interfaces/services/report.service.interface';

import { date } from '@domains/utils/date.util';

@Injectable()
export class OICPreReportService implements IOICPreReportService {
  filterMatchingRelationalData(
    preEnrollmentTransaction: InputOICReportFilterMatchingRelationalDataParams,
    relationalData: InputOICReportFilterMatchingResourceRelationalDataParams,
  ): boolean {
    const { id, preEnrollmentReservationId, enrollBy } = preEnrollmentTransaction;
    const { preEnrollmentReservationById, enrollmentRegulatorReportByPreEnrollmentTransactionId } = relationalData;
    const isPreEnrollmentReservationExist = preEnrollmentReservationById.has(preEnrollmentReservationId);
    const isEnrollmentRegulatorReportExist = enrollmentRegulatorReportByPreEnrollmentTransactionId.has(id);
    const isSelfEnrollment = enrollBy === PreEnrollmentTransactionEnrollByEnum.SELF;

    const isValidPreEnrollment = (isPreEnrollmentReservationExist && !isSelfEnrollment) || isSelfEnrollment;

    return isValidPreEnrollment && isEnrollmentRegulatorReportExist;
  }

  filterRegistrationCourse(
    preEnrollmentTransaction: InputOICReportFilterMatchingRelationalDataParams,
    relationalData: InputOICReportFilterRegistrationCourseRelationalDataParams,
  ): boolean {
    const { id, payload } = preEnrollmentTransaction;
    const {
      enrollmentRegulatorReportByPreEnrollmentTransactionId,
      courseById,
      reportLicenseType,
      latestPublishCourseVersionsByCourseId,
    } = relationalData;
    // Check Course Renew 4 && Both
    const enrollmentRegulatorReport = enrollmentRegulatorReportByPreEnrollmentTransactionId.get(id);

    const course = courseById.get(enrollmentRegulatorReport.courseId);
    if (!course) {
      return false;
    }

    const isCourseVersionExist = latestPublishCourseVersionsByCourseId.has(course.id);
    if (!isCourseVersionExist) {
      return false;
    }

    const { oicExtendYearType } = payload;

    const isBoth = oicExtendYearType === LicenseTypeEnum.BOTH;
    const isRenew4 = course.regulatorInfo.licenseRenewal === LicenseRenewalEnum.RENEW4;

    if (isRenew4 && !isNull(reportLicenseType)) {
      return isBoth || reportLicenseType === oicExtendYearType;
    }

    return true;
  }

  buildOicSubjectRowDataMapByKey(
    courseSubjectBySubjectCode: Map<string, CourseSubjectParams>,
    uniqueSubjectCodes: string[],
    subjectCodeNameMapByKey: Map<string, OICReportCourseSubjectCodeNameParams>,
    courseId: GenericID,
  ): Map<CourseIdSubjectCodeKey, OICRegulatorReportSubjectValuesParams> {
    const result = uniqueSubjectCodes.reduce((accumulate, subjectCode) => {
      const key: CourseIdSubjectCodeKey = `${courseId}_${subjectCode}`;
      const subjectCodeValue = subjectCodeNameMapByKey.get(key);

      if (subjectCodeValue) {
        const isSubjectExistInCourse = courseSubjectBySubjectCode.has(subjectCodeValue.subjectCode);
        accumulate.set(key, {
          subjectExistInCourse: isSubjectExistInCourse ? 'Y' : '',
          trainingDuration: isSubjectExistInCourse
            ? this.convertStringNumber(subjectCodeValue.trainingDuration, 2)
            : '',
        });
      }

      return accumulate;
    }, new Map<CourseIdSubjectCodeKey, OICRegulatorReportSubjectValuesParams>());
    return result;
  }

  buildOicPreReportRowData(
    preEnrollmentTransaction: InputOICReportPreEnrollmentTransactionParams,
    resource: InputOICPreReportResourceMappingDataParams,
  ): OICPreReportDataParams {
    const {
      organizationMainUrl,
      preEnrollmentReservationById,
      roundById,
      enrollmentRegulatorReportByPreEnrollmentTransactionId,
      subjectRegulatorCourseById,
      latestPublishCourseVersionsByCourseId,
      reportLicenseType,
    } = resource;

    const enrollmentRegulatorReport = enrollmentRegulatorReportByPreEnrollmentTransactionId.get(
      preEnrollmentTransaction.id,
    );

    const preEnrollmentReservation = preEnrollmentReservationById.get(
      preEnrollmentTransaction.preEnrollmentReservationId,
    );

    const round = roundById.get(preEnrollmentTransaction.roundId);

    const subjectRegulatorCourse = subjectRegulatorCourseById.get(enrollmentRegulatorReport.courseId);
    const latestPublishCourseVersion = latestPublishCourseVersionsByCourseId.get(subjectRegulatorCourse.id);

    const isFCmd = [PreEnrollmentTransactionStatusEnum.PASSED, PreEnrollmentTransactionStatusEnum.APPLIED].includes(
      preEnrollmentTransaction.status,
    );

    const { regulatorInfo } = subjectRegulatorCourse;

    const licenseNo =
      reportLicenseType === LicenseTypeEnum.LIFE
        ? preEnrollmentTransaction.payload.oicLicenseLifeNo || preEnrollmentTransaction.payload.oicLicenseNonLifeNo
        : preEnrollmentTransaction.payload.oicLicenseNonLifeNo;

    const { payload } = preEnrollmentTransaction;
    const isRenew4 = subjectRegulatorCourse.regulatorInfo.licenseRenewal === LicenseRenewalEnum.RENEW4;

    let licenseType: string;
    if (isRenew4) {
      licenseType = ReportLicenseTypeTHEnum[payload.oicExtendYearType] ?? '';
    } else {
      const isBothRegulatorLicense =
        regulatorInfo.licenseType.includes(LicenseTypeEnum.LIFE) &&
        regulatorInfo.licenseType.includes(LicenseTypeEnum.NONLIFE);
      licenseType = isBothRegulatorLicense
        ? ReportLicenseTypeTHEnum.BOTH
        : ReportLicenseTypeTHEnum[regulatorInfo.licenseType[0]];
    }
    const regulatorPrefix = payload?.regulatorProfile?.prefix || payload.prefix || '';
    return {
      eid: enrollmentRegulatorReport.id.toString(),
      fCmd: isFCmd ? '[1.1.N.0.0]-ADD' : '',
      item: 0,
      agLicense: licenseNo ?? '',
      agIdCard: payload.citizenId,
      prefix: regulatorPrefix.replace('น.ส.', 'นางสาว').trim(),
      agFNameTH: payload?.regulatorProfile?.firstname || payload.firstname,
      agLNameTH: payload?.regulatorProfile?.lastname || payload.lastname,
      fDeduction: regulatorInfo.isDeduct ? 'Y' : 'N',
      remark: payload.remark,
      status: PreEnrollmentTransactionStatusTHMapperEnum[preEnrollmentTransaction.status] ?? '',
      email: payload.email,
      phoneNumber: payload.mobile,
      customerCode: preEnrollmentReservation?.customerCode ?? '',
      round: round ? date(round.roundDate).format(DateFormat.buddhistDayMonthYearWithLeadingZero) : '',
      courseCode: payload.courseCode || payload.productSKUCode,
      courseName: latestPublishCourseVersion?.name || '',
      salute: (payload.prefix || '').replace('น.ส.', 'นางสาว').trim(),
      firstName: payload.firstname,
      lastName: payload.lastname,
      oicLifeLicenseNo: payload.oicLicenseLifeNo,
      oicLifeLicenseStartAt: payload.oicLifeStartDate,
      oicLifeLicenseExpiredAt: payload.oicLifeEndDate,
      oicNonLifeLicenseNo: payload.oicLicenseNonLifeNo,
      oicNonLifeStartAt: payload.oicNonLifeStartDate,
      oicNonLifeExpiredAt: payload.oicNonLifeEndDate,
      applicantType: ApplicantTypeTHEnum[regulatorInfo.applicantType],
      licenseRenewalType: LicenseRenewalTHEnum[regulatorInfo.licenseRenewal] ?? '',
      licenseType,
      haveCost: preEnrollmentTransaction?.contentItems[0]?.isHavingCost ? 'มีต้นทุน' : 'ไม่มีต้นทุน',
      regulatorCheck: preEnrollmentTransaction.isCheckRegulator ? 'ได้รับการตรวจสอบ' : 'ไม่ได้รับการตรวจสอบ',
      link: `${organizationMainUrl}/admin/waitingToEnroll/${preEnrollmentTransaction.id}`,
    };
  }

  async buildOicRegulatorPreReportFile(
    props: OICRegulatorPreReportDataParams[],
    options?: OICPreReportOptionParams,
  ): Promise<ArrayBuffer> {
    const { isShowCustomer, isShowHaveCost, isShowLifeLicense, isShowNonLifeLicense, subjectCodeNames } =
      options ?? this.defaultOicPreReportOptions();
    const subjectHeaders = uniq(subjectCodeNames.map((subjectCodeName) => subjectCodeName.subjectCode));
    const subjectHeaderNumbers: OICReportSubjectNameParams[] = subjectHeaders.map((_, i) => {
      const id = BigInt(i + 1);
      return `SUBJECT${id}` as OICReportSubjectNameParams;
    });
    const headerNamesColumns: KeyColumnHeaderOICRegulatorPreReportParams[] = (
      [
        KeyColumnHeaderOICPreReportEnum.HEADER,
        KeyColumnHeaderOICPreReportEnum.EID,
        KeyColumnHeaderOICPreReportEnum.F_CMD,
        KeyColumnHeaderOICPreReportEnum.ITEM,
        KeyColumnHeaderOICPreReportEnum.AG_LICENSE,
        KeyColumnHeaderOICPreReportEnum.AG_ID_CARD,
        KeyColumnHeaderOICPreReportEnum.PREFIX_CODE,
        KeyColumnHeaderOICPreReportEnum.AG_FNAME_TH,
        KeyColumnHeaderOICPreReportEnum.AG_LNAME_TH,
        KeyColumnHeaderOICPreReportEnum.F_DEDUCTION,
        KeyColumnHeaderOICPreReportEnum.REMARK,
      ] as KeyColumnHeaderOICRegulatorPreReportParams[]
    )
      .concat(subjectHeaders)
      .concat([
        KeyColumnHeaderOICPreReportEnum.STATUS,
        KeyColumnHeaderOICPreReportEnum.EMAIL,
        KeyColumnHeaderOICPreReportEnum.PHONE_NUMBER,
      ])
      .concat(isShowCustomer ? [KeyColumnHeaderOICPreReportEnum.CUSTOMER_CODE] : [])
      .concat([
        KeyColumnHeaderOICPreReportEnum.ROUND,
        KeyColumnHeaderOICPreReportEnum.COURSE_CODE,
        KeyColumnHeaderOICPreReportEnum.COURSE_NAME,
        KeyColumnHeaderOICPreReportEnum.SALUTE,
        KeyColumnHeaderOICPreReportEnum.FIRST_NAME,
        KeyColumnHeaderOICPreReportEnum.LAST_NAME,
      ])
      .concat(
        isShowLifeLicense && [
          KeyColumnHeaderOICPreReportEnum.OIC_LIFE_LICENSE_NO,
          KeyColumnHeaderOICPreReportEnum.OIC_LIFE_LICENSE_START_AT,
          KeyColumnHeaderOICPreReportEnum.OIC_LIFE_LICENSE_EXPIRED_AT,
        ],
      )
      .concat(
        isShowNonLifeLicense && [
          KeyColumnHeaderOICPreReportEnum.OIC_NON_LIFE_LICENSE_NO,
          KeyColumnHeaderOICPreReportEnum.OIC_NON_LIFE_LICENSE_START_AT,
          KeyColumnHeaderOICPreReportEnum.OIC_NON_LIFE_LICENSE_EXPIRED_AT,
        ],
      )
      .concat([
        KeyColumnHeaderOICPreReportEnum.APPLICANT_TYPE,
        KeyColumnHeaderOICPreReportEnum.LICENSE_RENEWAL_TYPE,
        KeyColumnHeaderOICPreReportEnum.LICENSE_TYPE,
      ])
      .concat(isShowHaveCost ? [KeyColumnHeaderOICPreReportEnum.HAVING_COST] : [])
      .concat([KeyColumnHeaderOICPreReportEnum.IS_CHECK_REGULATOR, KeyColumnHeaderOICPreReportEnum.LINK])
      .filter(Boolean);

    const rawData = props.map((data) => {
      const subjects = subjectHeaders.map((subjectCodeName) => {
        const key: CourseIdSubjectCodeKey = `${data.courseId}_${subjectCodeName}`;
        return data[key]?.subjectExistInCourse ?? '';
      });
      return [
        '1.1.N.0.0',
        data.eid,
        data.fCmd,
        data.item,
        data.agLicense,
        data.agIdCard,
        data.prefix,
        data.agFNameTH,
        data.agLNameTH,
        data.fDeduction,
        data.remark,
      ]
        .concat(subjects)
        .concat([data.status, data.email, data.phoneNumber])
        .concat(isShowCustomer ? [data.customerCode] : [])
        .concat([data.round, data.courseCode, data.courseName, data.salute, data.firstName, data.lastName])
        .concat(
          isShowLifeLicense ? [data.oicLifeLicenseNo, data.oicLifeLicenseStartAt, data.oicLifeLicenseExpiredAt] : [],
        )
        .concat(
          isShowNonLifeLicense ? [data.oicNonLifeLicenseNo, data.oicNonLifeStartAt, data.oicNonLifeExpiredAt] : [],
        )
        .concat([data.applicantType, data.licenseRenewalType, data.licenseType])
        .concat(isShowHaveCost ? [data.haveCost] : [])
        .concat([data.regulatorCheck, data.link]);
    });
    const WORKSHEET_NAME = 'Sheet1';
    const excelBuilderService = new ExcelBuilderService();
    const lineSpaces = Array.from({ length: headerNamesColumns.length }, () => '');
    const rowsData = [this.headerTitleOICPreReport(subjectHeaderNumbers), lineSpaces, headerNamesColumns, ...rawData];

    const startRowBorder = 3;
    const startColumnBorder = 2;
    const startRowAlignment = 3;
    const startColumnAlignment = 1;
    const startRowSubjectAlignment = 4;
    const startColumnSubjectAlignment = 12;
    const startRowDeductAlignment = 3;
    const startColumnDeductAlignment = 10;

    excelBuilderService
      .addWorksheet(WORKSHEET_NAME)
      .addRows(WORKSHEET_NAME, rowsData)
      .setCellOuterBorder(
        WORKSHEET_NAME,
        { row: startRowBorder, col: startColumnBorder },
        { row: startRowBorder + rawData.length, col: headerNamesColumns.length },
      )
      .setCellAlignment(
        WORKSHEET_NAME,
        { row: startRowAlignment, col: startColumnAlignment },
        { row: startRowAlignment + rawData.length, col: startColumnAlignment + headerNamesColumns.length },
        { vertical: 'middle', horizontal: 'left' },
      )
      .setCellAlignment(
        WORKSHEET_NAME,
        { row: startRowSubjectAlignment, col: startColumnSubjectAlignment },
        {
          row: startRowSubjectAlignment + rawData.length,
          col: startColumnSubjectAlignment + subjectHeaderNumbers.length,
        },
        { vertical: 'middle', horizontal: 'center' },
      )
      .setCellAlignment(
        WORKSHEET_NAME,
        { row: startRowDeductAlignment, col: startColumnDeductAlignment },
        { row: startRowDeductAlignment + rawData.length, col: startColumnDeductAlignment },
        { vertical: 'middle', horizontal: 'center' },
      )
      .setAppearanceResizeWidth(WORKSHEET_NAME, { offset: 3 });

    const buffer = await excelBuilderService.getFileBuffer();
    return buffer;
  }

  async buildOICPreReportFile(
    props: OICPreReportDataParams[],
    options?: OICPreReportOptionParams,
  ): Promise<ArrayBuffer> {
    const { isShowCustomer, isShowHaveCost, isShowLifeLicense, isShowNonLifeLicense } =
      options ?? this.defaultOicPreReportOptions();
    const headerNamesColumns = (
      [
        KeyColumnHeaderOICPreReportEnum.HEADER,
        KeyColumnHeaderOICPreReportEnum.EID,
        KeyColumnHeaderOICPreReportEnum.F_CMD,
        KeyColumnHeaderOICPreReportEnum.ITEM,
        KeyColumnHeaderOICPreReportEnum.AG_LICENSE,
        KeyColumnHeaderOICPreReportEnum.AG_ID_CARD,
        KeyColumnHeaderOICPreReportEnum.PREFIX_CODE,
        KeyColumnHeaderOICPreReportEnum.AG_FNAME_TH,
        KeyColumnHeaderOICPreReportEnum.AG_LNAME_TH,
        KeyColumnHeaderOICPreReportEnum.F_DEDUCTION,
        KeyColumnHeaderOICPreReportEnum.REMARK,
        KeyColumnHeaderOICPreReportEnum.STATUS,
        KeyColumnHeaderOICPreReportEnum.EMAIL,
        KeyColumnHeaderOICPreReportEnum.PHONE_NUMBER,
      ] as KeyColumnHeaderOICPreReportParams[]
    )
      .concat(isShowCustomer && [KeyColumnHeaderOICPreReportEnum.CUSTOMER_CODE])
      .concat([
        KeyColumnHeaderOICPreReportEnum.ROUND,
        KeyColumnHeaderOICPreReportEnum.COURSE_CODE,
        KeyColumnHeaderOICPreReportEnum.COURSE_NAME,
        KeyColumnHeaderOICPreReportEnum.SALUTE,
        KeyColumnHeaderOICPreReportEnum.FIRST_NAME,
        KeyColumnHeaderOICPreReportEnum.LAST_NAME,
      ])
      .concat(
        isShowLifeLicense && [
          KeyColumnHeaderOICPreReportEnum.OIC_LIFE_LICENSE_NO,
          KeyColumnHeaderOICPreReportEnum.OIC_LIFE_LICENSE_START_AT,
          KeyColumnHeaderOICPreReportEnum.OIC_LIFE_LICENSE_EXPIRED_AT,
        ],
      )
      .concat(
        isShowNonLifeLicense && [
          KeyColumnHeaderOICPreReportEnum.OIC_NON_LIFE_LICENSE_NO,
          KeyColumnHeaderOICPreReportEnum.OIC_NON_LIFE_LICENSE_START_AT,
          KeyColumnHeaderOICPreReportEnum.OIC_NON_LIFE_LICENSE_EXPIRED_AT,
        ],
      )
      .concat([
        KeyColumnHeaderOICPreReportEnum.APPLICANT_TYPE,
        KeyColumnHeaderOICPreReportEnum.LICENSE_RENEWAL_TYPE,
        KeyColumnHeaderOICPreReportEnum.LICENSE_TYPE,
      ])
      .concat(isShowHaveCost ? [KeyColumnHeaderOICPreReportEnum.HAVING_COST] : [])
      .concat([KeyColumnHeaderOICPreReportEnum.IS_CHECK_REGULATOR, KeyColumnHeaderOICPreReportEnum.LINK])
      .filter(Boolean);

    const rawData = props.map((data) =>
      [
        '1.1.N.0.0',
        data.eid,
        data.fCmd,
        data.item,
        data.agLicense,
        data.agIdCard,
        data.prefix,
        data.agFNameTH,
        data.agLNameTH,
        data.fDeduction,
        data.remark,
        data.status,
        data.email,
        data.phoneNumber,
      ]
        .concat(isShowCustomer ? [data.customerCode] : [])
        .concat([data.round, data.courseCode, data.courseName, data.salute, data.firstName, data.lastName])
        .concat(
          isShowLifeLicense ? [data.oicLifeLicenseNo, data.oicLifeLicenseStartAt, data.oicLifeLicenseExpiredAt] : [],
        )
        .concat(
          isShowNonLifeLicense ? [data.oicNonLifeLicenseNo, data.oicNonLifeStartAt, data.oicNonLifeExpiredAt] : [],
        )
        .concat([data.applicantType, data.licenseRenewalType, data.licenseType])
        .concat(isShowHaveCost ? [data.haveCost] : [])
        .concat([data.regulatorCheck, data.link]),
    );

    const WORKSHEET_NAME = 'Sheet1';
    const excelBuilderService = new ExcelBuilderService();

    const lineSpaces = Array.from({ length: headerNamesColumns.length }, () => '');
    const rowsData = [this.headerTitleOICPreReport([]), lineSpaces, headerNamesColumns, ...rawData];

    const startRowBorder = 3;
    const startColumnBorder = 2;
    const startRowAlignment = 3;
    const startColumnAlignment = 1;
    const startRowDeductAlignment = 4;
    const startColumnDeductAlignment = 10;

    excelBuilderService
      .addWorksheet(WORKSHEET_NAME)
      .addRows(WORKSHEET_NAME, rowsData)
      .setCellOuterBorder(
        WORKSHEET_NAME,
        { row: startRowBorder, col: startColumnBorder },
        { row: startRowBorder + rawData.length, col: headerNamesColumns.length },
      )
      .setCellAlignment(
        WORKSHEET_NAME,
        { row: startRowAlignment, col: startColumnAlignment },
        { row: startRowAlignment + rawData.length, col: startColumnAlignment + headerNamesColumns.length },
        { vertical: 'middle', horizontal: 'left' },
      )
      .setCellAlignment(
        WORKSHEET_NAME,
        { row: startRowDeductAlignment, col: startColumnDeductAlignment },
        { row: startRowDeductAlignment + rawData.length, col: startColumnDeductAlignment },
        { vertical: 'middle', horizontal: 'center' },
      )
      .setAppearanceResizeWidth(WORKSHEET_NAME, { offset: 3 });

    const buffer = await excelBuilderService.getFileBuffer();
    return buffer;
  }

  private defaultOicPreReportOptions(): OICPreReportOptionParams {
    return {
      isShowCustomer: true,
      isShowHaveCost: true,
      isShowLifeLicense: true,
      isShowNonLifeLicense: true,
      subjectCodeNames: [],
    };
  }

  private headerTitleOICPreReport(subjects: OICReportSubjectNameParams[]): string[] {
    return [
      '',
      '',
      KeyColumnOICPreReportEnum.F_CMD,
      KeyColumnOICPreReportEnum.ITEM,
      KeyColumnOICPreReportEnum.AG_LICENSE,
      KeyColumnOICPreReportEnum.AG_ID_CARD,
      KeyColumnOICPreReportEnum.PREFIX_CODE,
      KeyColumnOICPreReportEnum.AG_FNAME_TH,
      KeyColumnOICPreReportEnum.AG_LNAME_TH,
      KeyColumnOICPreReportEnum.F_DEDUCTION,
      KeyColumnOICPreReportEnum.REMARK,
    ].concat(subjects);
  }

  private convertStringNumber(numStr?: string, decimal: number = 2): string {
    if (!numStr || numStr.trim() === '') {
      return '';
    }

    const num = parseFloat(numStr.trim());

    if (isNaN(num)) {
      return '';
    }

    return (Math.round(num * 100) / 100).toFixed(decimal);
  }
}
