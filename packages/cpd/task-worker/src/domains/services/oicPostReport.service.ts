import { GenericID, Nullable } from '@iso/constants/commonTypes';
import { DateFormat, TimeZoneEnum } from '@iso/helpers/dateUtils';
import { LicenseRenewalEnum, LicenseRenewalTHEnum, LicenseTypeEnum } from '@iso/lms/enums/course.enum';
import { EnrollmentStatusEnum, EnrollmentStatusTextEnum } from '@iso/lms/enums/enrollment.enum';
import { EnrollmentAttachmentTypeEnum } from '@iso/lms/enums/enrollmentAttachment.enum';
import { ApplicantTypeTHEnum, UserLicenseTypeCodeEnum } from '@iso/lms/enums/license.enum';
import { EnrollmentAttachment } from '@iso/lms/models/enrollmentAttachment.model';
import { EnrollmentCertificate } from '@iso/lms/models/enrollmentCertificate.model';
import { CourseSubjectParams } from '@iso/lms/types/course.type';
import { PreEnrollmentTransactionPayloadParams } from '@iso/lms/types/preEnrollmentTransaction.type';
import { Injectable } from '@nestjs/common';
import { isNil, isNull, partition, uniq } from 'lodash';

import { ExcelBuilderService } from '@infrastructures/services/files/excelBuilder.service';

import { EnrollmentAttachmentStatusTextEnum } from '@constants/enums/enrollmentAttachment.enum';
import {
  KeyColumnHeaderOICPostReportEnum,
  KeyColumnOICPreReportEnum,
  ReportLicenseTypeTHEnum,
} from '@constants/enums/report.enum';
import {
  EnrollmentPayloadOfBuildOicPostReportParams,
  OutputFindEnrollmentPayloadOfOicReportParams,
} from '@constants/types/enrollment.type';
import {
  CourseIdSubjectCodeKey,
  InputOICPostReportFilterMatchingResourceRelationalDataParams,
  InputOICPostReportResourceMappingDataParams,
  KeyColumnHeaderOICPostReportParams,
  KeyColumnHeaderOICRegulatorPostReportParams,
  OICPostReportDataParams,
  OICPostReportOptionParams,
  OICRegulatorPostReportDataParams,
  OICRegulatorReportSubjectValuesParams,
  OICReportCourseSubjectCodeNameParams,
  OICReportSubjectNameParams,
} from '@constants/types/reportHistory.type';

import { IOICPostReportService } from '@interfaces/services/report.service.interface';

import { date, formatDateInThaiLocale } from '@domains/utils/date.util';

@Injectable()
export class OICPostReportService implements IOICPostReportService {
  buildOicSubjectRowDataMapByKey(
    courseSubjectBySubjectCode: Map<string, CourseSubjectParams>,
    uniqueSubjectCodes: string[],
    subjectCodeNameMapByKey: Map<string, OICReportCourseSubjectCodeNameParams>,
    courseId: GenericID,
  ): Map<CourseIdSubjectCodeKey, OICRegulatorReportSubjectValuesParams> {
    const result = uniqueSubjectCodes.reduce((accumulate, subjectCode) => {
      const key: CourseIdSubjectCodeKey = `${courseId}_${subjectCode}`;
      const subjectCodeValue = subjectCodeNameMapByKey.get(key);

      if (subjectCodeValue) {
        const isSubjectExistInCourse = courseSubjectBySubjectCode.has(subjectCodeValue.subjectCode);
        accumulate.set(key, {
          subjectExistInCourse: isSubjectExistInCourse ? 'Y' : '',
          trainingDuration: isSubjectExistInCourse
            ? this.convertStringNumber(subjectCodeValue.trainingDuration, 2)
            : '',
        });
      }

      return accumulate;
    }, new Map<CourseIdSubjectCodeKey, OICRegulatorReportSubjectValuesParams>());
    return result;
  }

  filterEnrollmentRelationalData(
    enrollment: OutputFindEnrollmentPayloadOfOicReportParams,
    relationalData: InputOICPostReportFilterMatchingResourceRelationalDataParams,
  ): boolean {
    const { preEnrollmentTransaction } = enrollment;
    const { courseById, courseVersionByCourseId, reportLicenseType, enrollmentCertificateGroupByEnrollmentId } =
      relationalData;
    // Check Course Renew 4 && Both

    const course = courseById.get(enrollment.courseId);
    if (!course?.id) return false;

    const courseVersion = courseVersionByCourseId.get(course.id);
    if (!courseVersion?.id) return false;

    const isManualEnrollment = isNull(preEnrollmentTransaction);

    const isRenew4 = course.regulatorInfo.licenseRenewal === LicenseRenewalEnum.RENEW4;

    if (isManualEnrollment) {
      if (isRenew4 && !isNil(reportLicenseType)) {
        const enrollmentCertificate = enrollmentCertificateGroupByEnrollmentId[enrollment.id] || [];
        const licenseTypeByEnrollmentCertificate = this.licenseTypeByEnrollmentCertificates(
          enrollmentCertificate,
          course.regulatorInfo.licenseType,
          course.regulatorInfo.licenseRenewal,
        );

        return reportLicenseType === licenseTypeByEnrollmentCertificate;
      }
    } else {
      if (!preEnrollmentTransaction?.payload) {
        return false;
      }

      if (isRenew4 && !isNil(reportLicenseType)) {
        const { oicExtendYearType } = preEnrollmentTransaction.payload;

        return reportLicenseType === oicExtendYearType;
      }
    }

    return true;
  }

  buildOicPostReportRowData(
    enrollment: EnrollmentPayloadOfBuildOicPostReportParams,
    resource: InputOICPostReportResourceMappingDataParams,
  ): Nullable<OICPostReportDataParams> {
    const {
      organizationMainUrl,
      roundById,
      subjectRegulatorCourseById,
      latestPublishCourseVersionsByCourseId,
      enrollmentAttachmentGroupByEnrollmentId,
      userByUserId,
    } = resource;

    const {
      enrollmentRegulatorReport,
      preEnrollmentTransaction,
      resultAveragePostTestScore,
      percentCompleteCourseItems,
      licenseTypeByEnrollmentCertificate,
    } = enrollment;
    const { isHavePostTestQuiz, percentPostTestScore } = resultAveragePostTestScore;

    const round = roundById.get(enrollment.roundId);

    const subjectRegulatorCourse = subjectRegulatorCourseById.get(enrollment.courseId);
    if (!subjectRegulatorCourse?.id) {
      return null;
    }

    const latestPublishCourseVersion = latestPublishCourseVersionsByCourseId.get(subjectRegulatorCourse.id);
    if (!latestPublishCourseVersion?.id) {
      return null;
    }

    const enrollmentAttachments = enrollmentAttachmentGroupByEnrollmentId[enrollment.id];
    const [enrollmentAttachmentAdditional, enrollmentAttachmentDeduct] = partition(
      enrollmentAttachments,
      (attachment) => attachment.fileType === EnrollmentAttachmentTypeEnum.ADDITIONAL,
    );

    const { regulatorInfo } = subjectRegulatorCourse;
    const { licenseType: licenseCourseType, licenseRenewal: licenseCourseRenewal } = regulatorInfo;

    const isFCmd = this.isFCmd(enrollment.status, licenseCourseType, licenseCourseRenewal);

    const isRenew4 = subjectRegulatorCourse.regulatorInfo.licenseRenewal === LicenseRenewalEnum.RENEW4;

    const hasLifeLicenseCourse = regulatorInfo.licenseType.includes(LicenseTypeEnum.LIFE);
    const hasNonLifeLicenseCourse = regulatorInfo.licenseType.includes(LicenseTypeEnum.NONLIFE);
    const hasUKLicenseCourse = regulatorInfo.licenseType.includes(LicenseTypeEnum.UNIT_LINKED);
    const isBothLicenseCourse = hasLifeLicenseCourse && hasNonLifeLicenseCourse;
    const isLife = hasLifeLicenseCourse && !isBothLicenseCourse;
    const isNonLife = hasNonLifeLicenseCourse && !isBothLicenseCourse;
    const isUK = hasUKLicenseCourse && !isBothLicenseCourse;

    let licenseNo = '';
    let licenseType: string;
    let prefix: string;
    let agFNameTH: string;
    let agLNameTH: string;
    let agIdCard: string;
    let email: string;
    let phoneNumber: string;
    let courseCode: string;
    let salute: string;
    let firstName: string;
    let lastName: string;
    let oicLifeLicenseNo: string;
    let oicLifeLicenseStartAt: string;
    let oicLifeLicenseExpiredAt: string;
    let oicNonLifeLicenseNo: string;
    let oicNonLifeStartAt: string;
    let oicNonLifeExpiredAt: string;

    const isManualEnrollment = isNull(preEnrollmentTransaction);

    if (!isManualEnrollment) {
      const { payload } = preEnrollmentTransaction;

      licenseNo = this.getLicenseNo(payload, isLife, isUK, isNonLife, isBothLicenseCourse);

      if (isRenew4) {
        licenseType = ReportLicenseTypeTHEnum[payload.oicExtendYearType] ?? '';
      } else {
        licenseType = isBothLicenseCourse
          ? ReportLicenseTypeTHEnum.BOTH
          : ReportLicenseTypeTHEnum[regulatorInfo.licenseType[0]];
      }

      prefix = payload?.regulatorProfile?.prefix || payload?.prefix || '';
      agFNameTH = payload?.regulatorProfile?.firstname || payload?.firstname || '';
      agLNameTH = payload?.regulatorProfile?.lastname || payload?.lastname || '';
      agIdCard = payload?.citizenId || '';
      email = payload?.email || '';
      phoneNumber = payload?.mobile || '';
      courseCode = payload?.courseCode || payload?.productSKUCode || '';
      salute = payload?.prefix ? payload?.prefix : '';
      firstName = payload?.firstname || '';
      lastName = payload?.lastname || '';
      oicLifeLicenseNo = payload?.oicLicenseLifeNo || '';
      oicLifeLicenseStartAt = payload?.oicLifeStartDate || '';
      oicLifeLicenseExpiredAt = payload?.oicLifeEndDate || '';
      oicNonLifeLicenseNo = payload?.oicLicenseNonLifeNo || '';
      oicNonLifeStartAt = payload?.oicNonLifeStartDate || '';
      oicNonLifeExpiredAt = payload?.oicNonLifeEndDate || '';
    } else {
      const user = userByUserId.get(enrollment.userId);
      const licenses = user?.licenses || [];

      const oicLifeLicense = licenses.find((license) => license.licenseTypeCode === UserLicenseTypeCodeEnum.OIC_LIFE);
      const oicNonLifeLicense = licenses.find(
        (license) => license.licenseTypeCode === UserLicenseTypeCodeEnum.OIC_NON_LIFE,
      );

      licenseNo = this.getLicenseNoManualEnrollment({ licenses, isLife, isUK, isNonLife, isBothLicenseCourse });

      if (isRenew4) {
        licenseType = ReportLicenseTypeTHEnum[licenseTypeByEnrollmentCertificate] ?? '';
      } else {
        licenseType = isBothLicenseCourse
          ? ReportLicenseTypeTHEnum.BOTH
          : ReportLicenseTypeTHEnum[regulatorInfo.licenseType[0]];
      }

      prefix = user?.profile.salute || '';
      agFNameTH = user?.profile.firstname;
      agLNameTH = user?.profile.lastname;
      agIdCard = user?.citizenId || '';
      email = user?.email || '';
      phoneNumber = user?.profile.mobilePhoneNumber || '';
      courseCode = subjectRegulatorCourse?.code || '';
      salute = user?.profile.salute ? user?.profile.salute : '';
      firstName = user?.profile.firstname;
      lastName = user?.profile.lastname;
      oicLifeLicenseNo = oicLifeLicense?.licenseNo || '';
      oicLifeLicenseStartAt = this.covertFormatDateInThaiLocale(oicLifeLicense?.startedAt);
      oicLifeLicenseExpiredAt = this.covertFormatDateInThaiLocale(oicLifeLicense?.expiredAt);
      oicNonLifeLicenseNo = oicNonLifeLicense?.licenseNo || '';
      oicNonLifeStartAt = this.covertFormatDateInThaiLocale(oicNonLifeLicense?.startedAt);
      oicNonLifeExpiredAt = this.covertFormatDateInThaiLocale(oicNonLifeLicense?.expiredAt);
    }

    return {
      eid: enrollmentRegulatorReport?.id?.toString() || '',
      fCmd: isFCmd ? '[1.1.N.0.0]-ADD' : '',
      item: 0,
      agLicense: licenseNo,
      agIdCard,
      prefix: prefix === 'น.ส.' ? 'นางสาว' : prefix,
      agFNameTH,
      agLNameTH,
      fDeduction: regulatorInfo.isDeduct ? 'Y' : 'N',
      remark: this.isPassed(enrollment.status) ? 'P' : '',
      status: EnrollmentStatusTextEnum[enrollment.status],
      additionalDeductStatus: this.getEnrollmentAttachmentsStatusText(enrollmentAttachmentDeduct),
      additionalAttachmentStatus: this.getEnrollmentAttachmentsStatusText(enrollmentAttachmentAdditional),
      email,
      phoneNumber,
      round: round ? date(round.roundDate).format(DateFormat.buddhistDayMonthYearWithLeadingZero) : '',
      courseCode,
      courseName: latestPublishCourseVersion?.name || '',
      customerCode: enrollment?.customerCode ?? '',
      salute: salute === 'น.ส.' ? 'นางสาว' : salute,
      firstName,
      lastName,
      oicLifeLicenseNo,
      oicLifeLicenseStartAt,
      oicLifeLicenseExpiredAt,
      oicNonLifeLicenseNo,
      oicNonLifeStartAt,
      oicNonLifeExpiredAt,
      applicantType: ApplicantTypeTHEnum[regulatorInfo.applicantType],
      licenseRenewalType: LicenseRenewalTHEnum[regulatorInfo.licenseRenewal] ?? '',
      licenseType,
      percentLearningProgress: `${percentCompleteCourseItems}%`,
      percentPostQuizScore: isHavePostTestQuiz ? `${percentPostTestScore}%` : '',
      link: `${organizationMainUrl}/admin/enrollments/${enrollment.id}`,
    };
  }

  async buildOICPostReportFile(
    props: OICPostReportDataParams[],
    options?: OICPostReportOptionParams,
  ): Promise<ArrayBuffer> {
    const { isShowCustomer, isShowLifeLicense, isShowNonLifeLicense } = options ?? this.defaultOicPostReportOptions();
    const headerNamesColumns = (
      [
        KeyColumnHeaderOICPostReportEnum.HEADER,
        KeyColumnHeaderOICPostReportEnum.EID,
        KeyColumnHeaderOICPostReportEnum.F_CMD,
        KeyColumnHeaderOICPostReportEnum.ITEM,
        KeyColumnHeaderOICPostReportEnum.AG_LICENSE,
        KeyColumnHeaderOICPostReportEnum.AG_ID_CARD,
        KeyColumnHeaderOICPostReportEnum.PREFIX_CODE,
        KeyColumnHeaderOICPostReportEnum.AG_FNAME_TH,
        KeyColumnHeaderOICPostReportEnum.AG_LNAME_TH,
        KeyColumnHeaderOICPostReportEnum.F_DEDUCTION,
        KeyColumnHeaderOICPostReportEnum.REMARK,
      ] as KeyColumnHeaderOICPostReportParams[]
    )
      .concat([
        KeyColumnHeaderOICPostReportEnum.ENROLLMENT_STATUS,
        KeyColumnHeaderOICPostReportEnum.ADDITIONAL_DEDUCT_STATUS,
        KeyColumnHeaderOICPostReportEnum.ADDITIONAL_ATTACHMENT_STATUS,
        KeyColumnHeaderOICPostReportEnum.EMAIL,
        KeyColumnHeaderOICPostReportEnum.PHONE_NUMBER,
      ])
      .concat(isShowCustomer && [KeyColumnHeaderOICPostReportEnum.CUSTOMER_CODE])
      .concat([
        KeyColumnHeaderOICPostReportEnum.ROUND,
        KeyColumnHeaderOICPostReportEnum.COURSE_CODE,
        KeyColumnHeaderOICPostReportEnum.COURSE_NAME,
        KeyColumnHeaderOICPostReportEnum.SALUTE,
        KeyColumnHeaderOICPostReportEnum.FIRST_NAME,
        KeyColumnHeaderOICPostReportEnum.LAST_NAME,
      ])
      .concat(
        isShowLifeLicense && [
          KeyColumnHeaderOICPostReportEnum.OIC_LIFE_LICENSE_NO,
          KeyColumnHeaderOICPostReportEnum.OIC_LIFE_LICENSE_START_AT,
          KeyColumnHeaderOICPostReportEnum.OIC_LIFE_LICENSE_EXPIRED_AT,
        ],
      )
      .concat(
        isShowNonLifeLicense && [
          KeyColumnHeaderOICPostReportEnum.OIC_NON_LIFE_LICENSE_NO,
          KeyColumnHeaderOICPostReportEnum.OIC_NON_LIFE_LICENSE_START_AT,
          KeyColumnHeaderOICPostReportEnum.OIC_NON_LIFE_LICENSE_EXPIRED_AT,
        ],
      )
      .concat([
        KeyColumnHeaderOICPostReportEnum.APPLICANT_TYPE,
        KeyColumnHeaderOICPostReportEnum.LICENSE_RENEWAL_TYPE,
        KeyColumnHeaderOICPostReportEnum.LICENSE_TYPE,
        KeyColumnHeaderOICPostReportEnum.PERCENT_LEARNING_PROGRESS,
        KeyColumnHeaderOICPostReportEnum.PERCENT_POST_QUIZ_SCORE,
        KeyColumnHeaderOICPostReportEnum.LINK,
      ])
      .filter(Boolean);

    const rawData = props.map((data) => {
      return [
        '1.1.N.0.0',
        data.eid,
        data.fCmd,
        data.item,
        data.agLicense,
        data.agIdCard,
        data.prefix,
        data.agFNameTH,
        data.agLNameTH,
        data.fDeduction,
        data.remark,
      ]
        .concat([
          data.status,
          data.additionalDeductStatus,
          data.additionalAttachmentStatus,
          data.email,
          data.phoneNumber,
        ])
        .concat(isShowCustomer ? [data.customerCode] : [])
        .concat([data.round, data.courseCode, data.courseName, data.salute, data.firstName, data.lastName])
        .concat(
          isShowLifeLicense ? [data.oicLifeLicenseNo, data.oicLifeLicenseStartAt, data.oicLifeLicenseExpiredAt] : [],
        )
        .concat(
          isShowNonLifeLicense ? [data.oicNonLifeLicenseNo, data.oicNonLifeStartAt, data.oicNonLifeExpiredAt] : [],
        )
        .concat([
          data.applicantType,
          data.licenseRenewalType,
          data.licenseType,
          data.percentLearningProgress,
          data.percentPostQuizScore,
          data.link,
        ]);
    });

    const WORKSHEET_NAME = 'Sheet1';
    const excelBuilderService = new ExcelBuilderService();

    const lineSpaces = Array.from({ length: headerNamesColumns.length }, () => '');
    const rowsData = [this.headerTitleOICPreReport([]), lineSpaces, headerNamesColumns, ...rawData];

    const startRowBorder = 3;
    const startColumnBorder = 2;
    const startRowAlignment = 3;
    const startColumnAlignment = 1;
    const startRowDeductAlignment = 4;
    const startColumnDeductAlignment = 10;
    const startRowRemarkAlignment = 4;
    const startColumnRemarkAlignment = 11;

    excelBuilderService
      .addWorksheet(WORKSHEET_NAME)
      .addRows(WORKSHEET_NAME, rowsData)
      .setCellOuterBorder(
        WORKSHEET_NAME,
        { row: startRowBorder, col: startColumnBorder },
        { row: startRowBorder + rawData.length, col: headerNamesColumns.length },
      )
      .setCellAlignment(
        WORKSHEET_NAME,
        { row: startRowAlignment, col: startColumnAlignment },
        { row: startRowAlignment + rawData.length, col: startColumnAlignment + headerNamesColumns.length },
        { vertical: 'middle', horizontal: 'left' },
      )
      .setCellAlignment(
        WORKSHEET_NAME,
        { row: startRowDeductAlignment, col: startColumnDeductAlignment },
        { row: startRowDeductAlignment + rawData.length, col: startColumnDeductAlignment },
        { vertical: 'middle', horizontal: 'center' },
      )
      .setCellAlignment(
        WORKSHEET_NAME,
        { row: startRowRemarkAlignment, col: startColumnRemarkAlignment },
        { row: startRowRemarkAlignment + rawData.length, col: startColumnRemarkAlignment },
        { vertical: 'middle', horizontal: 'center' },
      )
      .setAppearanceResizeWidth(WORKSHEET_NAME, { offset: 3 });

    const buffer = await excelBuilderService.getFileBuffer();
    return buffer;
  }

  async buildOicRegulatorPostReportFile(
    props: OICRegulatorPostReportDataParams[],
    options?: OICPostReportOptionParams,
  ): Promise<ArrayBuffer> {
    const { isShowCustomer, isShowLifeLicense, isShowNonLifeLicense, subjectCodeNames } =
      options ?? this.defaultOicPostReportOptions();
    const subjectHeaders = uniq(subjectCodeNames.map((subjectCodeName) => subjectCodeName.subjectCode));
    const subjectHeaderNumbers: OICReportSubjectNameParams[] = subjectHeaders.map((_, i) => {
      const id = BigInt(i + 1);
      return `SUBJECT${id}` as OICReportSubjectNameParams;
    });
    const headerNamesColumns: KeyColumnHeaderOICRegulatorPostReportParams[] = (
      [
        KeyColumnHeaderOICPostReportEnum.HEADER,
        KeyColumnHeaderOICPostReportEnum.EID,
        KeyColumnHeaderOICPostReportEnum.F_CMD,
        KeyColumnHeaderOICPostReportEnum.ITEM,
        KeyColumnHeaderOICPostReportEnum.AG_LICENSE,
        KeyColumnHeaderOICPostReportEnum.AG_ID_CARD,
        KeyColumnHeaderOICPostReportEnum.PREFIX_CODE,
        KeyColumnHeaderOICPostReportEnum.AG_FNAME_TH,
        KeyColumnHeaderOICPostReportEnum.AG_LNAME_TH,
        KeyColumnHeaderOICPostReportEnum.F_DEDUCTION,
        KeyColumnHeaderOICPostReportEnum.REMARK,
      ] as KeyColumnHeaderOICRegulatorPostReportParams[]
    )
      .concat(subjectHeaders)
      .concat([
        KeyColumnHeaderOICPostReportEnum.ENROLLMENT_STATUS,
        KeyColumnHeaderOICPostReportEnum.ADDITIONAL_DEDUCT_STATUS,
        KeyColumnHeaderOICPostReportEnum.ADDITIONAL_ATTACHMENT_STATUS,
        KeyColumnHeaderOICPostReportEnum.EMAIL,
        KeyColumnHeaderOICPostReportEnum.PHONE_NUMBER,
      ])
      .concat(isShowCustomer && [KeyColumnHeaderOICPostReportEnum.CUSTOMER_CODE])
      .concat([
        KeyColumnHeaderOICPostReportEnum.ROUND,
        KeyColumnHeaderOICPostReportEnum.COURSE_CODE,
        KeyColumnHeaderOICPostReportEnum.COURSE_NAME,
        KeyColumnHeaderOICPostReportEnum.SALUTE,
        KeyColumnHeaderOICPostReportEnum.FIRST_NAME,
        KeyColumnHeaderOICPostReportEnum.LAST_NAME,
      ])
      .concat(
        isShowLifeLicense && [
          KeyColumnHeaderOICPostReportEnum.OIC_LIFE_LICENSE_NO,
          KeyColumnHeaderOICPostReportEnum.OIC_LIFE_LICENSE_START_AT,
          KeyColumnHeaderOICPostReportEnum.OIC_LIFE_LICENSE_EXPIRED_AT,
        ],
      )
      .concat(
        isShowNonLifeLicense && [
          KeyColumnHeaderOICPostReportEnum.OIC_NON_LIFE_LICENSE_NO,
          KeyColumnHeaderOICPostReportEnum.OIC_NON_LIFE_LICENSE_START_AT,
          KeyColumnHeaderOICPostReportEnum.OIC_NON_LIFE_LICENSE_EXPIRED_AT,
        ],
      )
      .concat([
        KeyColumnHeaderOICPostReportEnum.APPLICANT_TYPE,
        KeyColumnHeaderOICPostReportEnum.LICENSE_RENEWAL_TYPE,
        KeyColumnHeaderOICPostReportEnum.LICENSE_TYPE,
        KeyColumnHeaderOICPostReportEnum.PERCENT_LEARNING_PROGRESS,
        KeyColumnHeaderOICPostReportEnum.PERCENT_POST_QUIZ_SCORE,
        KeyColumnHeaderOICPostReportEnum.LINK,
      ])
      .filter(Boolean);

    const rawData = props.map((data) => {
      const subjects = subjectHeaders.map((subjectCodeName) => {
        const key: CourseIdSubjectCodeKey = `${data.courseId}_${subjectCodeName}`;
        return data.isShowTrainingDuration ? (data[key]?.trainingDuration ?? '') : '';
      });
      return [
        '1.1.N.0.0',
        data.eid,
        data.fCmd,
        data.item,
        data.agLicense,
        data.agIdCard,
        data.prefix,
        data.agFNameTH,
        data.agLNameTH,
        data.fDeduction,
        data.remark,
      ]
        .concat(subjects)
        .concat([
          data.status,
          data.additionalDeductStatus,
          data.additionalAttachmentStatus,
          data.email,
          data.phoneNumber,
        ])
        .concat(isShowCustomer ? [data.customerCode] : [])
        .concat([data.round, data.courseCode, data.courseName, data.salute, data.firstName, data.lastName])
        .concat(
          isShowLifeLicense ? [data.oicLifeLicenseNo, data.oicLifeLicenseStartAt, data.oicLifeLicenseExpiredAt] : [],
        )
        .concat(
          isShowNonLifeLicense ? [data.oicNonLifeLicenseNo, data.oicNonLifeStartAt, data.oicNonLifeExpiredAt] : [],
        )
        .concat([
          data.applicantType,
          data.licenseRenewalType,
          data.licenseType,
          data.percentLearningProgress,
          data.percentPostQuizScore,
          data.link,
        ]);
    });
    const WORKSHEET_NAME = 'Sheet1';
    const excelBuilderService = new ExcelBuilderService();
    const lineSpaces = Array.from({ length: headerNamesColumns.length }, () => '');
    const rowsData = [this.headerTitleOICPreReport(subjectHeaderNumbers), lineSpaces, headerNamesColumns, ...rawData];

    const startRowBorder = 3;
    const startColumnBorder = 2;
    const startRowAlignment = 3;
    const startColumnAlignment = 1;
    const startRowSubjectAlignment = 4;
    const startColumnSubjectAlignment = 12;
    const startRowDeductAlignment = 3;
    const startColumnDeductAlignment = 10;
    const startRowRemarkAlignment = 3;
    const startColumnRemarkAlignment = 11;

    excelBuilderService
      .addWorksheet(WORKSHEET_NAME)
      .addRows(WORKSHEET_NAME, rowsData)
      .setCellOuterBorder(
        WORKSHEET_NAME,
        { row: startRowBorder, col: startColumnBorder },
        { row: startRowBorder + rawData.length, col: headerNamesColumns.length },
      )
      .setCellAlignment(
        WORKSHEET_NAME,
        { row: startRowAlignment, col: startColumnAlignment },
        { row: startRowAlignment + rawData.length, col: startColumnAlignment + headerNamesColumns.length },
        { vertical: 'middle', horizontal: 'left' },
      )
      .setCellAlignment(
        WORKSHEET_NAME,
        { row: startRowSubjectAlignment, col: startColumnSubjectAlignment },
        {
          row: startRowSubjectAlignment + rawData.length,
          col: startColumnSubjectAlignment + subjectHeaderNumbers.length,
        },
        { vertical: 'middle', horizontal: 'center' },
      )
      .setCellAlignment(
        WORKSHEET_NAME,
        { row: startRowDeductAlignment, col: startColumnDeductAlignment },
        { row: startRowDeductAlignment + rawData.length, col: startColumnDeductAlignment },
        { vertical: 'middle', horizontal: 'center' },
      )
      .setCellAlignment(
        WORKSHEET_NAME,
        { row: startRowRemarkAlignment, col: startColumnRemarkAlignment },
        { row: startRowRemarkAlignment + rawData.length, col: startColumnRemarkAlignment },
        { vertical: 'middle', horizontal: 'center' },
      )
      .setAppearanceResizeWidth(WORKSHEET_NAME, { offset: 3 });

    const buffer = await excelBuilderService.getFileBuffer();
    return buffer;
  }

  private defaultOicPostReportOptions(): OICPostReportOptionParams {
    return {
      isShowCustomer: true,
      isShowLifeLicense: true,
      isShowNonLifeLicense: true,
      subjectCodeNames: [],
    };
  }

  private headerTitleOICPreReport(subjects: OICReportSubjectNameParams[]): string[] {
    return [
      '',
      '',
      KeyColumnOICPreReportEnum.F_CMD,
      KeyColumnOICPreReportEnum.ITEM,
      KeyColumnOICPreReportEnum.AG_LICENSE,
      KeyColumnOICPreReportEnum.AG_ID_CARD,
      KeyColumnOICPreReportEnum.PREFIX_CODE,
      KeyColumnOICPreReportEnum.AG_FNAME_TH,
      KeyColumnOICPreReportEnum.AG_LNAME_TH,
      KeyColumnOICPreReportEnum.F_DEDUCTION,
      KeyColumnOICPreReportEnum.REMARK,
    ].concat(subjects);
  }

  licenseTypeByEnrollmentCertificates(
    enrollmentCertificates: EnrollmentCertificate[],
    courseLicenseType: Nullable<LicenseTypeEnum[]>,
    courseLicenseRenewal: Nullable<LicenseRenewalEnum>,
  ): string {
    const enrollmentCertificateTypes = enrollmentCertificates.map((item) => item.type);
    const isEnrollmentBothLicense =
      enrollmentCertificateTypes.includes(LicenseTypeEnum.LIFE) &&
      enrollmentCertificateTypes.includes(LicenseTypeEnum.NONLIFE);
    const isEnrollmentLifeLicense =
      enrollmentCertificateTypes.includes(LicenseTypeEnum.LIFE) && !isEnrollmentBothLicense;
    const isEnrollmentNonLifeLicense =
      enrollmentCertificateTypes.includes(LicenseTypeEnum.NONLIFE) && !isEnrollmentBothLicense;

    if (courseLicenseRenewal === LicenseRenewalEnum.UL) return LicenseTypeEnum.LIFE;
    if (courseLicenseRenewal === LicenseRenewalEnum.UK) return LicenseTypeEnum.UNIT_LINKED;

    if (isEnrollmentBothLicense) {
      const isCourseBothLicense =
        courseLicenseType.includes(LicenseTypeEnum.LIFE) && courseLicenseType.includes(LicenseTypeEnum.NONLIFE);
      if (isCourseBothLicense) {
        return LicenseTypeEnum.BOTH;
      }
    }

    if (isEnrollmentLifeLicense) {
      const isCourseLifeLicense = courseLicenseType.includes(LicenseTypeEnum.LIFE);
      if (isCourseLifeLicense) {
        return LicenseTypeEnum.LIFE;
      }
    }

    if (isEnrollmentNonLifeLicense) {
      const isCourseNonLifeLicense = courseLicenseType.includes(LicenseTypeEnum.NONLIFE);
      if (isCourseNonLifeLicense) {
        return LicenseTypeEnum.NONLIFE;
      }
    }

    return '';
  }

  private getEnrollmentAttachmentsStatusText(enrollmentAttachments: EnrollmentAttachment[]): string {
    const text = enrollmentAttachments
      .map((enrollmentAttachment) => {
        const { status } = enrollmentAttachment;
        return EnrollmentAttachmentStatusTextEnum[status];
      })
      .join(', ');

    return text;
  }

  private isPassed(enrollmentStatus: EnrollmentStatusEnum): boolean {
    return [
      EnrollmentStatusEnum.PASSED,
      EnrollmentStatusEnum.PENDING_APPROVAL,
      EnrollmentStatusEnum.VERIFIED,
      EnrollmentStatusEnum.APPROVED,
    ].includes(enrollmentStatus);
  }

  private isFCmd(
    enrollmentStatus: EnrollmentStatusEnum,
    licenseCourseType?: LicenseTypeEnum[],
    licenseRenewalType?: LicenseRenewalEnum,
  ): boolean {
    if (enrollmentStatus === EnrollmentStatusEnum.PASSED) {
      const isRenew4 = licenseRenewalType === LicenseRenewalEnum.RENEW4;
      const isBothLicense =
        licenseCourseType?.includes(LicenseTypeEnum.LIFE) && licenseCourseType?.includes(LicenseTypeEnum.NONLIFE);
      return isRenew4 && isBothLicense;
    } else if (enrollmentStatus === EnrollmentStatusEnum.PENDING_APPROVAL) {
      return true;
    } else if (enrollmentStatus === EnrollmentStatusEnum.VERIFIED) {
      return true;
    } else if (enrollmentStatus === EnrollmentStatusEnum.APPROVED) {
      return true;
    }

    return false;
  }

  private convertStringNumber(numStr?: string, decimal: number = 2): string {
    if (!numStr || numStr.trim() === '') {
      return '';
    }

    const num = parseFloat(numStr.trim());

    if (isNaN(num)) {
      return '';
    }

    return (Math.round(num * 100) / 100).toFixed(decimal);
  }

  private getLicenseNo(
    payload: PreEnrollmentTransactionPayloadParams,
    isLife: boolean,
    isUK: boolean,
    isNonLife: boolean,
    isBothLicenseCourse: boolean,
  ): string {
    const { oicExtendYearType, oicLicenseLifeNo, oicLicenseNonLifeNo } = payload;
    let licenseNo: string = '';

    if (isLife || isUK) {
      licenseNo = oicLicenseLifeNo;
    } else if (isNonLife) {
      licenseNo = oicLicenseNonLifeNo;
    } else if (isBothLicenseCourse) {
      if (oicExtendYearType === LicenseTypeEnum.LIFE) {
        licenseNo = oicLicenseLifeNo;
      } else if (oicExtendYearType === LicenseTypeEnum.NONLIFE) {
        licenseNo = oicLicenseNonLifeNo;
      } else if (oicExtendYearType === LicenseTypeEnum.BOTH) {
        licenseNo = oicLicenseLifeNo || oicLicenseNonLifeNo;
      }
    }
    return licenseNo;
  }

  private getLicenseNoManualEnrollment(params: {
    licenses: { licenseTypeCode: UserLicenseTypeCodeEnum; licenseNo: string }[];
    isLife: boolean;
    isUK: boolean;
    isNonLife: boolean;
    isBothLicenseCourse: boolean;
  }): string {
    const { licenses, isLife, isUK, isNonLife, isBothLicenseCourse } = params;

    if (isLife || isUK) {
      return licenses.find((license) => license.licenseTypeCode === UserLicenseTypeCodeEnum.OIC_LIFE)?.licenseNo;
    }

    if (isNonLife) {
      return licenses.find((license) => license.licenseTypeCode === UserLicenseTypeCodeEnum.OIC_NON_LIFE)?.licenseNo;
    }

    if (isBothLicenseCourse) {
      return (
        licenses.find((license) => license.licenseTypeCode === UserLicenseTypeCodeEnum.OIC_LIFE)?.licenseNo ||
        licenses.find((license) => license.licenseTypeCode === UserLicenseTypeCodeEnum.OIC_NON_LIFE)?.licenseNo
      );
    }

    return '';
  }

  private covertFormatDateInThaiLocale = (val?: Nullable<Date>): string => {
    const result = val
      ? formatDateInThaiLocale(val, TimeZoneEnum.Bangkok, DateFormat.buddhistDayMonthYearWithLeadingZero)
      : '';

    return result;
  };
}
