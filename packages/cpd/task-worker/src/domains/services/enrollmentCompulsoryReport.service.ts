import { date, DateFormat } from '@iso/helpers/dateUtils';
import { numberWithCommas } from '@iso/helpers/utility';
import { EnrollmentStatusTextEnum } from '@iso/lms/enums/enrollment.enum';
import { Injectable } from '@nestjs/common';

import { ExcelBuilderService } from '@infrastructures/services/files/excelBuilder.service';

import { CourseObjectiveTypeTH } from '@constants/enums/course.enum';
import { KeyColumnHeaderEnrollmentCompulsoryReportEnum } from '@constants/enums/report.enum';
import { EnrollmentCompulsoryReportDataParams } from '@constants/types/enrollment.type';
import {
  EnrollmentCompulsoryRawDataParams,
  InputEnrollmentCompulsoryResourceMappingDataParams,
  KeyColumnHeaderEnrollmentCompulsoryReportParams,
} from '@constants/types/reportHistory.type';

import { IEnrollmentCompulsoryReportService } from '@interfaces/services/report.service.interface';

import { getValueOrDefault } from '@domains/utils/transform.util';

@Injectable()
export class EnrollmentCompulsoryReportService implements IEnrollmentCompulsoryReportService {
  buildReportRawData(
    enrollments: EnrollmentCompulsoryReportDataParams[],
    resource: InputEnrollmentCompulsoryResourceMappingDataParams,
  ): EnrollmentCompulsoryRawDataParams[] {
    const raws = enrollments
      .filter((enrollment) => {
        const isCourseExisted = resource.courseById.has(enrollment.courseId);
        const isCourseVersionExisted = resource.courseVersionById.has(enrollment.courseVersionId);
        const isUserExisted = resource.userById.has(enrollment.userId);
        return isCourseExisted && isCourseVersionExisted && isUserExisted;
      })
      .map((enrollment) => {
        const user = resource.userById.get(enrollment.userId);
        const course = resource.courseById.get(enrollment.courseId);
        const courseVersion = resource.courseVersionById.get(enrollment.courseVersionId);
        const preEnrollmentTransaction = resource.preEnrollmentTransactionByEnrollmentId.get(enrollment.id);
        const userInDepartment = resource.userInDepartmentByUserId.get(enrollment.userId);
        const department = resource.departmentById.get(userInDepartment?.departmentId ?? '');
        const enrollmentPlanPackageLicenses = resource.enrollmentPlanPackageLicensesGroupByEnrollmentId.get(
          enrollment.id,
        );

        const plans = (enrollmentPlanPackageLicenses ?? []).map((item) => item.planName).join(', ');
        const packages = (enrollmentPlanPackageLicenses ?? [])
          .map((item) => item.planPackageName ?? '')
          .join(', ')
          .trim();

        const raw: EnrollmentCompulsoryRawDataParams = {
          userId: String(user?.guid ?? '-'),
          email: getValueOrDefault(user?.email, '-'),
          firstName: getValueOrDefault(user?.profile?.firstname, '-'),
          lastName: getValueOrDefault(user?.profile?.lastname, '-'),
          employeeId: getValueOrDefault(user?.employeeId, '-'),
          department: getValueOrDefault(department?.name, '-'),
          position: getValueOrDefault(user?.position, '-'),
          userStatus: getValueOrDefault(
            resource.activeUserIds.has(user?.guid),
            'ปิดใช้งาน',
            () => 'เปิดใช้งาน',
          ) as string,
          courseId: getValueOrDefault(course?.code, '-'),
          courseName: getValueOrDefault(courseVersion?.name, '-'),
          courseVersion: numberWithCommas(courseVersion?.version),
          assignmentDate: getValueOrDefault(
            preEnrollmentTransaction?.createdAt || enrollment.createdAt,
            '-',
            this.buildDateFormat,
          ) as string,
          assignmentStatus: getValueOrDefault(enrollment.status, '-', (v) => EnrollmentStatusTextEnum[v]),
          startedAt: getValueOrDefault(enrollment.startedAt, '-', this.buildDateFormat) as string,
          expiredAt: getValueOrDefault(enrollment?.expiredAt, '-', this.buildDateFormat) as string,
          courseObjective: getValueOrDefault(course?.objectiveType, '', (v) => CourseObjectiveTypeTH[v]),
          plan: getValueOrDefault(plans, '-'),
          package: getValueOrDefault(packages, '-'),
        };
        return raw;
      });

    return raws;
  }

  async buildReportFile(rawData: EnrollmentCompulsoryRawDataParams[]): Promise<ArrayBuffer> {
    const headerNamesColumns: KeyColumnHeaderEnrollmentCompulsoryReportParams[] = Object.values(
      KeyColumnHeaderEnrollmentCompulsoryReportEnum,
    );

    const WORKSHEET_NAME = 'Sheet1';
    const excelBuilderService = new ExcelBuilderService();

    const dataRows = rawData.map((data) => [
      data.userId, // 1
      data.email, // 2
      data.firstName, // 3
      data.lastName, // 4
      data.employeeId, // 5
      data.department, // 6
      data.position, // 7
      data.userStatus, // 8
      data.courseId, // 9
      data.courseName, // 10
      data.courseVersion, // 11
      data.assignmentDate, // 12
      data.assignmentStatus, // 13
      data.startedAt, // 14
      data.expiredAt, // 15
      data.courseObjective, // 16
      data.plan, // 17
      data.package, // 18
    ]);

    const rows = [headerNamesColumns, ...dataRows];

    excelBuilderService
      .addWorksheet(WORKSHEET_NAME)
      .addRows(WORKSHEET_NAME, rows)

      .setCellOuterBorder(
        WORKSHEET_NAME,
        { row: 1, col: 1 },
        { row: dataRows.length + 1, col: headerNamesColumns.length },
        'thin',
      )
      // set alignment
      // course version
      // assignment date
      .setCellAlignment(
        WORKSHEET_NAME,
        { row: 2, col: 11 },
        { row: dataRows.length + 2, col: 12 },
        { vertical: 'middle', horizontal: 'right' },
      )
      // set alignment
      // start round
      // end round
      .setCellAlignment(
        WORKSHEET_NAME,
        { row: 2, col: 14 },
        { row: dataRows.length + 2, col: 15 },
        { vertical: 'middle', horizontal: 'right' },
      )
      .setAppearanceResizeWidth(WORKSHEET_NAME, { offset: 3 });

    const buffer = await excelBuilderService.getFileBuffer();
    return buffer;
  }

  private buildDateFormat(value: Date): string {
    return date(value).format(DateFormat.dayMonthYearWithLeadingZero);
  }
}
