import { date, DateFormat } from '@iso/helpers/dateUtils';
import { TextUserGenderEnum } from '@iso/lms/enums/user.enum';
import isEmpty from 'lodash/isEmpty';
import isNil from 'lodash/isNil';

import { ExcelBuilderService } from '@infrastructures/services/files/excelBuilder.service';

import { KeyColumnHeaderLicenseUsageReportEnum } from '@constants/enums/report.enum';
import { LicenseUsageReportDataParams } from '@constants/types/enrollment.type';
import {
  InputLicenseUsageResourceMappingDataParams,
  KeyColumnHeaderLicenseUsageReportParams,
  LicenseUsageRawDataParams,
} from '@constants/types/reportHistory.type';

import { ILicenseUsageReportService } from '@interfaces/services/report.service.interface';

import { getValueOrDefault } from '@domains/utils/transform.util';

export class LicenseUsageReportService implements ILicenseUsageReportService {
  buildReportRawData(
    users: LicenseUsageReportDataParams[],
    resource: InputLicenseUsageResourceMappingDataParams,
  ): LicenseUsageRawDataParams[] {
    const data = users
      .map((user) => {
        const permissionGroups = (user?.permissionGroupIds ?? []).map(
          (permissionGroupId) => resource.permissionGroupById.get(permissionGroupId)?.name,
        );

        const userInDepartment = resource.userInDepartmentByUserId.get(user.guid);
        const department = resource.departmentById.get(userInDepartment?.departmentId ?? '');

        const userRawData = {
          email: user.email,
          firstName: user.profile.firstname,
          lastName: user.profile.lastname,

          department: getValueOrDefault(department?.name, ''),
          position: user.position,
          gender: getValueOrDefault(user.profile?.gender, '', (v) => TextUserGenderEnum[v]),
          permissionGroups: getValueOrDefault(permissionGroups.join(','), ''),
          userStatus: resource.activeUserIds.has(user.guid) ? 'Active' : 'Inactive',
          userVerificationStatus: isNil(user.setPasswordDate) ? 'False' : 'True',
        };

        const userLicenses = resource.planPackageLicenseGroupByUserId.get(String(user.guid)) ?? [];

        if (isEmpty(userLicenses)) {
          return [
            {
              ...userRawData,
              licenseId: '',
              planName: '',
              packageName: '',
              licenseStatus: '',
              packageLicenseActivateDate: '',
              packageLicenseExpiredDate: '',
            },
          ];
        }

        const licenseRawDataList = userLicenses.map((userLicense) => {
          return {
            licenseId: String(userLicense.licenseId),
            planName: userLicense.planName,
            packageName: userLicense.packageName,
            licenseStatus: this.getLicenseStatus(userLicense.startedDate, userLicense.expiredDate),
            packageLicenseActivateDate: this.buildDateFormat(userLicense.startedDate),
            packageLicenseExpiredDate: this.buildDateFormat(userLicense.expiredDate),
          };
        });

        const rows: LicenseUsageRawDataParams[] = licenseRawDataList.map((licenseRawData) => ({
          ...userRawData,
          ...licenseRawData,
        }));
        return rows;
      })
      .flat();

    return data;
  }

  async buildReportFile(rawData: LicenseUsageRawDataParams[]): Promise<ArrayBuffer> {
    const headerNamesColumns: KeyColumnHeaderLicenseUsageReportParams[] = Object.values(
      KeyColumnHeaderLicenseUsageReportEnum,
    );

    const WORKSHEET_NAME = 'Sheet1';
    const excelBuilderService = new ExcelBuilderService();

    const dataRows = rawData.map((data) => [
      data.email, // 1
      data.firstName, // 2
      data.lastName, // 3
      data.department, // 4
      data.position, // 5
      data.gender, // 6
      data.userStatus, // 7
      data.userVerificationStatus, // 8
      data.permissionGroups, // 9
      data.planName, // 10
      data.packageName, // 11
      data.licenseId, // 12
      data.licenseStatus, // 13
      data.packageLicenseActivateDate, //14
      data.packageLicenseExpiredDate, // 15
    ]);

    const rows = [headerNamesColumns, ...dataRows];

    excelBuilderService
      .addWorksheet(WORKSHEET_NAME)
      .addRows(WORKSHEET_NAME, rows)
      .setCellOuterBorder(
        WORKSHEET_NAME,
        { row: 1, col: 1 },
        { row: dataRows.length + 1, col: headerNamesColumns.length },
        'thin',
      )
      .setCellAlignment(
        WORKSHEET_NAME,
        { row: 2, col: 8 },
        { row: dataRows.length + 2, col: 8 },
        { vertical: 'middle', horizontal: 'center' },
      )
      .setAppearanceResizeWidth(WORKSHEET_NAME);

    const buffer = await excelBuilderService.getFileBuffer();
    return buffer;
  }

  private buildDateFormat(value: Date): string {
    return date(value).format(DateFormat.buddhistDayMonthYearHourMinuteWithLeadingZero);
  }

  private getLicenseStatus(startedAt: Date, expiredAt: Date): string {
    const dateNow = date();
    const isBeforeStartedAt = dateNow.isBefore(date(startedAt));
    if (isBeforeStartedAt) return 'not_start';
    return date(expiredAt).isBefore(dateNow) ? 'expire' : 'active';
  }
}
