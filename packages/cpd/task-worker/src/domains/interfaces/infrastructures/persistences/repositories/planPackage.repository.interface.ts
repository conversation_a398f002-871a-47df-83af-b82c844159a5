import { GenericID } from '@iso/constants/commonTypes';
import { PlanPackage } from '@iso/lms/models/planPackage.model';
import { PlanPackageParams } from '@iso/lms/types/planPackage.type';

import { FindPlanPackageIdsByPackageTypePlanIdsAndPackageIdsParams } from '@constants/types/planPackage.type';

import { IBaseRepository } from '@interfaces/repositories/base.repository.interface';

export interface IPlanPackageRepository extends IBaseRepository<PlanPackage, PlanPackageParams> {
  findIdsByPackageTypePlanIdAndPackageIds(
    params: FindPlanPackageIdsByPackageTypePlanIdsAndPackageIdsParams,
  ): Promise<GenericID[]>;
  findAvailableCustomContentPlanPackagesByProductSKUCourseIds(
    productSKUCourseIds: GenericID[],
    planPackageIds: GenericID[],
  ): Promise<PlanPackageParams[]>;
  findAvailableSubscriptionContentPlanPackagesByProductSKUCourseIds(
    productSKUCourseIds: GenericID[],
    planPackageIds: GenericID[],
  ): Promise<PlanPackageParams[]>;
}
