import { GenericID } from '@iso/constants/commonTypes';
import { Plan } from '@iso/lms/models/plan.model';
import { PlanParams } from '@iso/lms/types/plan.type';

import { IBaseRepository } from '@interfaces/repositories/base.repository.interface';

export interface IPlanRepository extends IBaseRepository<Plan, PlanParams> {
  findPlanPackagePlatform(organizationId: GenericID): Promise<Record<string, any>[]>;
  findActivePlatformPlanPackageIdsByOrganizationId(organizationId: GenericID): Promise<GenericID[]>;
  findAvailableContentPlanPackageIdsByOrganizationId(organizationId: GenericID): Promise<GenericID[]>;
}
