import { GenericID } from '@iso/constants/commonTypes';
import { UserNotificationTypeEnum } from '@iso/constants/userNotification';
import { ContentProviderTypeEnum, CourseObjectiveTypeEnum, RegulatorEnum } from '@iso/lms/enums/course.enum';
import { KnowledgeContentItemTypeEnum } from '@iso/lms/enums/knowledgeContentItem.enum';
import {
  PromoteContentAccessTypeEnum,
  PromoteNotificationContentTypeEnum,
} from '@iso/lms/enums/promoteNotification.enum';
import { Media } from '@iso/lms/models/media.model';
import { Organization } from '@iso/lms/models/organization.model';
import { OrganizationStorage } from '@iso/lms/models/organizationStorage.model';
import { PromoteNotification } from '@iso/lms/models/promoteNotification.model';
import { PackageParams } from '@iso/lms/types/package.type';
import { PlanPackageParams } from '@iso/lms/types/planPackage.type';
import { PromoteNotificationContentParams } from '@iso/lms/types/promoteNotification.type';
import { UserNotificationPayloadParams } from '@iso/lms/types/userNotification.type';
import { Dictionary } from 'lodash';

import { UserGroupContentParams } from '@constants/types/userGroup.type';

export type PublishPromoteNotificationParams = {
  promoteNotificationId: GenericID;
};

export type PromoteContentItemParams = {
  id: GenericID;
  name: string;
  thumbnailMediaId: GenericID;
  thumbnailUrl?: string;
  instructorIds?: GenericID[];
  instructors?: string[];
  accessType?: PromoteContentAccessTypeEnum;
  media?: Media;

  // course
  courseUrl?: string;
  objectiveType?: CourseObjectiveTypeEnum;
  regulator?: RegulatorEnum;
  contentProviderType?: ContentProviderTypeEnum;
  productSKUCourseId?: GenericID;

  // learning path
  code?: string;
  totalContent?: number;

  // knowledge content
  type?: KnowledgeContentItemTypeEnum;

  // announcement
  publishedStartAt?: Date;
};

export const PromoteNotificationContentTypeMapUserNotificationTypeEnum = {
  [PromoteNotificationContentTypeEnum.COURSE]: UserNotificationTypeEnum.PROMOTE_COURSE,
  [PromoteNotificationContentTypeEnum.LEARNING_PATH]: UserNotificationTypeEnum.PROMOTE_LEARNING_PATH,
  [PromoteNotificationContentTypeEnum.KNOWLEDGE_CONTENT]: UserNotificationTypeEnum.PROMOTE_KNOWLEDGE_CONTENT,
  [PromoteNotificationContentTypeEnum.ANNOUNCEMENT]: UserNotificationTypeEnum.PROMOTE_ANNOUNCEMENT,
};

export type PromoteContentInAppNotificationParams = {
  contentId: GenericID;
  inApplicationPayload: Pick<UserNotificationPayloadParams, 'mediaId' | 'message' | 'url'>;
};
export type OperatePromoteNotificationParams = {
  promoteNotification: PromoteNotification;
  emailContent: PromoteNotificationContentParams;
  organization: Organization;
  organizationStorage: OrganizationStorage;
  contentType: PromoteNotificationContentTypeEnum;
  contentsGroupById: Dictionary<PromoteContentItemParams>;
  contentUserNotificationsGroupByContentId: Dictionary<PromoteContentInAppNotificationParams>;
  userGroupContents: UserGroupContentParams[];
  productSKUCourseIds: GenericID[];
  contentPlanPackageIds: GenericID[];
  contentPlanPackagesGroupById: Dictionary<PlanPackageParams>;
  subscriptionPackagesGroupById: Dictionary<PackageParams>;
};
