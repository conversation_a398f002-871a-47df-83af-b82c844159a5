import { Inject, Injectable } from '@nestjs/common';
import { OnEvent } from '@nestjs/event-emitter';

import { ILogger } from '@infrastructures/services/logger/interfaces';

import { NotificationDIToken } from '@applications/di/domain/notification.di';
import { InfrastructuresAdaptersDIToken } from '@applications/di/infrastructures/adapters';
import { InfrastructuresServiceDIToken } from '@applications/di/infrastructures/services';

import { MessageBrokerEventEnum } from '@constants/enums/infrastructures/messageBroker.enum';

import { IConsumer } from '@interfaces/presenters/consumers/consumer.interface';
import { IMessageBrokerService } from '@interfaces/services/messageBroker.interface';
import { IPublishPromoteNotificationUseCase } from '@interfaces/usecases/notification.interface';

import { PublishPromoteNotificationSchema } from '@schemas/consumers/notification/publishPromoteNotification.schema';

import { ConsumerHandler, MessageBody } from '@presenters/consumers/decorator';
@Injectable()
export class NotificationConsumer implements IConsumer {
  // Queue Name
  private readonly consumerPublishPromoteNotificationOperation = 'notification:promote-notification';

  constructor(
    // usecase
    @Inject(NotificationDIToken.PublishPromoteNotificationUseCase)
    private readonly publishPromoteNotificationUseCase: IPublishPromoteNotificationUseCase,

    // service
    @Inject(InfrastructuresAdaptersDIToken.MessageBrokerAdaptorService)
    private readonly messageBrokerServiceAdaptor: IMessageBrokerService,
    @Inject(InfrastructuresServiceDIToken.LoggerApplication)
    private readonly logger: ILogger,
  ) {}

  async onModuleInit() {
    await this.onInitialConsumeMessageBroker();
  }

  @OnEvent(MessageBrokerEventEnum.Connected, { async: true })
  async onInitialConsumeMessageBroker(): Promise<void> {
    const queueNamePublishPromoteNotification = this.consumerPublishPromoteNotificationOperation;
    this.logger.log(`Subscribe: ${queueNamePublishPromoteNotification}`);
    this.messageBrokerServiceAdaptor.consumers(
      queueNamePublishPromoteNotification,
      async (msg, channel) => {
        this.handlerPublishPromoteNotification.bind(this)(msg, channel);
      },
      {
        noAck: false,
      },
    );
  }

  @ConsumerHandler()
  async handlerPublishPromoteNotification(
    @MessageBody(PublishPromoteNotificationSchema) content: PublishPromoteNotificationSchema,
  ) {
    this.logger.log('consume: promote notification');
    const payload = content.toJson();
    await this.publishPromoteNotificationUseCase.execute(payload);
  }
}
