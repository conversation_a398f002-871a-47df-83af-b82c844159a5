import { Inject, Injectable } from '@nestjs/common';
import { OnEvent } from '@nestjs/event-emitter';

import { ILogger } from '@infrastructures/services/logger/interfaces';

import { UserGroupDIToken } from '@applications/di/domain';
import { InfrastructuresAdaptersDIToken } from '@applications/di/infrastructures/adapters';
import { InfrastructuresServiceDIToken } from '@applications/di/infrastructures/services';

import { MessageBrokerEventEnum } from '@constants/enums/infrastructures/messageBroker.enum';

import { IConsumer } from '@interfaces/presenters/consumers/consumer.interface';
import { IMessageBrokerService } from '@interfaces/services/messageBroker.interface';
import { IUpdateUsersInUserGroupUseCase } from '@interfaces/usecases/userGroup.usecase.interface';

import { UpdateUsersInUserGroupSchema } from '@schemas/consumers/userGroup/userGroup.schema';

import { ConsumerHandler, MessageBody } from '@presenters/consumers/decorator';

@Injectable()
export class UserGroupConsumer implements IConsumer {
  private readonly userGroupQueueName = 'user-group:update-user-in-user-group';
  constructor(
    @Inject(InfrastructuresAdaptersDIToken.MessageBrokerAdaptorService)
    private readonly messageBrokerServiceAdaptor: IMessageBrokerService,
    @Inject(UserGroupDIToken.UpdateUsersInUserGroupByConditionUseCase)
    private readonly updateUserInUserGroupByConditionUseCase: IUpdateUsersInUserGroupUseCase,

    @Inject(InfrastructuresServiceDIToken.LoggerApplication)
    private readonly logger: ILogger,
  ) {}

  async onModuleInit() {
    await this.onInitialConsumeMessageBroker();
  }

  @OnEvent(MessageBrokerEventEnum.Connected, { async: true })
  async onInitialConsumeMessageBroker(): Promise<void> {
    this.logger.log(`Subscribe: ${this.userGroupQueueName}`);
    this.messageBrokerServiceAdaptor.consumers(
      this.userGroupQueueName,
      this.updateUserInUserGroupConsumerListener.bind(this),
      {
        noAck: false,
      },
    );
  }

  @ConsumerHandler()
  async updateUserInUserGroupConsumerListener(
    @MessageBody(UpdateUsersInUserGroupSchema) content: UpdateUsersInUserGroupSchema,
  ): Promise<void> {
    this.logger.log('consume: update user in user group');
    const payload = content.toJson();
    await this.updateUserInUserGroupByConditionUseCase.execute(payload);
  }
}
