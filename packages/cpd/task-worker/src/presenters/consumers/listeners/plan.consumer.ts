import { BulkOpTypeEnum } from '@iso/lms/enums/job.enum';
import { Inject, Injectable, Logger } from '@nestjs/common';
import { OnEvent } from '@nestjs/event-emitter';

import { PlanDIToken } from '@applications/di/domain';
import { InfrastructuresAdaptersDIToken } from '@applications/di/infrastructures/adapters';

import { MessageBrokerEventEnum } from '@constants/enums/infrastructures/messageBroker.enum';

import { IConsumer } from '@interfaces/presenters/consumers/consumer.interface';
import { IMessageBrokerService } from '@interfaces/services/messageBroker.interface';
import {
  IPlanPackageLicenseApprovedUseCase,
  IPlanPackageLicenseExpiredUseCase,
} from '@interfaces/usecases/plan.interface';

import { PlanPackageLicenseApprovedSchema } from '@schemas/consumers/plan/planPackageLicenseApproved.schema';
import { PlanPackageLicenseExpiredSchema } from '@schemas/consumers/plan/planPackageLicenseExpired.schema';

import { ConsumerHeaderOperationTypeHandler, MessageBody } from '@presenters/consumers/decorator';
import { validateHeaderPlanPackageLicenseOperationDomainQueue } from '@presenters/consumers/middleware/validator.middleware';

@Injectable()
export class PlanConsumer implements IConsumer {
  private readonly logger = new Logger(PlanConsumer.name);

  // Queue Name
  private readonly consumePlanPackageLicenseOperation = 'plan-package-license';

  constructor(
    @Inject(PlanDIToken.PlanPackageLicenseExpiredUseCase)
    private readonly planPackageLicenseExpiredUseCase: IPlanPackageLicenseExpiredUseCase,
    @Inject(PlanDIToken.PlanPackageLicenseApprovedUseCase)
    private readonly planPackageLicenseApprovedUseCase: IPlanPackageLicenseApprovedUseCase,

    // service
    @Inject(InfrastructuresAdaptersDIToken.MessageBrokerAdaptorService)
    private readonly messageBrokerServiceAdaptor: IMessageBrokerService,
  ) {}

  async onModuleInit() {
    await this.onInitialConsumeMessageBroker();
  }

  @OnEvent(MessageBrokerEventEnum.Connected, { async: true })
  async onInitialConsumeMessageBroker(): Promise<void> {
    const queueNamePlanPackageLicense = this.consumePlanPackageLicenseOperation;
    this.logger.log(`Subscribe: ${queueNamePlanPackageLicense}`);

    this.messageBrokerServiceAdaptor.consumers(
      queueNamePlanPackageLicense,
      async (msg, channel) => {
        this.planPackageLicenseExpiredConsumerListener.bind(this)(msg, channel);
        this.planPackageLicenseApprovedConsumerListener.bind(this)(msg, channel);
      },
      {
        noAck: false,
      },
    );
  }

  @ConsumerHeaderOperationTypeHandler({
    isAck: true,
    operationType: BulkOpTypeEnum.PLAN_PACKAGE_LICENSE_EXPIRED,
    onValidateHeader: validateHeaderPlanPackageLicenseOperationDomainQueue,
  })
  async planPackageLicenseExpiredConsumerListener(
    @MessageBody(PlanPackageLicenseExpiredSchema) content: PlanPackageLicenseExpiredSchema,
  ): Promise<void> {
    this.logger.log('consume: plan package license expired');
    const payload = content.toJson();
    await this.planPackageLicenseExpiredUseCase.execute(payload);
  }

  @ConsumerHeaderOperationTypeHandler({
    isAck: true,
    operationType: BulkOpTypeEnum.PLAN_PACKAGE_LICENSE_APPROVED,
    onValidateHeader: validateHeaderPlanPackageLicenseOperationDomainQueue,
  })
  async planPackageLicenseApprovedConsumerListener(
    @MessageBody(PlanPackageLicenseApprovedSchema) content: PlanPackageLicenseApprovedSchema,
  ): Promise<void> {
    this.logger.log('consume: plan package license approved');
    const payload = content.toJson();
    await this.planPackageLicenseApprovedUseCase.execute(payload);
  }
}
