import { BulkOpTypeEnum } from '@iso/lms/enums/job.enum';
import { Inject, Injectable } from '@nestjs/common';
import { OnEvent } from '@nestjs/event-emitter';

import { ILogger } from '@infrastructures/services/logger/interfaces';

import { OrganizationDIToken } from '@applications/di/domain/organization.di';
import { ReportDIToken } from '@applications/di/domain/report.di';
import { InfrastructuresAdaptersDIToken } from '@applications/di/infrastructures/adapters';
import { InfrastructuresServiceDIToken } from '@applications/di/infrastructures/services';

import { MessageBrokerEventEnum } from '@constants/enums/infrastructures/messageBroker.enum';

import { IConsumer } from '@interfaces/presenters/consumers/consumer.interface';
import { IMessageBrokerService } from '@interfaces/services/messageBroker.interface';
import { IGetOrganizationDomainsUseCase } from '@interfaces/usecases/organization.interface';
import {
  IExportApprovalTSIReportUseCase,
  IExportCustomerCreditBalanceReportUseCase,
  IExportLearnerReportUseCase,
  IExportEnrollmentCompulsoryReportUseCase,
  IExportOICDeductReportUseCase,
  IExportOICPostReportUseCase,
  IExportOICPreReportUseCase,
  IExportOICRegulatorPostReportUseCase,
  IExportOICRegulatorPreReportUseCase,
  IExportPreEnrollmentReportUseCase,
  IExportRegularPreEnrollmentReportUseCase,
  IExportSurveySubmissionReportUseCase,
  IExportLicenseUsageReportUseCase,
} from '@interfaces/usecases/report.interface';

import {
  ExportCustomerCreditBalanceReportSchema,
  ExportOICPreReportSchema,
  ExportSurveySubmissionReportSchema,
  FilterExportApprovalTSIReportHistorySchema,
  FilterExportOICDeductReportSchema,
  FilterExportOICPostReportSchema,
  FilterExportPreEnrollmentReportSchema,
} from '@schemas/consumers/report';
import { FilterExportEnrollmentCompulsoryReportSchema } from '@schemas/consumers/report/exportEnrollmentCompulsoryReport.schema';
import { FilterExportLearnerReportSchema } from '@schemas/consumers/report/exportLearnerReport.schema';
import { FilterExportLicenseUsageReportSchema } from '@schemas/consumers/report/exportLicenseUsageReport.schema';

import { ConsumerHeaderOperationTypeHandler, MessageBody, MessageHeader } from '@presenters/consumers/decorator';
import { validateTaskHeaderOperation } from '@presenters/consumers/middleware/validator.middleware';

@Injectable()
export class ReportConsumer implements IConsumer {
  // Queue Name
  private readonly consumerTaskOperation = '{domain}:report';

  constructor(
    @Inject(InfrastructuresAdaptersDIToken.MessageBrokerAdaptorService)
    private readonly messageBrokerServiceAdaptor: IMessageBrokerService,
    @Inject(OrganizationDIToken.GetOrganizationDomainsUseCase)
    private readonly getOrganizationDomainsUseCase: IGetOrganizationDomainsUseCase,
    @Inject(ReportDIToken.ExportOICDeductReportUseCase)
    private readonly exportOICDeductReportUseCase: IExportOICDeductReportUseCase,
    @Inject(ReportDIToken.ExportApprovalTSIReportUseCase)
    private readonly exportApprovalTSIReportUseCase: IExportApprovalTSIReportUseCase,
    @Inject(ReportDIToken.ExportPreEnrollmentReportUseCase)
    private readonly exportPreEnrollmentReportUseCase: IExportPreEnrollmentReportUseCase,
    @Inject(ReportDIToken.ExportRegularPreEnrollmentReportUseCase)
    private readonly exportRegularPreEnrollmentReportUseCase: IExportRegularPreEnrollmentReportUseCase,
    @Inject(ReportDIToken.ExportOICPreReportUseCase)
    private readonly exportOICPreReportUseCase: IExportOICPreReportUseCase,
    @Inject(ReportDIToken.ExportOICRegulatorPreReportUseCase)
    private readonly exportOICRegulatorPreReportUseCase: IExportOICRegulatorPreReportUseCase,
    @Inject(ReportDIToken.ExportSurveySubmissionReportUseCase)
    private readonly exportSurveySubmissionReportUseCase: IExportSurveySubmissionReportUseCase,
    @Inject(ReportDIToken.ExportOICPostReportUseCase)
    private readonly exportOICPostReportUseCase: IExportOICPostReportUseCase,
    @Inject(ReportDIToken.ExportOICRegulatorPostReportUseCase)
    private readonly exportOICRegulatorPostReportUseCase: IExportOICRegulatorPostReportUseCase,
    @Inject(ReportDIToken.ExportCustomerCreditBalanceReportUseCase)
    private readonly exportCustomerCreditBalanceReportUseCase: IExportCustomerCreditBalanceReportUseCase,
    @Inject(ReportDIToken.ExportLearnerReportUseCase)
    private readonly exportLearnerReportUseCase: IExportLearnerReportUseCase,
    @Inject(ReportDIToken.ExportEnrollmentCompulsoryReportUseCase)
    private readonly exportEnrollmentCompulsoryReportUseCase: IExportEnrollmentCompulsoryReportUseCase,
    @Inject(ReportDIToken.ExportLicenseUsageReportUseCase)
    private readonly exportLicenseUsageReportUseCase: IExportLicenseUsageReportUseCase,

    @Inject(InfrastructuresServiceDIToken.LoggerApplication)
    private readonly logger: ILogger,
  ) {}

  async onModuleInit() {
    this.onInitialConsumeMessageBroker();
  }

  @OnEvent(MessageBrokerEventEnum.Connected, { async: true })
  async onInitialConsumeMessageBroker(): Promise<void> {
    const organizations = await this.getOrganizationDomainsUseCase.execute(null);

    for (const organization of organizations) {
      const queueName = this.consumerTaskOperation.replace('{domain}', organization.domain);
      this.logger.log(`Subscribe: ${queueName}`);
      this.messageBrokerServiceAdaptor.consumers(
        queueName,
        async (msg, channel) => {
          this.exportApprovalTSIReportConsumerListener.bind(this)(msg, channel);
          this.exportOICDeductReportConsumerListener.bind(this)(msg, channel);
          this.exportPreEnrollmentReportConsumerListener.bind(this)(msg, channel);
          this.exportRegularPreEnrollmentReportConsumerListener.bind(this)(msg, channel);
          this.exportOICPreReportConsumerListener.bind(this)(msg, channel);
          this.exportOICRegulatorReportConsumerListener.bind(this)(msg, channel);
          this.exportSurveySubmissionReportConsumerListener.bind(this)(msg, channel);
          this.exportOICPostReportConsumerListener.bind(this)(msg, channel);
          this.exportOICRegulatorPostReportConsumerListener.bind(this)(msg, channel);
          this.exportCustomerCreditBalanceReportConsumerListener.bind(this)(msg, channel);
          this.exportLearnerReportConsumerListener.bind(this)(msg, channel);
          this.exportEnrollmentCompulsoryReport.bind(this)(msg, channel);
          this.exportLicenseUsageReport.bind(this)(msg, channel);
        },
        {
          noAck: false,
        },
      );
    }
  }

  @ConsumerHeaderOperationTypeHandler({
    isAck: true,
    operationType: BulkOpTypeEnum.TSI_REPORT,
    onValidateHeader: validateTaskHeaderOperation,
  })
  async exportApprovalTSIReportConsumerListener(
    @MessageBody(FilterExportApprovalTSIReportHistorySchema) content: FilterExportApprovalTSIReportHistorySchema,
    @MessageHeader('reportHistoryId') reportHistoryId: string,
  ): Promise<void> {
    this.logger.log(`consume: Export Approval TSI Report.`);
    const data = content.toJson();
    await this.exportApprovalTSIReportUseCase.execute({ ...data, reportHistoryId });
  }

  @ConsumerHeaderOperationTypeHandler({
    isAck: true,
    operationType: BulkOpTypeEnum.PRE_ENROLLMENT_REPORT,
    onValidateHeader: validateTaskHeaderOperation,
  })
  async exportPreEnrollmentReportConsumerListener(
    @MessageBody(FilterExportPreEnrollmentReportSchema) content: FilterExportPreEnrollmentReportSchema,
    @MessageHeader() headers: Record<string, unknown>,
  ): Promise<void> {
    this.logger.log(`consume: Export Pre-enrollment Report.`);
    const data = content.toJson();
    const reportHistoryId = String(headers.reportHistoryId);
    await this.exportPreEnrollmentReportUseCase.execute({ ...data, reportHistoryId });
  }

  @ConsumerHeaderOperationTypeHandler({
    isAck: true,
    operationType: BulkOpTypeEnum.REGULAR_PRE_ENROLLMENT_REPORT,
    onValidateHeader: validateTaskHeaderOperation,
  })
  async exportRegularPreEnrollmentReportConsumerListener(
    @MessageBody(FilterExportPreEnrollmentReportSchema) content: FilterExportPreEnrollmentReportSchema,
    @MessageHeader() headers: Record<string, unknown>,
  ): Promise<void> {
    this.logger.log(`consume: Export Regular PreEnrollment Report.`);
    const data = content.toJson();
    const reportHistoryId = String(headers.reportHistoryId);
    await this.exportRegularPreEnrollmentReportUseCase.execute({ ...data, reportHistoryId });
  }

  @ConsumerHeaderOperationTypeHandler({
    isAck: true,
    operationType: BulkOpTypeEnum.OIC_DEDUCT_REPORT,
    onValidateHeader: validateTaskHeaderOperation,
  })
  async exportOICDeductReportConsumerListener(
    @MessageBody(FilterExportOICDeductReportSchema) content: FilterExportOICDeductReportSchema,
    @MessageHeader('reportHistoryId') reportHistoryId: string,
  ): Promise<void> {
    this.logger.log(`consume: Export OIC Deduct Report.`);
    const data = content.toJson();
    await this.exportOICDeductReportUseCase.execute({ ...data, reportHistoryId });
  }

  @ConsumerHeaderOperationTypeHandler({
    isAck: true,
    operationType: BulkOpTypeEnum.OIC_PRE_REPORT,
    onValidateHeader: validateTaskHeaderOperation,
  })
  async exportOICPreReportConsumerListener(
    @MessageBody(ExportOICPreReportSchema) content: ExportOICPreReportSchema,
    @MessageHeader('reportHistoryId') reportHistoryId: string,
  ): Promise<void> {
    this.logger.log(`consume: Export OIC Pre Report.`);
    const data = content.toJson();
    await this.exportOICPreReportUseCase.execute({ ...data, reportHistoryId });
  }

  @ConsumerHeaderOperationTypeHandler({
    isAck: true,
    operationType: BulkOpTypeEnum.OIC_REGULATOR_PRE_REPORT,
    onValidateHeader: validateTaskHeaderOperation,
  })
  async exportOICRegulatorReportConsumerListener(
    @MessageBody(ExportOICPreReportSchema) content: ExportOICPreReportSchema,
    @MessageHeader('reportHistoryId') reportHistoryId: string,
  ): Promise<void> {
    this.logger.log(`consume: Export OIC Regulator Pre Report.`);
    const data = content.toJson();
    await this.exportOICRegulatorPreReportUseCase.execute({ ...data, reportHistoryId });
  }

  @ConsumerHeaderOperationTypeHandler({
    isAck: true,
    operationType: BulkOpTypeEnum.OIC_POST_REPORT,
    onValidateHeader: validateTaskHeaderOperation,
  })
  async exportOICPostReportConsumerListener(
    @MessageBody(FilterExportOICPostReportSchema) content: FilterExportOICPostReportSchema,
    @MessageHeader('reportHistoryId') reportHistoryId: string,
  ): Promise<void> {
    this.logger.log(`consume: Export OIC Post Report.`);
    const data = content.toJson();
    await this.exportOICPostReportUseCase.execute({ ...data, reportHistoryId });
  }

  @ConsumerHeaderOperationTypeHandler({
    isAck: true,
    operationType: BulkOpTypeEnum.OIC_REGULATOR_POST_REPORT,
    onValidateHeader: validateTaskHeaderOperation,
  })
  async exportOICRegulatorPostReportConsumerListener(
    @MessageBody(FilterExportOICPostReportSchema) content: FilterExportOICPostReportSchema,
    @MessageHeader('reportHistoryId') reportHistoryId: string,
  ): Promise<void> {
    this.logger.log(`consume: Export OIC Regulator Post Report.`);
    const data = content.toJson();
    await this.exportOICRegulatorPostReportUseCase.execute({ ...data, reportHistoryId });
  }

  @ConsumerHeaderOperationTypeHandler({
    isAck: true,
    operationType: BulkOpTypeEnum.SURVEY_SUBMISSION_REPORT,
    onValidateHeader: validateTaskHeaderOperation,
  })
  async exportSurveySubmissionReportConsumerListener(
    @MessageBody(ExportSurveySubmissionReportSchema) content: ExportSurveySubmissionReportSchema,
    @MessageHeader('reportHistoryId') reportHistoryId: string,
  ): Promise<void> {
    this.logger.log(`consume: Export Survey Submission Report.`);
    const data = content.toJson();
    await this.exportSurveySubmissionReportUseCase.execute({ ...data, reportHistoryId });
  }

  @ConsumerHeaderOperationTypeHandler({
    isAck: true,
    operationType: BulkOpTypeEnum.CUSTOMER_CREDIT_BALANCE_REPORT,
    onValidateHeader: validateTaskHeaderOperation,
  })
  async exportCustomerCreditBalanceReportConsumerListener(
    @MessageBody(ExportCustomerCreditBalanceReportSchema) content: ExportCustomerCreditBalanceReportSchema,
    @MessageHeader('reportHistoryId') reportHistoryId: string,
  ): Promise<void> {
    this.logger.log(`consume: Export Customer Credit Balance Report.`);
    const data = content.toJson();
    await this.exportCustomerCreditBalanceReportUseCase.execute({ ...data, reportHistoryId });
  }

  @ConsumerHeaderOperationTypeHandler({
    isAck: true,
    operationType: BulkOpTypeEnum.LEARNER_REPORT,
    onValidateHeader: validateTaskHeaderOperation,
  })
  async exportLearnerReportConsumerListener(
    @MessageBody(FilterExportLearnerReportSchema) content: FilterExportLearnerReportSchema,
    @MessageHeader('reportHistoryId') reportHistoryId: string,
  ): Promise<void> {
    this.logger.log(`consume: Export Learner Report.`);
    const data = content.toJson();
    await this.exportLearnerReportUseCase.execute({ ...data, reportHistoryId });
  }

  @ConsumerHeaderOperationTypeHandler({
    isAck: true,
    operationType: BulkOpTypeEnum.ENROLLMENT_COMPULSORY_REPORT,
    onValidateHeader: validateTaskHeaderOperation,
  })
  async exportEnrollmentCompulsoryReport(
    @MessageBody(FilterExportEnrollmentCompulsoryReportSchema) content: FilterExportEnrollmentCompulsoryReportSchema,
    @MessageHeader('reportHistoryId') reportHistoryId: string,
  ) {
    this.logger.log(`consume: Export Enrollment Compulsory Report.`);
    const data = content.toJson();
    await this.exportEnrollmentCompulsoryReportUseCase.execute({ ...data, reportHistoryId });
  }

  @ConsumerHeaderOperationTypeHandler({
    isAck: true,
    operationType: BulkOpTypeEnum.LICENSE_USAGE_REPORT,
    onValidateHeader: validateTaskHeaderOperation,
  })
  async exportLicenseUsageReport(
    @MessageBody(FilterExportLicenseUsageReportSchema) content: FilterExportLicenseUsageReportSchema,
    @MessageHeader('reportHistoryId') reportHistoryId: string,
  ) {
    this.logger.log(`consume: Export License Usage Report.`);
    const data = content.toJson();
    await this.exportLicenseUsageReportUseCase.execute({ ...data, reportHistoryId });
  }
}
