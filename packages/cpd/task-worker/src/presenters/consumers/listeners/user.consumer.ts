import { Inject, Injectable } from '@nestjs/common';
import { OnEvent } from '@nestjs/event-emitter';

import { ILogger } from '@infrastructures/services/logger/interfaces';

import { OrganizationDIToken } from '@applications/di/domain/organization.di';
import { InfrastructuresServiceDIToken } from '@applications/di/infrastructures/services';

import { MessageBrokerEventEnum } from '@constants/enums/infrastructures/messageBroker.enum';

import { IConsumer } from '@interfaces/presenters/consumers/consumer.interface';
import { IGetOrganizationDomainsUseCase } from '@interfaces/usecases/organization.interface';

@Injectable()
export class UserConsumer implements IConsumer {
  // Queue Name
  private readonly bulkUserValidateOperation = '{domain}:user_validate';

  constructor(
    // UserCase
    @Inject(OrganizationDIToken.GetOrganizationDomainsUseCase)
    private readonly getOrganizationDomainsUseCase: IGetOrganizationDomainsUseCase,
    @Inject(InfrastructuresServiceDIToken.LoggerApplication)
    private readonly logger: ILogger,
  ) {}

  async onModuleInit() {
    this.onInitialConsumeMessageBroker();
  }

  @OnEvent(MessageBrokerEventEnum.Connected, { async: true })
  async onInitialConsumeMessageBroker(): Promise<void> {
    const organizations = await this.getOrganizationDomainsUseCase.execute(null);

    for (const organization of organizations) {
      this.logger.log(`Subscribe: ${this.bulkUserValidateOperation.replace('{domain}', organization.domain)}`);
    }
  }
}
