import { GenericID } from '@iso/constants/commonTypes';
import { DBCollectionEnum } from '@iso/lms/enums/dbCollection.enum';
import { PackageTypeEnum } from '@iso/lms/enums/packages.enum';
import { PlanPackageLicense } from '@iso/lms/models/planPackageLicense.model';
import {
  PlanPackageLicenseParams,
  ResultFindNonSoApproveLicenseGroupUserByUserIdsParams,
  ResultFindSoApproveLicenseGroupUserByUserIdsParams,
} from '@iso/lms/types/planPackageLicense.type';

import { BaseRepository } from '@infrastructures/persistence/repositories/database/base.repository';

import { DbInstanceParams } from '@constants/types/infrastructures/database.type';

import { IPlanPackageLicenseDataMapper } from '@interfaces/dataMapper/planPackageLicense.dataMapper.interface';
import { IPlanPackageLicenseRepository } from '@interfaces/repositories/planPackageLicense.repository.interface';

import { date } from '@domains/utils/date.util';

const TABLE_NAME = DBCollectionEnum.PLAN_PACKAGE_LICENSES;

export class PlanPackageLicenseRepository
  extends BaseRepository<PlanPackageLicense, PlanPackageLicenseParams>
  implements IPlanPackageLicenseRepository
{
  constructor(
    readonly db: DbInstanceParams,
    readonly mapper: IPlanPackageLicenseDataMapper,
  ) {
    super(TABLE_NAME, db, mapper);
  }

  async findPlanPackageLicenseSOPendingActive(
    userId: GenericID,
    planPackageIds: GenericID[],
  ): Promise<PlanPackageLicenseParams[]> {
    const outout = await this.findPlanPackageLicenseMultipleUserSOPendingActive([userId], planPackageIds);
    return outout;
  }

  async findPlanPackageLicenseSOApprovedActive(
    userId: GenericID,
    planPackageIds: GenericID[],
  ): Promise<PlanPackageLicenseParams[]> {
    const output = await this.findPlanPackageLicenseMultipleUserSOApprovedActive([userId], planPackageIds);
    return output;
  }

  async findPlanPackageLicenseMultipleUserSOApprovedActive(
    userIds: GenericID[],
    planPackageIds: GenericID[],
  ): Promise<PlanPackageLicenseParams[]> {
    const output = await this.find({
      userId: {
        $in: userIds,
      },
      planPackageId: {
        $in: planPackageIds,
      },
      startedAt: { $ne: null },
      expiredAt: { $gte: date().toDate() },
    });
    return output;
  }

  async findPlanPackageLicenseMultipleUserSOPendingActive(
    userIds: GenericID[],
    planPackageIds: GenericID[],
  ): Promise<PlanPackageLicenseParams[]> {
    const aggregateParams = [
      {
        $match: {
          userId: {
            $in: userIds,
          },
          planPackageId: {
            $in: planPackageIds,
          },
          startedAt: null,
          expiredAt: null,
        },
      },
      {
        $lookup: {
          from: DBCollectionEnum.PLAN_PACKAGES,
          let: {
            planPackageId: '$planPackageId',
          },
          pipeline: [
            {
              $match: {
                $expr: {
                  $and: [
                    {
                      $eq: ['$id', '$$planPackageId'],
                    },
                  ],
                },
              },
            },
          ],
          as: 'planPackage',
        },
      },
      {
        $unwind: '$planPackage',
      },
      {
        $project: {
          id: 1,
          planPackageId: 1,
          userId: 1,
          startedAt: '$planPackage.startDate',
          expiredAt: '$planPackage.endDate',
          createdAt: 1,
          updatedAt: 1,
          deletedAt: 1,
        },
      },
      {
        $match: {
          expiredAt: {
            $gte: date().toDate(),
          },
        },
      },
    ];

    const output = await this.aggregate<PlanPackageLicenseParams>(aggregateParams);
    return output;
  }

  async findActiveLicenseUserIdsByPlanPackageIds(planPackageIds: GenericID[]): Promise<GenericID[]> {
    const dateNow = date().toDate();
    const pipeline = [
      {
        $match: {
          $and: [
            { planPackageId: { $in: planPackageIds } },
            {
              $or: [
                {
                  startedAt: null,
                  expiredAt: null,
                },
                {
                  startedAt: {
                    $lte: dateNow,
                  },
                  expiredAt: {
                    $gte: dateNow,
                  },
                },
              ],
            },
          ],
        },
      },
      {
        $group: {
          _id: null,
          userIds: { $addToSet: '$userId' },
        },
      },
    ];

    const [result] = await this.aggregate<{ userIds: GenericID[] }>(pipeline);
    return result?.userIds ?? [];
  }

  async findIdsByPlanPackageIds(planPackageIds: GenericID[]): Promise<GenericID[]> {
    const pipeline = [
      {
        $match: {
          planPackageId: { $in: planPackageIds },
        },
      },
      {
        $group: {
          _id: null,
          ids: { $push: '$id' },
        },
      },
    ];
    const [result] = await this.aggregate<{ ids: GenericID[] }>(pipeline);
    return result?.ids ?? [];
  }

  async findUserIdsByPlanPackageIds(planPackageIds: GenericID[]): Promise<GenericID[]> {
    const pipeline = [
      {
        $match: {
          planPackageId: { $in: planPackageIds },
        },
      },
      {
        $group: {
          _id: null,
          userIds: { $addToSet: '$userId' },
        },
      },
    ];
    const [result] = await this.aggregate<{ userIds: GenericID[] }>(pipeline);
    return result?.userIds ?? [];
  }

  async findActivePlatformPackageLicenseUserIdsByUserIds(userIds: GenericID[]): Promise<GenericID[]> {
    const dateNow = date().toDate();
    const pipeline = [
      {
        $match: {
          userId: { $in: userIds },
        },
      },
      {
        $lookup: {
          from: DBCollectionEnum.PLAN_PACKAGES,
          let: { planPackageId: '$planPackageId' },
          pipeline: [
            {
              $match: {
                $expr: {
                  $eq: ['$id', '$$planPackageId'],
                },
              },
            },
          ],
          as: 'planPackage',
        },
      },
      {
        $match: {
          $and: [
            { 'planPackage.type': PackageTypeEnum.PLATFORM },
            {
              $or: [
                {
                  startedAt: null,
                  expiredAt: null,
                  'planPackage.startDate': {
                    $lte: dateNow,
                  },
                  'planPackage.endDate': {
                    $gte: dateNow,
                  },
                },
                {
                  startedAt: {
                    $lte: dateNow,
                  },
                  expiredAt: {
                    $gte: dateNow,
                  },
                },
              ],
            },
          ],
        },
      },
      {
        $group: {
          _id: null,
          userIds: { $addToSet: '$userId' },
        },
      },
    ];
    const [result] = await this.aggregate<{ userIds: GenericID[] }>(pipeline);
    return result?.userIds ?? [];
  }

  async findSoApproveLicenseGroupUserByUserIds(
    userIds: GenericID[],
  ): Promise<ResultFindSoApproveLicenseGroupUserByUserIdsParams[]> {
    const pipeline = [
      {
        $match: {
          userId: { $in: userIds },
          startedAt: { $ne: null },
          expiredAt: { $ne: null },
        },
      },
      {
        $sort: {
          createdAt: -1,
        },
      },
      {
        $group: {
          _id: {
            userId: '$userId',
            planPackageId: '$planPackageId',
          },
          planPackageLicense: {
            $first: {
              id: '$id',
              planPackageId: '$planPackageId',
              startedAt: '$startedAt',
              expiredAt: '$expiredAt',
              createdAt: '$createdAt',
            },
          },
        },
      },
      {
        $group: {
          _id: '$_id.userId',
          planPackageLicenses: {
            $push: '$planPackageLicense',
          },
        },
      },
      {
        $project: {
          _id: 0,
          userId: '$_id',
          planPackageLicenses: 1,
        },
      },
    ];

    const cursor = this.aggregateStream<ResultFindSoApproveLicenseGroupUserByUserIdsParams>(pipeline);
    const results: ResultFindSoApproveLicenseGroupUserByUserIdsParams[] = [];
    for await (const doc of cursor) {
      results.push(doc);
    }
    return results;
  }

  async findNonSoApproveLicenseGroupUserByUserIds(
    userIds: GenericID[],
  ): Promise<ResultFindNonSoApproveLicenseGroupUserByUserIdsParams[]> {
    const pipeline = [
      {
        $match: {
          userId: { $in: userIds },
          startedAt: null,
          expiredAt: null,
        },
      },
      {
        $group: {
          _id: '$userId',
          planPackageLicenses: { $push: { id: '$id', planPackageId: '$planPackageId', createdAt: '$createdAt' } },
        },
      },
      {
        $project: {
          _id: 0,
          userId: '$_id',
          planPackageLicenses: 1,
        },
      },
    ];
    const cursor = this.aggregateStream<ResultFindNonSoApproveLicenseGroupUserByUserIdsParams>(pipeline);
    const results: ResultFindNonSoApproveLicenseGroupUserByUserIdsParams[] = [];
    for await (const doc of cursor) {
      results.push(doc);
    }
    return results;
  }
}
