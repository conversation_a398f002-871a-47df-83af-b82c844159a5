import { GenericID } from '@iso/constants/commonTypes';
import { DBCollectionEnum } from '@iso/lms/enums/dbCollection.enum';
import { PackageTypeEnum } from '@iso/lms/enums/packages.enum';
import { Plan } from '@iso/lms/models/plan.model';
import { PlanParams } from '@iso/lms/types/plan.type';

import { BaseRepository } from '@infrastructures/persistence/repositories/database/base.repository';

import { DbInstanceParams } from '@constants/types/infrastructures/database.type';

import { IPlanDataMapper } from '@interfaces/dataMapper/plan.dataMapper.interface';
import { IPlanRepository } from '@interfaces/repositories';

import { date } from '@domains/utils/date.util';

const TABLE_NAME = DBCollectionEnum.PLANS;

export class PlanRepository extends BaseRepository<Plan, PlanParams> implements IPlanRepository {
  constructor(
    readonly db: DbInstanceParams,
    readonly mapper: IPlanDataMapper,
  ) {
    super(TABLE_NAME, db, mapper);
  }

  async findPlanPackagePlatform(organizationId: GenericID): Promise<Record<string, any>[]> {
    const aggregateParams = [
      {
        $match: {
          organizationId,
          endDate: { $gte: date().toDate() },
        },
      },
      {
        $lookup: {
          from: DBCollectionEnum.PLAN_PACKAGES,
          let: { id: '$id' },
          pipeline: [
            {
              $match: {
                $expr: {
                  $and: [
                    {
                      $eq: ['$planId', '$$id'],
                    },
                    { $eq: ['$type', PackageTypeEnum.PLATFORM] },
                  ],
                },
              },
            },
          ],
          as: 'planPackage',
        },
      },
      { $unwind: '$planPackage' },
    ];

    return this.aggregate(aggregateParams);
  }

  async findActivePlatformPlanPackageIdsByOrganizationId(organizationId: GenericID): Promise<GenericID[]> {
    const dateNow = date().toDate();
    const pipeline = [
      {
        $match: {
          organizationId,
        },
      },
      {
        $lookup: {
          from: DBCollectionEnum.PLAN_PACKAGES,
          let: { id: '$id' },
          pipeline: [
            {
              $match: {
                $expr: {
                  $eq: ['$planId', '$$id'],
                },
              },
            },
          ],
          as: 'planPackage',
        },
      },
      {
        $match: {
          'planPackage.type': PackageTypeEnum.PLATFORM,
          'planPackage.startDate': {
            $lte: dateNow,
          },
          'planPackage.endDate': {
            $gte: dateNow,
          },
        },
      },
      {
        $group: {
          _id: null,
          planPackageIds: { $push: '$planPackage.id' },
        },
      },
      {
        $project: {
          _id: 0,
          planPackageIds: {
            $reduce: {
              input: '$planPackageIds',
              initialValue: [],
              in: { $concatArrays: ['$$value', '$$this'] },
            },
          },
        },
      },
    ];

    const [result] = await this.aggregate<{ planPackageIds: GenericID[] }>(pipeline);
    return result?.planPackageIds || [];
  }

  async findAvailableContentPlanPackageIdsByOrganizationId(organizationId: GenericID): Promise<GenericID[]> {
    const aggregateParams = [
      {
        $match: {
          organizationId,
          endDate: { $gte: date().toDate() },
        },
      },
      {
        $lookup: {
          from: DBCollectionEnum.PLAN_PACKAGES,
          let: { id: '$id' },
          pipeline: [
            {
              $match: {
                $expr: {
                  $and: [{ $eq: ['$planId', '$$id'] }, { $eq: ['$type', PackageTypeEnum.CONTENT] }],
                },
              },
            },
          ],
          as: 'planPackages',
        },
      },
      { $unwind: '$planPackages' },
      {
        $group: {
          _id: null,
          planPackageIds: { $push: '$planPackages.id' },
        },
      },
      {
        $project: {
          _id: 0,
          planPackageIds: 1,
        },
      },
    ];

    const [result] = await this.aggregate<{ planPackageIds: GenericID[] }>(aggregateParams);

    return result?.planPackageIds ?? [];
  }
}
