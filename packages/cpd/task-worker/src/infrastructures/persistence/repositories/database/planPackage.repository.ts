import { GenericID } from '@iso/constants/commonTypes';
import { date } from '@iso/helpers/dateUtils';
import { DBCollectionEnum } from '@iso/lms/enums/dbCollection.enum';
import { PackageContentModelTypeEnum, PackageTypeEnum } from '@iso/lms/enums/packages.enum';
import { PlanPackage } from '@iso/lms/models/planPackage.model';
import { PlanPackageParams } from '@iso/lms/types/planPackage.type';
import { isEmpty } from 'lodash';

import { BaseRepository } from '@infrastructures/persistence/repositories/database/base.repository';

import { DbInstanceParams } from '@constants/types/infrastructures/database.type';
import { FindPlanPackageIdsByPackageTypePlanIdsAndPackageIdsParams } from '@constants/types/planPackage.type';

import { IPlanPackageDataMapper } from '@interfaces/dataMapper/planPackage.dataMapper.interface';
import { IPlanPackageRepository } from '@interfaces/repositories/planPackage.repository.interface';

const TABLE_NAME = DBCollectionEnum.PLAN_PACKAGES;

export class PlanPackageRepository
  extends BaseRepository<PlanPackage, PlanPackageParams>
  implements IPlanPackageRepository
{
  constructor(
    readonly db: DbInstanceParams,
    readonly mapper: IPlanPackageDataMapper,
  ) {
    super(TABLE_NAME, db, mapper);
  }

  async findIdsByPackageTypePlanIdAndPackageIds(
    params: FindPlanPackageIdsByPackageTypePlanIdsAndPackageIdsParams,
  ): Promise<GenericID[]> {
    const pipeline = [
      {
        $match: {
          $and: [
            isEmpty(params.planIds) ? null : { planId: { $in: params.planIds } },
            isEmpty(params.packageIds) ? null : { packageId: { $in: params.packageIds } },
            isEmpty(params?.types) ? null : { type: { $in: params.types } },
          ].filter(Boolean),
        },
      },
      {
        $group: {
          _id: null,
          ids: { $push: '$id' },
        },
      },
    ];
    const [result] = await this.aggregate<{ ids: GenericID[] }>(pipeline);
    return result?.ids ?? [];
  }

  async findAvailableCustomContentPlanPackagesByProductSKUCourseIds(
    productSKUCourseIds: GenericID[],
    planPackageIds: GenericID[],
  ): Promise<PlanPackageParams[]> {
    const aggregateParams = [
      {
        $match: {
          id: { $in: planPackageIds },
          type: PackageTypeEnum.CONTENT,
          'content.model': PackageContentModelTypeEnum.CUSTOM,
          endDate: { $gte: date().toDate() },
          $expr: {
            $anyElementTrue: {
              $map: {
                input: productSKUCourseIds,
                as: 'productSKUCourseIds',
                in: { $in: ['$$productSKUCourseIds', '$content.productSKUCourseIds'] },
              },
            },
          },
        },
      },
      {
        $project: {
          _id: 0,
        },
      },
    ];

    return this.aggregate(aggregateParams);
  }

  async findAvailableSubscriptionContentPlanPackagesByProductSKUCourseIds(
    productSKUCourseIds: GenericID[],
    planPackageIds: GenericID[],
  ): Promise<PlanPackageParams[]> {
    const aggregateParams = [
      {
        $match: {
          id: { $in: planPackageIds },
          type: PackageTypeEnum.CONTENT,
          'content.model': PackageContentModelTypeEnum.SUBSCRIPTION,
          endDate: { $gte: date().toDate() },
        },
      },
      {
        $lookup: {
          from: DBCollectionEnum.PACKAGES,
          let: { packageId: '$packageId' },
          pipeline: [
            {
              $match: {
                $expr: { $eq: ['$id', '$$packageId'] },
              },
            },
          ],
          as: 'package',
        },
      },
      {
        $unwind: '$package',
      },
      {
        $match: {
          $expr: {
            $anyElementTrue: {
              $map: {
                input: productSKUCourseIds,
                as: 'sku',
                in: { $in: ['$$sku', '$package.productSKUCourseIds'] },
              },
            },
          },
        },
      },
      {
        $project: {
          _id: 0,
          package: 0,
        },
      },
    ];

    return this.aggregate(aggregateParams);
  }
}
