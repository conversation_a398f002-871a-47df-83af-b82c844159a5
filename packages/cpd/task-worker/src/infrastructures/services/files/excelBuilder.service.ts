import { Writable } from 'stream';

import excel from 'exceljs';

import {
  ExcelAdjustWidthOptionParams,
  ExcelBuilderParams,
  ExcelCellParams,
  ExcelDataPayload,
  ExcelHeaderParams,
  ExcelStyleParams,
  defaultStyle,
  numberFormat,
} from '@constants/types/infrastructures/excelBuilder.type';

export class ExcelBuilderService {
  // Constance default
  static readonly defaultWidth = 15;
  static readonly defaultStyle = {
    border: defaultStyle,
    numFmt: numberFormat.defaultFormat,
  };

  private columnsStyle: Record<string, ExcelStyleParams> = {};
  private headerColumns: Record<string, ExcelHeaderParams[]> = {};
  private workSheetsName: Set<string>;
  private workbook: excel.Workbook;
  private widthColumns: number[] = [];

  constructor(params?: ExcelBuilderParams) {
    this.workbook = new excel.Workbook();
    this.workSheetsName = new Set(params?.workSheets);
    this.workSheetsName.forEach((name) => {
      this.setStyle(ExcelBuilderService.defaultStyle, name);
      this.setColumnHeader([], name);
    });
  }

  addWorksheet(name: string): this {
    if (this.workSheetsName.has(name)) return this;
    this.workSheetsName.add(name);

    this.setColumnHeader([], name);
    this.setStyle(ExcelBuilderService.defaultStyle, name);
    this.workbook.addWorksheet(name);
    return this;
  }

  removeWorksheet(name: string): this {
    this.workSheetsName.delete(name);
    this.workbook.removeWorksheet(name);
    return this;
  }

  setColumnHeader(columns: ExcelHeaderParams[], workSheetName: string): this {
    if (!this.workSheetsName.has(workSheetName)) return this;
    const headerColumns = columns.map(({ header, key, width, style }) => ({
      header,
      key,
      width: width ?? ExcelBuilderService.defaultWidth,
      style: style || this.columnsStyle[workSheetName] || ExcelBuilderService.defaultStyle,
    }));

    this.headerColumns[workSheetName] = headerColumns;
    return this;
  }

  setStyle(style: ExcelStyleParams, workSheetName: string): ExcelBuilderService {
    if (!this.workSheetsName.has(workSheetName)) return this;
    this.columnsStyle = { [workSheetName]: style };
    return this;
  }

  buildStreamFileExcel(payload: ExcelDataPayload[]) {
    return async (stream: Writable): Promise<void> => {
      const workbook = new excel.stream.xlsx.WorkbookWriter({
        stream,
        useStyles: true,
      });

      for (const groupSheetData of payload) {
        const { workSheetName, rowsData } = groupSheetData;

        const worksheet = workbook.addWorksheet(workSheetName);
        worksheet.columns = this.headerColumns[workSheetName] as excel.Column[];

        for (const data of rowsData) {
          worksheet.addRow(data).commit();
        }
      }

      await workbook.commit();
    };
  }

  addRow(workSheetName: string, rowData: Array<string | number>, style?: string): this {
    const worksheet = this.workbook.getWorksheet(workSheetName);
    worksheet.addRow(rowData, style);

    for (let colIndex = 0; colIndex < rowData.length; colIndex++) {
      const cellValue = rowData[colIndex];
      const value = cellValue ? cellValue.toString() : '';
      const currentLength = value.length;

      if (this.widthColumns[colIndex] === undefined || currentLength > this.widthColumns[colIndex]) {
        this.widthColumns[colIndex] = currentLength;
      }
    }

    return this;
  }

  addRows(workSheetName: string, rowsData: Array<string | number>[]): this {
    const worksheet = this.workbook.getWorksheet(workSheetName);
    worksheet.addRows(rowsData);

    for (const row of rowsData) {
      for (let colIndex = 0; colIndex < row.length; colIndex++) {
        const cellValue = row[colIndex];
        const value = cellValue ? cellValue.toString() : '';
        const currentLength = value.length;

        if (this.widthColumns[colIndex] === undefined || currentLength > this.widthColumns[colIndex]) {
          this.widthColumns[colIndex] = currentLength;
        }
      }
    }

    return this;
  }

  buildRawFileExcel(payload: ExcelDataPayload[]): this {
    const payloadLength = payload.length;
    const worksheets: excel.Worksheet[] = Array.from({ length: payloadLength });
    for (let i = 0; i < payloadLength; i++) {
      const { workSheetName, rowsData } = payload[i];
      const worksheet = this.workbook.getWorksheet(workSheetName);

      worksheet.columns = this.headerColumns[workSheetName] as excel.Column[];
      worksheet.addRows(rowsData);
      worksheets[i] = worksheet;
    }

    return this;
  }

  setRowFill(row: number, workSheetName: string, fill: excel.Fill): this {
    if (!this.workSheetsName.has(workSheetName)) return this;
    const worksheet = this.workbook.getWorksheet(workSheetName);
    worksheet.getRow(row).fill = fill;
    return this;
  }

  setCellOuterBorder(
    sheetName: string,
    start: ExcelCellParams,
    end: ExcelCellParams,
    style: excel.BorderStyle = 'thin',
  ): this {
    const worksheet = this.workbook.getWorksheet(sheetName);

    if (!worksheet) return this;

    const borderStyle = style ?? 'thin';

    for (let iRow = start.row; iRow <= end.row; iRow++) {
      for (let iCol = start.col; iCol <= end.col; iCol++) {
        const cell = worksheet.getCell(iRow, iCol);
        cell.border = {
          top: { style: borderStyle },
          left: { style: borderStyle },
          bottom: { style: borderStyle },
          right: { style: borderStyle },
        };
      }
    }
    return this;
  }

  setCellAlignment(
    sheetName: string,
    start: ExcelCellParams,
    end: ExcelCellParams,
    alignment: Partial<excel.Alignment>,
  ): this {
    const worksheet = this.workbook.getWorksheet(sheetName);

    if (!worksheet) return this;

    for (let i = start.row; i <= end.row; i++) {
      for (let j = start.col; j <= end.col; j++) {
        const cell = worksheet.getCell(i, j);
        cell.alignment = alignment;
      }
    }
    return this;
  }

  setAppearanceResizeWidth(sheetName: string, options?: ExcelAdjustWidthOptionParams): this {
    const worksheet = this.workbook.getWorksheet(sheetName);

    if (!worksheet || !worksheet.columns || this.widthColumns.length === 0) return this;

    worksheet.columns.forEach((column) => {
      const maxLength = this.widthColumns[column.number - 1] || ExcelBuilderService.defaultWidth;

      column.width = maxLength + (options?.offset ?? 0);
    });

    return this;
  }

  async getFileBuffer(): Promise<ArrayBuffer> {
    const buffer = await this.workbook.xlsx.writeBuffer();
    return buffer;
  }

  async loadStreamFile(stream: Buffer): Promise<excel.Workbook> {
    const workbook = new excel.Workbook();
    await workbook.xlsx.load(stream);
    return workbook;
  }
}
