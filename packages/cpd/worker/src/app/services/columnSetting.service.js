import { getColumnSettingValueByGroupFields } from '@iso/helpers/columnSetting';
import { ColumnSettingDataTypeEnum } from '@iso/lms/enums/columnSetting.enum';
import {
  assign,
  camelCase,
  chain,
  filter,
  flatten,
  get,
  gte,
  has,
  intersection,
  isArray,
  isBoolean,
  isEmpty,
  isEqual,
  isFunction,
  isNil,
  isNumber,
  isString,
  isUndefined,
  keyBy,
  last,
  map,
  mapValues,
  merge,
  omit,
  partition,
  reduce,
  reverse,
  size,
  uniq,
} from 'lodash';

import { DBCollection } from '@constants/dbCollection';
import { date } from '@infrastructure/dateUtils';
import {
  convertSelectedToProject,
  createParameterExpress,
  getWhereConditionFromRelationalAggregation,
  groupAndUnwindByRelationFieldData,
  transformColumnSettingFilterToFilterTypeExpress,
} from '@root/infrastructure/transformColumnSettingUtils';

class ColumnSettingService {
  constructor({ db, logger }) {
    this.repository = db.collection(DBCollection.column_settings);
    this.logger = logger;
  }

  async find(query = {}) {
    const res = await this.repository.find(query).toArray();
    return res;
  }

  async findOne(query = {}) {
    const res = await this.repository.findOne(query);
    return res;
  }

  async aggregate(query) {
    const res = await this.repository.aggregate(query).toArray();
    return res;
  }

  async save(document, opts) {
    const { id } = document;
    document.updatedAt = date().toDate();

    const option = { upsert: true };
    if (opts) {
      option.session = opts.session;
    }
    await this.repository.replaceOne({ id }, document, option);
  }

  async findColumnSettingWithTemplate(key) {
    const pipeline = [
      {
        $match: { key },
      },
      {
        $addFields: {
          template: '',
          code: '',
          isActive: true,
          isFilter: true,
          isTemplateRequired: true,
          isDefault: true,
          order: 0,
        },
      },
    ];

    const result = await this.repository.aggregate(pipeline).toArray();
    return result;
  }

  getValueByColumnSettingKey(key, result, columnSettings) {
    const columnSetting = columnSettings.find((item) => item.key === key);
    if (!columnSetting) return [''];
    const value = getColumnSettingValueByGroupFields(result, columnSetting.groupFields);

    return value;
  }

  buildAggregationFromColumnSetting(params) {
    const { filters, columnSettings } = params;

    const mappedFilterConditionWithColumnSettingId = reduce(
      filters,
      (acc, cur) => {
        const { id, data } = cur;
        acc.set(id, data);
        return acc;
      },
      new Map(),
    );

    const aggregateStateFromGroupFieldColumnSetting = chain(columnSettings)
      .filter((columnSetting) => columnSetting?.isActive)
      .map((columnSetting) => {
        const columnSettingId = columnSetting?.key;
        if (!mappedFilterConditionWithColumnSettingId.has(columnSettingId)) {
          return { ...columnSetting, filterDataInColumnSettingObject: {} };
        }
        const filterDataInColumnSettings = mappedFilterConditionWithColumnSettingId.get(columnSettingId);
        const filterDataInColumnSettingObject = mapValues(keyBy(filterDataInColumnSettings, 'field'), 'value');

        return { ...columnSetting, filterDataInColumnSettingObject };
      })
      .map((columnSetting) => {
        const { filterDataInColumnSettingObject, filterType } = columnSetting;
        const mergeFilterValueAndGroupFields = map(columnSetting?.groupFields, (groupField) => ({
          ...groupField,
          filterType,
          value: has(filterDataInColumnSettingObject, groupField.columnCode)
            ? filterDataInColumnSettingObject[groupField.columnCode]
            : null,
        }));
        return mergeFilterValueAndGroupFields;
      })

      // Map filter value with group field column code
      .map((groupFields) => {
        const isRequiredDefaultFilter = chain(groupFields)
          .filter((v) => v.isFilter && !v.isDefault)
          .map('value')
          .some((v) => (!isNil(v) && !isEmpty(v)) || isBoolean(v))
          .value();

        const [columnsInMainCollection, columnsInJoinedCollection] = chain(groupFields)
          .partition((v) => isEmpty(v.relationalPath) || isNil(v.relationalPath))
          .value();

        const aggregateInMainCollection = map(columnsInMainCollection, (v) =>
          this.operateColumnSettingCondition(v, isRequiredDefaultFilter),
        );
        const aggregateInJoinCollection = map(columnsInJoinedCollection, (v) =>
          this.operateColumnSettingJoinCondition(v, isRequiredDefaultFilter),
        );
        return { aggregateInMainCollection, aggregateInJoinCollection };
      })

      .value();

    mappedFilterConditionWithColumnSettingId.clear();

    const aggregateInMainCollection = chain(aggregateStateFromGroupFieldColumnSetting)
      .map('aggregateInMainCollection')
      .filter((v) => !isEmpty(v))
      .flatten()
      .reduce(
        (acc, cur) => {
          const { select, where } = cur;
          if (!isEmpty(where)) acc.match.push(where);
          if (!isEmpty(select)) acc.project = assign(acc.project, { [select]: 1 });
          return acc;
        },
        { match: [], project: {} },
      )
      .value();

    const aggregateJoinCollections = chain(aggregateStateFromGroupFieldColumnSetting)
      .map('aggregateInJoinCollection')
      .filter((v) => !isEmpty(v))
      .map((joinCollections) => {
        const joinRelationalLookUpCollection = chain(joinCollections)
          .map((joinCollectionRelationList) => {
            if (gte(size(joinCollectionRelationList), 2)) {
              const [main, ...other] = joinCollectionRelationList;
              const chainLookUps = flatten(other);

              const selectsFieldInChainLookUp = chain(chainLookUps)
                .filter((lookUp) => isEqual(lookUp.from, main.collection))
                .map((v) => v.foreignField)
                .uniq()
                .value();
              main.selects = main.selects.concat(selectsFieldInChainLookUp);
              return { ...main, chainLookUps };
            }
            return joinCollectionRelationList[0];
          })
          .reduce((accumulateJoinCollection, currentJoinCollection) => {
            if (isEmpty(accumulateJoinCollection)) return currentJoinCollection;

            accumulateJoinCollection.where = merge(accumulateJoinCollection.where, currentJoinCollection.where);
            accumulateJoinCollection.selects = chain(accumulateJoinCollection.selects)
              .concat(currentJoinCollection.selects)
              .uniq()
              .value();

            accumulateJoinCollection.chainLookUps = chain(accumulateJoinCollection.chainLookUps)
              .concat(currentJoinCollection.chainLookUps)
              .reduce((acc, cur) => {
                const { collection, from, foreignField, where, selects } = cur;
                const key = `${collection}_${from}_${foreignField}`;

                if (_.has(acc, key)) {
                  const joinCollection = acc[key];
                  joinCollection.where = merge(joinCollection.where, where);
                  joinCollection.selects = uniq([...joinCollection.selects, ...selects]);
                  acc[key] = joinCollection;
                  return acc;
                }

                acc[key] = omit(cur, 'chainLookUps');
                return acc;
              }, {})
              .values()
              .value();

            return accumulateJoinCollection;
          }, {})
          .value();
        return joinRelationalLookUpCollection;
      })
      .reduce((acc, cur) => this.reduceAggregateJoinCollection(acc, cur), {})
      .values()
      .map(this.reduceAndCombinedChainLookUps)
      .value();

    const projectFieldInJoinCollection = chain(aggregateJoinCollections)
      .map('foreignField')
      .reduce((acc, cur) => ({ ...acc, [cur]: 1 }), {})
      .value();

    aggregateInMainCollection.project = merge(aggregateInMainCollection.project, projectFieldInJoinCollection);

    const aggregateJoinCollectionsWithNoneConditions = [];
    const aggregateJoinCollectionWithFilterConditions = [];
    for (const aggregateJoinCollection of aggregateJoinCollections) {
      const { where, chainLookUps } = aggregateJoinCollection;
      if (isEmpty(where) && isEmpty(chainLookUps.flat())) {
        aggregateJoinCollectionsWithNoneConditions.push(aggregateJoinCollection);
        continue;
      }

      if (!isEmpty(where) && isEmpty(chainLookUps.flat())) {
        aggregateJoinCollectionWithFilterConditions.push(aggregateJoinCollection);
        aggregateJoinCollectionsWithNoneConditions.push({ ...aggregateJoinCollection, where: [] });
        continue;
      }

      const chainLookUpConditions = [];
      const chainLookUpNoneCondition = [];
      const chainLookUpList = chainLookUps;
      for (const [index, partitionChainLookUp] of chainLookUpList.entries()) {
        const isFirstPartition = index === 0;

        const partitionChainLookUpWithConditions = partitionChainLookUp.filter(
          (chainLookUp) => !isEmpty(chainLookUp.where),
        );
        const partitionChainLookUpNoneConditions = partitionChainLookUp.map((chainLookUp) => ({
          ...chainLookUp,
          where: {},
        }));
        if (isFirstPartition) {
          if (!isEmpty(partitionChainLookUpWithConditions))
            chainLookUpConditions.push(partitionChainLookUpWithConditions);
          if (!isEmpty(partitionChainLookUpNoneConditions))
            chainLookUpNoneCondition.push(partitionChainLookUpNoneConditions);
          continue;
        }

        const previousPartitionConditionChainLookUp = (last(chainLookUpConditions) || []).map((ch) => ch.from);
        const previousPartitionNoneConditionChainLookUp = (last(chainLookUpNoneCondition) || []).map((ch) => ch.from);

        const relationalConditionChainLookUp = partitionChainLookUp.filter(
          (chainLookUp) =>
            previousPartitionConditionChainLookUp.includes(chainLookUp.collection) || !isEmpty(chainLookUp.where),
        );

        const relationalNoneConditionChainLookUp = partitionChainLookUp.filter(
          (chainLookUp) =>
            previousPartitionNoneConditionChainLookUp.includes(chainLookUp.collection) || isEmpty(chainLookUp.where),
        );

        if (!isEmpty(relationalConditionChainLookUp)) {
          chainLookUpConditions.push(relationalConditionChainLookUp);
        }

        if (!isEmpty(relationalNoneConditionChainLookUp)) {
          chainLookUpNoneCondition.push(relationalNoneConditionChainLookUp);
        }
      }

      aggregateJoinCollectionsWithNoneConditions.push({
        ...aggregateJoinCollection,
        where: [],
        chainLookUps: chainLookUpNoneCondition,
      });

      if (!isEmpty(where) || !isEmpty(chainLookUpConditions)) {
        aggregateJoinCollectionWithFilterConditions.push({
          ...aggregateJoinCollection,
          where,
          chainLookUps: chainLookUpConditions,
        });
      }
    }

    return {
      aggregateInMainCollection,
      aggregateJoinCollectionsWithNoneConditions,
      aggregateJoinCollectionWithFilterConditions,
    };
  }

  async buildAndQueryJoinAggregateCollectionWithFilterCondition(
    aggregateJoinCollectionWithFilterConditions,
    repository,
    buildCustomStateAggregation,
  ) {
    let matchAggregationJoinConditions = [];
    let resultAggregateJoinCollectionsWithConditions = [];

    if (isEmpty(aggregateJoinCollectionWithFilterConditions))
      return {
        isNothingMatchInJoinCondition: false,
        matchAggregationJoinConditions,
        resultAggregateJoinCollectionsWithConditions,
      };

    resultAggregateJoinCollectionsWithConditions = await Promise.all(
      map(aggregateJoinCollectionWithFilterConditions, (v) =>
        this.queryJoinRelationalCollectionWithFilterCondition(v, repository, buildCustomStateAggregation),
      ),
    );

    const matchCombinedCondition = chain(resultAggregateJoinCollectionsWithConditions)
      .reduce((acc, cur) => {
        const value = chain(cur.data).map(cur.localField).flatten().value();
        if (has(acc, cur.foreignField)) {
          acc[cur.foreignField] = intersection(acc[cur.foreignField], value);
        } else {
          acc[cur.foreignField] = value;
        }
        return acc;
      }, {})
      .value();

    const isNothingMatchInJoinCondition = chain(matchCombinedCondition).values().flatten().isEmpty().value();
    if (isNothingMatchInJoinCondition)
      return {
        isNothingMatchInJoinCondition,
        resultAggregateJoinCollectionsWithConditions: [],
        matchAggregationJoinConditions: [],
      };

    matchAggregationJoinConditions = chain(matchCombinedCondition)
      .map((value, key) => ({ [key]: { $in: value } }))
      .value();

    return {
      isNothingMatchInJoinCondition: false,
      resultAggregateJoinCollectionsWithConditions,
      matchAggregationJoinConditions,
    };
  }

  async queryJoinRelationalCollection(aggregation, repository, buildCustomStateAggregation, options) {
    const { collection, selects, where, ...relation } = aggregation;
    const project = convertSelectedToProject(selects, options);
    const params = { match: where, project };
    const state = isFunction(buildCustomStateAggregation)
      ? buildCustomStateAggregation(params, collection)
      : { match: { $and: where }, project };

    if (isEqual(params.match, state.match)) state.match = { $and: params.match };

    const data = await repository(collection, [
      {
        $match: state.match,
      },
      {
        $project: state.project,
      },
    ]);

    if (isEmpty(relation.chainLookUps) || isEmpty(data)) return { ...aggregation, data };

    const { chainLookUps } = relation;

    const dataInCollectionMapper = new Map();
    dataInCollectionMapper.set(collection, data);

    const chainLookUpsWithData = [];

    // query chain look up data
    for (const chainLookUp of chainLookUps) {
      const partitionQueries = map(chainLookUp, (aggregate) => {
        // get selects from chainLookups
        const chainSelects = [];
        if (options) {
          const selectKeys = Object.keys(options);
          for (const selectKey of selectKeys) {
            const prefix = aggregate.asField + '.';
            if (selectKey.startsWith(prefix)) {
              chainSelects.push(selectKey.slice(prefix.length));
            }
          }
        }

        // add custom selects
        if (chainSelects.length) {
          for (const chainSelect of chainSelects) {
            aggregate.selects.push(chainSelect);
          }
        }

        const dataInCollections = dataInCollectionMapper.get(aggregate.from) || [];
        if (isEmpty(dataInCollections)) return Promise.resolve({ ...aggregate, data: [] });

        aggregate.where = getWhereConditionFromRelationalAggregation(dataInCollections, {
          localField: aggregate.localField,
          foreignField: aggregate.foreignField,
        });

        return this.queryChainLookUpData(aggregate, repository);
      });
      const promiseTaskPartitionQueryResults = await Promise.all(partitionQueries);
      const chainLookUpPartitionWithData = [];

      for (const promiseTaskPartitionQueryResult of promiseTaskPartitionQueryResults) {
        chainLookUpPartitionWithData.push(promiseTaskPartitionQueryResult);
        dataInCollectionMapper.set(promiseTaskPartitionQueryResult.collection, promiseTaskPartitionQueryResult.data);
      }

      chainLookUpsWithData.push(chainLookUpPartitionWithData);
    }

    // map chain look up data
    const joinCollection = {
      collection,
      selects,
      where: {},
      data,
      ...relation,
    };

    const reverseChainLookUpsWithData = chainLookUpsWithData.toReversed();
    reverseChainLookUpsWithData.push([joinCollection]);

    for (const [index, partitionChainLookUpWithData] of reverseChainLookUpsWithData.entries()) {
      const isFirstPartition = index === 0;

      if (isFirstPartition) continue;

      const previousPartitionChainLookUpDataGroupFromCollection = chain(reverseChainLookUpsWithData[index - 1])
        .map((previousPartitionChainLookUpData) => ({
          ...previousPartitionChainLookUpData,
          data: groupAndUnwindByRelationFieldData(
            previousPartitionChainLookUpData.data,
            previousPartitionChainLookUpData.localField,
          ),
        }))
        .groupBy('from')
        .value();

      const partitionChainLookUpWithDataMergePreviousPartitionData = partitionChainLookUpWithData.map(
        (partitionChainLookUp) => {
          const previousPartitionFromCollection =
            previousPartitionChainLookUpDataGroupFromCollection[partitionChainLookUp.collection] ?? [];

          const previousPartitionChainLookUps = previousPartitionFromCollection;
          const mergedRelationalData = partitionChainLookUp.data.map((item) => {
            const currentData = item;
            const mappingDataFromPreviousPartition = previousPartitionChainLookUps
              .filter((chainLookUp) => isEqual(chainLookUp.from, partitionChainLookUp.collection))
              .reduce((accumulate, chainLookUp) => {
                const foreignFieldValue = currentData[chainLookUp.foreignField];
                return merge(accumulate, {
                  [chainLookUp.asField]: has(chainLookUp.data, foreignFieldValue)
                    ? chainLookUp.data[foreignFieldValue]
                    : [],
                });
              }, {});

            return merge(currentData, mappingDataFromPreviousPartition);
          });
          return { ...partitionChainLookUp, data: mergedRelationalData };
        },
      );

      reverseChainLookUpsWithData[index] = partitionChainLookUpWithDataMergePreviousPartitionData;
    }

    const lastReverseChainLookUpPartition = last(reverseChainLookUpsWithData) || [];
    const result = { ...aggregation, data: lastReverseChainLookUpPartition[0]?.data || [] };

    dataInCollectionMapper.clear();
    reverseChainLookUpsWithData.splice(0, reverseChainLookUpsWithData.length);

    return result;
  }

  updateWhereConditionInAggregateJoinCollectionCollection(data, aggregateJoinCollections) {
    return map(aggregateJoinCollections, (collection) => {
      const dataInForeignFields = map(data, collection.foreignField);
      collection.where = [{ [collection.localField]: { $in: dataInForeignFields.flat() } }];
      return collection;
    });
  }

  mergeRelationalDataFromJoinCollectionToMainCollection(data, aggregateJoinCollections) {
    const dataOfJoinCollection = map(aggregateJoinCollections, (aggregate) => {
      const { asField, from, localField, foreignField } = aggregate;
      const groupObjectData = groupAndUnwindByRelationFieldData(aggregate.data, localField);
      return {
        data: groupObjectData,
        from,
        localField,
        foreignField,
        asField,
      };
    });

    const result = map(data, (item) => {
      const assignRelationalDataToAsField = reduce(
        dataOfJoinCollection,
        (accumulate, current) => {
          const { foreignField, asField, data: groupObjectData } = current;

          const keyFromMainCollection = get(item, foreignField);
          if (isUndefined(keyFromMainCollection)) return { ...accumulate, [asField]: [] };

          if (isString(keyFromMainCollection) || isNumber(keyFromMainCollection)) {
            accumulate[asField] = groupObjectData[keyFromMainCollection] || [];
          }

          if (isArray(keyFromMainCollection)) {
            accumulate[asField] =
              keyFromMainCollection
                .filter((key) => has(groupObjectData, key))
                .map((key) => groupObjectData[key])
                .flat() || [];
          }

          return accumulate;
        },
        {},
      );

      return merge(item, assignRelationalDataToAsField);
    });

    return result;
  }

  async queryJoinRelationalCollectionWithFilterCondition(aggregation, repository, buildCustomStateAggregation) {
    const { collection, selects, where, chainLookUps, ...relation } = aggregation;

    if (isEmpty(chainLookUps)) {
      const project = convertSelectedToProject(selects);
      const params = { match: where, project };
      const state = isFunction(buildCustomStateAggregation)
        ? buildCustomStateAggregation(params, collection)
        : { match: { $and: where }, project };

      if (isEqual(params.match, state.match)) state.match = { $and: params.match };

      const data = await repository(collection, [
        {
          $match: state.match,
        },
        {
          $project: state.project,
        },
      ]);
      return { ...aggregation, data };
    }

    const joinCollection = {
      collection,
      selects,
      where: {},
      ...relation,
      data: [],
    };
    const reversePartitionChainLookUps = reverse(chainLookUps);

    const dataInFromCollectionMapper = {};

    const reversePartitionChainLookUpWithDataClones = chain(chainLookUps)
      .reverse()
      .map((partitionLookUp) => partitionLookUp.map((p) => ({ ...p, data: [] })))
      .push([joinCollection])
      .value();

    // query chain look up data
    for (const [index, partitionChainLookUp] of reversePartitionChainLookUpWithDataClones.entries()) {
      const isFirstPartition = index === 0;
      const previousPartitionCollections = isFirstPartition ? [] : reversePartitionChainLookUps[index - 1];

      const groupPreviousAggregationCondition = reduce(
        previousPartitionCollections,
        (acc, cur) => {
          const { localField, foreignField, from } = cur;
          const data = dataInFromCollectionMapper[cur.collection] || [];

          const mainLocalFieldFromPreviousCollection = foreignField;
          const mainForeignFieldFromPreviousCollection = localField;
          const joinCondition = isEmpty(data)
            ? {}
            : getWhereConditionFromRelationalAggregation(data, {
                localField: mainLocalFieldFromPreviousCollection,
                foreignField: mainForeignFieldFromPreviousCollection,
              });

          acc[from] = merge(acc[from], joinCondition);

          return acc;
        },
        {},
      );

      const partitionQueries = map(partitionChainLookUp, async (aggregate) => {
        const previousJoinCondition = groupPreviousAggregationCondition[aggregate.collection] || {};

        if (isEmpty(previousJoinCondition) && !isFirstPartition) {
          dataInFromCollectionMapper[aggregate.collection] = [];
          return { ...aggregate, data: [] };
        }

        const whereConditions = [aggregate.where, previousJoinCondition].filter((v) => !isEmpty(v));
        if (isEmpty(whereConditions)) return { ...aggregate, data: [] };

        const project = convertSelectedToProject(aggregate.selects);
        const params = { match: whereConditions, project };
        const state = isFunction(buildCustomStateAggregation)
          ? buildCustomStateAggregation(params, aggregate.collection)
          : { match: { $and: whereConditions }, project };

        if (isEqual(params.match, state.match)) state.match = { $and: params.match };
        const data = await repository(aggregate.collection, [
          {
            $match: state.match,
          },
          {
            $project: state.project,
          },
        ]);

        return { ...aggregate, data };
      });

      const queryResults = await Promise.all(partitionQueries);
      queryResults.forEach((queryResult) => {
        dataInFromCollectionMapper[queryResult.collection] = queryResult.data;
      });

      reversePartitionChainLookUpWithDataClones[index] = queryResults;
    }

    const lastPartitionChainLookUp = last(reversePartitionChainLookUpWithDataClones) ?? [];

    return { ...aggregation, data: lastPartitionChainLookUp[0]?.data || [] };
  }

  reduceAggregateJoinCollection(accumulateJoinCollection, joinCollection) {
    const { collection, where } = joinCollection;

    if (has(accumulateJoinCollection, collection)) {
      const existingJoinCollection = accumulateJoinCollection[collection];
      if (!isEmpty(where)) {
        existingJoinCollection.where.push(where);
      }

      existingJoinCollection.selects = chain(existingJoinCollection.selects)
        .concat(joinCollection.selects)
        .uniq()
        .value();

      existingJoinCollection.chainLookUps = chain(existingJoinCollection.chainLookUps)
        .concat(joinCollection.chainLookUps)
        .reduce((acc, cur) => {
          const key = `${cur.collection}_${cur.from}_${cur.foreignField}`;

          if (_.has(acc, key)) {
            const _joinCollection = acc[key];
            _joinCollection.where = merge(_joinCollection.where, cur.where);
            _joinCollection.selects = uniq([..._joinCollection.selects, ...cur.selects]);
            acc[key] = _joinCollection;
            return acc;
          }

          acc[key] = omit(cur, 'chainLookUps');
          return acc;
        }, {})
        .values()
        .value();
      existingJoinCollection.chainLookUps = existingJoinCollection.chainLookUps.map((chainLookUp, index, array) => {
        const selects = array.filter((val) => val.from === chainLookUp.collection).map((val) => val.foreignField);
        chainLookUp.selects = uniq([...chainLookUp.selects, ...selects]);
        return chainLookUp;
      });
      accumulateJoinCollection[collection] = existingJoinCollection;
      return accumulateJoinCollection;
    }

    accumulateJoinCollection[collection] = merge(joinCollection, {
      where: isEmpty(where) ? [] : [where],
    });
    return accumulateJoinCollection;
  }

  reduceAndCombinedChainLookUps(aggregateJoinCollections) {
    const { chainLookUps } = aggregateJoinCollections;

    if (isEmpty(chainLookUps)) return aggregateJoinCollections;

    const [firstSequencePartitionChainLookUps, otherSequencePartitionChainLookUps] = partition(
      chainLookUps,
      (chainLookup) => isEqual(chainLookup.from, aggregateJoinCollections.collection),
    );

    const sequencePartitionChainLookUps = [];
    sequencePartitionChainLookUps.push(firstSequencePartitionChainLookUps);

    const currentPartitionSet = new Set(map(firstSequencePartitionChainLookUps, 'collection'));
    const nextPartitionChainList = otherSequencePartitionChainLookUps;

    while (!isEmpty(nextPartitionChainList)) {
      const [inChainLookUps, outChainLookUps] = partition(nextPartitionChainList, (v) =>
        currentPartitionSet.has(v.from),
      );

      if (isEqual(outChainLookUps.length, nextPartitionChainList.length) || isEmpty(inChainLookUps)) break;
      sequencePartitionChainLookUps.push(inChainLookUps);

      const collections = map(inChainLookUps, 'collection');

      currentPartitionSet.clear();
      collections.forEach((collection) => currentPartitionSet.add(collection));

      nextPartitionChainList.splice(0, nextPartitionChainList.length);
      outChainLookUps.forEach((chainLookUp) => nextPartitionChainList.push(chainLookUp));
    }

    return {
      ...aggregateJoinCollections,
      chainLookUps: sequencePartitionChainLookUps,
    };
  }

  operateColumnSettingCondition(groupField, isRequiredDefaultFilter) {
    const { columnCode, root, value, filterType, isFilter, isDefault, isDisplay, defaultValue } = groupField;

    let condition = {};
    if (isFilter && !isNil(value) && ((!isEmpty(value) && !isBoolean(value)) || isBoolean(value))) {
      const operatorType = transformColumnSettingFilterToFilterTypeExpress(filterType);
      const field = filter([root, columnCode], (v) => !isEmpty(v)).join('.');
      const state = createParameterExpress(operatorType, { field, value });
      condition = state;
    }

    if (isDefault && (defaultValue || isBoolean(defaultValue)) && isRequiredDefaultFilter) {
      const operatorType = 'equal';
      const field = filter([root, columnCode], (v) => !isEmpty(v)).join('.');
      const state = createParameterExpress(operatorType, { field, value: defaultValue });
      condition = state;
    }

    const select = isDisplay ? filter([root, columnCode], (v) => !isEmpty(v)).join('.') : '';
    return {
      select,
      where: !isEmpty(condition) ? condition : {},
    };
  }

  async queryChainLookUpData(aggregation, repository) {
    const { collection, selects, where } = aggregation;
    const match = Array.isArray(where) ? { $and: where } : where;

    const project = convertSelectedToProject(selects);
    const pipeline = [
      {
        $match: match,
      },
      {
        $project: project,
      },
    ];

    const data = await repository(collection, pipeline);
    return { ...aggregation, data };
  }

  operateColumnSettingJoinCondition(groupField, isRequiredDefaultFilter) {
    const { relationalPath, columnCode, root, filterType, isFilter, isDefault, defaultValue, value, isDisplay } =
      groupField;

    const relationalCollectionSections = String(relationalPath).split(',');

    const sectionSize = size(relationalCollectionSections);
    const states = map(relationalCollectionSections, (section, index) => {
      const [mainCollection, joinCollection] = section.split('->');

      const [nameOfMainCollection, ...fieldOfMainCollection] = mainCollection.split('.');
      const [nameOfJoinCollection, ...fieldOfJoinCollection] = joinCollection.split('.');

      const selects = [];
      const field = filter([root, columnCode], (v) => !isEmpty(v)).join('.');

      let condition = {};
      if (isFilter && !isNil(value) && ((!isEmpty(value) && !isBoolean(value)) || isBoolean(value))) {
        const operatorType = transformColumnSettingFilterToFilterTypeExpress(filterType);
        const state = createParameterExpress(operatorType, { field, value });
        condition = state;
      }

      if (isDefault && defaultValue && isRequiredDefaultFilter) {
        const operatorType = 'equal';
        const state = createParameterExpress(operatorType, { field, value: defaultValue });
        condition = state;
      }

      const isLastObjectKey = isEqual(sectionSize - 1, index);

      if (isLastObjectKey && (isDisplay || isDefault)) {
        selects.push(fieldOfJoinCollection.join('.'), field);
      } else {
        selects.push(fieldOfJoinCollection.join('.'));
      }

      return {
        collection: nameOfJoinCollection,
        selects,
        where: isEmpty(condition) ? {} : condition,
        from: nameOfMainCollection,
        foreignField: fieldOfMainCollection.join('.'),
        localField: fieldOfJoinCollection.join('.'),
        asField: camelCase(nameOfJoinCollection),
        chainLookUps: [],
      };
    });

    return states;
  }

  excludeFilterColumnSettings(filters, columnSettingKeys) {
    const result = {};
    if (!columnSettingKeys.length) return { filters, excludeData: result };

    const newFilter = [];
    for (const item of filters) {
      for (const data of item.data) {
        if (columnSettingKeys.includes(data.field)) {
          result[data.field] = data.value;
        } else {
          newFilter.push(item);
        }
      }
    }

    return { filters: newFilter, excludeData: result };
  }

  transformColumnSettingFilterType(params) {
    const { filters, columnSettings } = params;

    const result = filters.map(({ id, data }) => {
      const column = columnSettings.find((col) => col.key === id);
      const dataType = column?.groupFields.find((field) => field.isFilter)?.dataType;

      return {
        id,
        data: data.map(({ field, value }) => ({
          field,
          value: this.transformValueFilterType(dataType, value),
        })),
      };
    });

    return result;
  }

  transformValueFilterType(dataType, value) {
    switch (dataType) {
      case ColumnSettingDataTypeEnum.flag:
        return value === 'true';
      case ColumnSettingDataTypeEnum.enum:
        return value === 'true' || value === 'false' ? value === 'true' : value;
      case ColumnSettingDataTypeEnum.date:
        if (!Array.isArray(value)) return value;

        for (let index = 0; index < value.length; index++) {
          if (index === 0) {
            value[index] = date(value[index]).startOf('day').toDate();
          } else {
            value[index] = date(value[index]).endOf('day').toDate();
          }
        }
        return value;
      default:
        return value;
    }
  }
}

export default ColumnSettingService;
