import { DomainMetaDataEnum, ERROR_CODE } from '@constants/generic';
import { date, DateFormat } from '@infrastructure/dateUtils';
import { AppError } from '@infrastructure/utilities/AppError';

class CertificateService {
  constructor({ config, httpService, oauthService }) {
    this.config = config;
    this.endpoint = config.CERTIFICATE_API_ENDPOINT;
    this.http = httpService;
    this.oauthService = oauthService;
  }

  async getCertificateUrl(code) {
    const url = `${this.endpoint}/api/v1/user-certificates/${code}`;
    const options = {
      params: {
        retries: 3,
      },
    };

    const response = await this.http.get(url, options);

    if (response.status !== '000') {
      throw response;
    }

    const { cert_no, cert_pdf } = response.data;

    return {
      certificateCode: cert_no,
      certificatePDFUrl: cert_pdf,
    };
  }

  async create(payload) {
    const url = `${this.endpoint}/api/v1/user-certificates`;

    let retries = 0;
    const maxRetries = 3;
    let res;

    while (retries < maxRetries) {
      const token = await this.oauthService.getServerAccessToken();
      const options = {
        headers: { Authorization: `Bearer ${token}` },
      };
      res = await this.http.post(url, payload, options);

      if (res.httpStatus === 500) {
        if (res.status === 401 || res.status === 403) {
          await this.oauthService.requestNewAccessToken();
        }
        retries++;
      } else {
        break;
      }
    }

    if (res.httpStatus === 500) {
      throw new AppError(ERROR_CODE.SERVICE_UNAVAILABLE, res.data?.message);
    }

    const { certificateCode, certificateUrl, certificatePDFUrl } = res.data;
    return {
      certificateCode,
      certificateUrl,
      certificatePDFUrl,
    };
  }

  async pretestGenerateCertificate(payload) {
    const url = `${this.endpoint}/api/v1/user-certificates?pretest=true`;

    const token = await this.oauthService.getServerAccessToken();

    const options = {
      headers: { Authorization: `Bearer ${token}` },
    };

    const res = await this.http.post(url, payload, options);

    if (res.httpStatus === 500) {
      throw new AppError(ERROR_CODE.SERVICE_UNAVAILABLE, res.data?.message);
    }

    return res;
  }

  buildCertificatePayload(payload) {
    const {
      enrollmentId,
      courseName,
      slugName,
      salute,
      firstname,
      lastname,
      tsilicensetype,
      pillar,
      tsiCode,
      issuedDate,
      issuedBy,
      logoImageUrl,
      domainMetaData,
    } = payload;

    return {
      dynamic_value: {
        coursename: courseName,
        salute,
        firstname,
        lastname,
        tsilicensetype: tsilicensetype || '',
        pillar: pillar || '',
        coursecode: tsiCode || '',
        issueddate: issuedDate
          ? date(issuedDate).locale('th').format(DateFormat.buddhistFullDateWithLocale)
          : date().locale('th').format(DateFormat.buddhistFullDateWithLocale),
        issuedby: issuedBy || '',
        logo: logoImageUrl ? `${this.config.S3_BUCKET_URL}/${logoImageUrl}` : '',
      },
      slug_name: slugName,
      metadata_url: this.genMetaDataUrl(enrollmentId, domainMetaData),
    };
  }

  genMetaDataUrl(id, domain) {
    switch (domain) {
      case DomainMetaDataEnum.ENROLLMENT: {
        return `${this.endpoint}/enrollments-v1/enrollments/${id}/certMetaData`;
      }
      case DomainMetaDataEnum.LEARNING_PATH_ENROLLMENT: {
        return `${this.endpoint}/learning-path-enrollments-v1/learning-path-enrollments/${id}/certMetaData`;
      }
      default: {
        return '';
      }
    }
  }

  mergeProperties(certificateProperties, organizationCertificateProperties) {
    const organizationCertificatePropertiesMap = new Map(
      organizationCertificateProperties.map((prop) => [prop.key, prop]),
    );

    return certificateProperties.map((certificateProperty) => {
      if (!certificateProperty.isEnabledSetting) return certificateProperty;

      const organizationCertificatePropertiesMatch = organizationCertificatePropertiesMap.get(certificateProperty.key);
      if (organizationCertificatePropertiesMatch) {
        return {
          ...certificateProperty,
          type: organizationCertificatePropertiesMatch.type,
          columnSettingKey: organizationCertificatePropertiesMatch.columnSettingKey,
          value: organizationCertificatePropertiesMatch.value,
          mediaId: organizationCertificatePropertiesMatch.mediaId
            ? organizationCertificatePropertiesMatch.mediaId
            : null,
        };
      }

      return certificateProperty;
    });
  }
}

export default CertificateService;
