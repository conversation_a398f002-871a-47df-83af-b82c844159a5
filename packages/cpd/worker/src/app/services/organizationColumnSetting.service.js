import { DBCollection } from '@constants/dbCollection';
import BaseService from '@infrastructure/services/base.service';

class OrganizationColumnSettingService extends BaseService {
  constructor({ db, logger }) {
    super(db, DBCollection.organization_column_settings, logger);
  }

  async findColumnSettingWithTemplate(key) {
    const pipeline = [
      {
        $match: { key },
      },
      {
        $addFields: {
          template: '',
          code: '',
          isActive: true,
          isFilter: true,
          isTemplateRequired: true,
          isDefault: true,
          order: 0,
        },
      },
    ];

    const result = await this.aggregate(pipeline);
    return result;
  }
}

export default OrganizationColumnSettingService;
