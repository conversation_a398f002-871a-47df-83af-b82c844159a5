import { chain, filter, flatMap, isEqual } from 'lodash';

import { ColumnSettingNameCollectionEnum } from '@constants/columnSetting';

class BuildAggregateCertificateAdaptorService {
  constructor({ columnSettingService, userService }) {
    this.columnSettingService = columnSettingService;
    this.userService = userService;
  }

  async getUserColumnSettingData(organizationId, userId, columnSettings, mainRepository, repositoryFactory) {
    const { aggregateInMainCollection, aggregateJoinCollectionsWithNoneConditions } =
      this.columnSettingService.buildAggregationFromColumnSetting({
        filters: [],
        columnSettings,
      });

    const matchInMainCollection = this.userService.buildMatchStateFullNamePipeline(aggregateInMainCollection.match);
    const userPipeline = [
      {
        $match: {
          $and: [{ organizationId, guid: userId }, ...matchInMainCollection],
        },
      },
      {
        $sort: {
          updatedAt: -1,
        },
      },
      {
        $project: {
          _id: 0,
          guid: 1,
          ...aggregateInMainCollection.project,
        },
      },
    ];

    const result = await mainRepository(ColumnSettingNameCollectionEnum.USERS, userPipeline);

    const mergedAggregateJoinCollectionsWithNoneCondition =
      this.columnSettingService.updateWhereConditionInAggregateJoinCollectionCollection(
        result,
        aggregateJoinCollectionsWithNoneConditions,
      );
    const queryJoinCollection = mergedAggregateJoinCollectionsWithNoneCondition.map((joinCollectionRelation) =>
      this.columnSettingService.queryJoinRelationalCollection(joinCollectionRelation, (collection, pipeline) =>
        repositoryFactory(collection, pipeline),
      ),
    );

    const resultQueryJoinCollection = await Promise.all(queryJoinCollection);

    const mergeRelationalDataFromJoinCollectionToMainCollectionData =
      this.columnSettingService.mergeRelationalDataFromJoinCollectionToMainCollection(
        result,
        resultQueryJoinCollection,
      );

    return mergeRelationalDataFromJoinCollectionToMainCollectionData;
  }

  async getCourseColumnSettingData(
    organizationId,
    courseId,
    courseVersionId,
    columnSettings,
    mainRepository,
    repositoryFactory,
  ) {
    const { aggregateInMainCollection, aggregateJoinCollectionsWithNoneConditions } =
      this.columnSettingService.buildAggregationFromColumnSetting({
        filters: [],
        columnSettings,
      });

    const { matchAggregationJoinConditions, resultAggregateJoinCollectionsWithConditions } =
      await this.columnSettingService.buildAndQueryJoinAggregateCollectionWithFilterCondition(
        aggregateJoinCollectionsWithNoneConditions,
        (collection, pipeline) => repositoryFactory(collection, pipeline),
        (state, collection) => this.buildStateAggregationJoinCourseCollection(state, collection, courseVersionId),
      );
    const coursePipeline = [
      {
        $match: {
          $and: [
            { organizationId, id: courseId },
            ...aggregateInMainCollection.match,
            ...matchAggregationJoinConditions,
          ],
        },
      },
      {
        $sort: {
          updatedAt: -1,
        },
      },
      {
        $project: {
          _id: 0,
          id: 1,
          ...aggregateInMainCollection.project,
        },
      },
    ];

    const result = await mainRepository(ColumnSettingNameCollectionEnum.COURSES, coursePipeline);
    const mergedAggregateJoinCollectionsWithNoneCondition =
      this.columnSettingService.updateWhereConditionInAggregateJoinCollectionCollection(
        result,
        aggregateJoinCollectionsWithNoneConditions,
      );

    const queryJoinCollection = mergedAggregateJoinCollectionsWithNoneCondition.map((joinCollectionRelation) =>
      this.columnSettingService.queryJoinRelationalCollection(joinCollectionRelation, (collection, pipeline) =>
        repositoryFactory(collection, pipeline),
      ),
    );

    const resultQueryJoinCollection = await Promise.all(queryJoinCollection);
    const mergeRelationalDataFromJoinCollectionToMainCollectionData =
      this.columnSettingService.mergeRelationalDataFromJoinCollectionToMainCollection(
        result,
        resultQueryJoinCollection,
      );

    let filteredCourseVersions = [];
    if (resultAggregateJoinCollectionsWithConditions.length) {
      filteredCourseVersions = flatMap(
        filter(resultAggregateJoinCollectionsWithConditions, (item) =>
          isEqual(ColumnSettingNameCollectionEnum.COURSE_VERSIONS, item.collection),
        ),
        (item) => item.data,
      );
    }

    const newResult = this.transformCourseData(
      mergeRelationalDataFromJoinCollectionToMainCollectionData,
      filteredCourseVersions,
    );
    return newResult;
  }

  async getLearningPathColumnSettingData(
    organizationId,
    learningPathId,
    learningPathVersionId,
    columnSettings,
    mainRepository,
    repositoryFactory,
  ) {
    const { aggregateInMainCollection, aggregateJoinCollectionsWithNoneConditions } =
      this.columnSettingService.buildAggregationFromColumnSetting({
        filters: [],
        columnSettings,
      });

    const { matchAggregationJoinConditions, resultAggregateJoinCollectionsWithConditions } =
      await this.columnSettingService.buildAndQueryJoinAggregateCollectionWithFilterCondition(
        aggregateJoinCollectionsWithNoneConditions,
        (collection, pipeline) => repositoryFactory(collection, pipeline),
        (state, collection) =>
          this.buildStateAggregationJoinLearningPathCollection(state, collection, learningPathVersionId),
      );

    const learningPathPipeline = [
      {
        $match: {
          $and: [
            { organizationId, id: learningPathId },
            ...aggregateInMainCollection.match,
            ...matchAggregationJoinConditions,
          ],
        },
      },
      {
        $sort: {
          updatedAt: -1,
        },
      },
      {
        $project: {
          _id: 0,
          id: 1,
          ...aggregateInMainCollection.project,
        },
      },
    ];

    const result = await mainRepository(ColumnSettingNameCollectionEnum.LEARNING_PATHS, learningPathPipeline);
    const mergedAggregateJoinCollectionsWithNoneCondition =
      this.columnSettingService.updateWhereConditionInAggregateJoinCollectionCollection(
        result,
        aggregateJoinCollectionsWithNoneConditions,
      );

    const queryJoinCollection = mergedAggregateJoinCollectionsWithNoneCondition.map((joinCollectionRelation) =>
      this.columnSettingService.queryJoinRelationalCollection(joinCollectionRelation, (collection, pipeline) =>
        repositoryFactory(collection, pipeline),
      ),
    );

    const resultQueryJoinCollection = await Promise.all(queryJoinCollection);

    const mergeRelationalDataFromJoinCollectionToMainCollectionData =
      this.columnSettingService.mergeRelationalDataFromJoinCollectionToMainCollection(
        result,
        resultQueryJoinCollection,
      );

    let filteredLearningPathVersions = [];
    if (resultAggregateJoinCollectionsWithConditions.length) {
      filteredLearningPathVersions = flatMap(
        filter(resultAggregateJoinCollectionsWithConditions, (item) =>
          isEqual(ColumnSettingNameCollectionEnum.LEARNING_PATH_VERSIONS, item.collection),
        ),
        (item) => item.data,
      );
    }

    const newResult = this.transformLearningPathData(
      mergeRelationalDataFromJoinCollectionToMainCollectionData,
      filteredLearningPathVersions,
    );
    return newResult;
  }

  buildStateAggregationJoinCourseCollection(state, collection, courseVersionId) {
    if (collection === ColumnSettingNameCollectionEnum.COURSE_VERSIONS) {
      const newState = {
        ...state,
        match: {
          $and: [{ id: courseVersionId }, ...state.match],
        },
        project: {
          ...state.project,
          status: 1,
          id: 1,
        },
      };

      return newState;
    }
    return state;
  }

  buildStateAggregationJoinLearningPathCollection(state, collection, learningPathVersionId) {
    if (collection === ColumnSettingNameCollectionEnum.LEARNING_PATH_VERSIONS) {
      const newState = {
        ...state,
        match: {
          $and: [{ id: learningPathVersionId }, ...state.match],
        },
        project: {
          ...state.project,
          status: 1,
          id: 1,
        },
      };

      return newState;
    }
    return state;
  }

  transformCourseData(courses, filteredCourseVersions) {
    return chain(courses)
      .map((course) => {
        const courseVersion = course.courseVersions || [];
        if (!courseVersion) return null;

        return { ...course, courseVersions: filteredCourseVersions };
      })
      .compact()
      .value();
  }

  transformLearningPathData(learningPaths, filteredLearningPathVersions) {
    return chain(learningPaths)
      .map((learningPath) => {
        const learningPathVersion = learningPath.learningPathVersions || [];
        if (!learningPathVersion) return null;

        return { ...learningPath, learningPathVersions: filteredLearningPathVersions };
      })
      .compact()
      .value();
  }
}

export default BuildAggregateCertificateAdaptorService;
