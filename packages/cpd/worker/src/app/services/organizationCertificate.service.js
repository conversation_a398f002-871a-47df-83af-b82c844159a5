import { DBCollectionEnum } from '@iso/lms/enums/dbCollection.enum';

import BaseService from '@infrastructure/services/base.service';

class OrganizationCertificateService extends BaseService {
  constructor({ db, logger }) {
    super(db, DBCollectionEnum.ORGANIZATION_CERTIFICATES, logger);
  }

  async findWithCertificateDetail(id) {
    const result = await this.aggregate([
      {
        $match: {
          id,
        },
      },
      {
        $lookup: {
          from: 'certificates',
          localField: 'certificateId',
          foreignField: 'id',
          as: 'certificateDetail',
        },
      },
      {
        $unwind: {
          path: '$certificateDetail',
          preserveNullAndEmptyArrays: true,
        },
      },
    ]);

    return result[0];
  }
}

export default OrganizationCertificateService;
