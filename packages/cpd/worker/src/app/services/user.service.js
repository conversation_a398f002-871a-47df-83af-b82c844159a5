import { EnrollmentStatusEnum } from '@iso/lms/enums/enrollment.enum';
import { has, isEmpty, partition } from 'lodash';

import { DBCollection } from '@constants/dbCollection';
import { date } from '@infrastructure/dateUtils';
import BaseService from '@root/infrastructure/services/base.service';

class UserService extends BaseService {
  constructor({ db, logger }) {
    super(db, DBCollection.users, logger);
  }

  async find(filter = {}) {
    const res = await this.repository.find(filter).toArray();
    return res;
  }

  async findOne(filter = {}) {
    const res = await this.repository.findOne(filter);
    return res;
  }

  async save(entity, opts) {
    const { guid } = entity;

    entity.updatedAt = date().toDate();
    const option = { upsert: true };
    if (opts) {
      option.session = opts.session;
    }

    await this.repository.replaceOne({ guid }, entity, option);
  }

  async aggregate(filter) {
    const res = await this.repository.aggregate(filter).toArray();
    return res;
  }

  async getListStatusInProgressByCitizenIdOrUserIdList(citizenIdList, userIdList, organizationId) {
    const res = await this.aggregate([
      {
        $match: {
          $or: [
            {
              guid: {
                $in: userIdList,
              },
            },
            {
              citizenId: {
                $in: citizenIdList,
              },
            },
          ],
          organizationId,
        },
      },
      {
        $lookup: {
          from: 'enrollments',
          let: {
            userId: '$guid',
            citizenId: '$citizenId',
          },
          pipeline: [
            {
              $match: {
                $and: [
                  {
                    $expr: {
                      $eq: ['$userId', '$$userId'],
                    },
                  },
                  {
                    $or: [
                      {
                        $and: [{ status: EnrollmentStatusEnum.IN_PROGRESS }, { expiredAt: { $gt: date().toDate() } }],
                      },
                      {
                        $expr: {
                          $in: [
                            '$status',
                            [
                              EnrollmentStatusEnum.PENDING_RESULT,
                              EnrollmentStatusEnum.PASSED,
                              EnrollmentStatusEnum.PENDING_APPROVAL,
                              EnrollmentStatusEnum.VERIFIED,
                            ],
                          ],
                        },
                      },
                    ],
                  },
                ],
              },
            },
            {
              $lookup: {
                from: 'courses',
                localField: 'courseId',
                foreignField: 'id',
                as: 'course',
              },
            },
            {
              $unwind: '$course',
            },
            {
              $project: {
                id: 1,
                userId: '$$userId',
                citizenId: '$$citizenId',
                courseCode: '$course.code',
                courseId: 1,
                status: 1,
              },
            },
          ],
          as: 'enrollments',
        },
      },
      {
        $unwind: '$enrollments',
      },
      {
        $replaceRoot: {
          newRoot: '$enrollments',
        },
      },
    ]);

    return res;
  }

  async validateExistUser(citizenId) {
    const userResult = await this.repository.findOne({
      citizenId,
    });

    if (userResult) {
      return true;
    }

    return false;
  }

  async getUserBySalesId({ organizationId, salesId }) {
    const res = await this.findOne({ organizationId, 'additionalField.salesId': salesId });
    return res;
  }

  findStreamActiveUserIdsInOrganization({ organizationId }) {
    return this.repository.find(
      {
        organizationId,
        active: true,
        isTerminated: false,
      },
      {
        projection: {
          _id: 0,
          guid: 1,
          email: 1,
          'profile.firstname': 1,
          'profile.lastname': 1,
        },
      },
    );
  }

  findActiveUserIdWithIds(ids) {
    return this.repository
      .aggregate([
        {
          $match: {
            guid: { $in: ids },
            active: true,
            isTerminated: false,
          },
        },
        { $project: { guid: 1, email: 1, fullName: { $concat: ['$profile.firstname', ' ', '$profile.lastname'] } } },
      ])
      .toArray();
  }

  buildMatchStateFullNamePipeline(conditions) {
    const [fullNameCondition, otherConditions] = partition(
      conditions,
      (condition) => has(condition, 'profile.firstname') || has(condition, 'profile.lastname'),
    );
    if (isEmpty(fullNameCondition)) return otherConditions;

    if (fullNameCondition.length === 1) {
      const [condition] = fullNameCondition;
      const key = 'profile.firstname';
      return [
        {
          $or: [{ 'profile.firstname': condition[key] }, { 'profile.lastname': condition[key] }],
        },
        ...otherConditions,
      ];
    }

    return [{ $and: fullNameCondition }, ...otherConditions];
  }
}

export default UserService;
