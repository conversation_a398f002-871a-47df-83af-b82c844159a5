import { UserNotificationTypeEnum } from '@iso/constants/userNotification';
import {
  buildAssignEnrollmentCompletedNotificationMessage,
  buildLearningPathCompleteWithCertificateNotificationMessage,
  buildLearningPathExpandExpiryDayNotificationMessage,
} from '@iso/helpers/userNotification';
import { CertificatePropertyModuleEnum, CertificatePropertyTypeEnum } from '@iso/lms/enums/certificate.enum';
import { DBCollectionEnum } from '@iso/lms/enums/dbCollection.enum';
import { OrganizationStorageTypeEnum } from '@iso/lms/enums/organizationStorage.enum';
import dayjs from 'dayjs';
import { groupBy, isEqual, isNil, isNull, keyBy } from 'lodash';

import { DBCollection } from '@constants/dbCollection';
import { BulkStatus, DomainMetaDataEnum, GenericStatusCodes } from '@constants/generic';
import { buildUserNotificationModel } from '@domains/userNotification.domain';
import { date, DateFormat, getDateLocale } from '@infrastructure/dateUtils';
import { LearningPathEnrollmentStatusEnum } from '@root/constants/learningPathEnrollment';
import { LearningPathVersionStatusEnum } from '@root/constants/learningPathVersion';
import { PreAssignContentTypeEnum } from '@root/constants/preAssignContent';
import { AppError } from '@root/infrastructure/utilities/AppError';

const MAX_EXPIRE_LEARNING_PATH_ENROLLMENT_DAY = 30;

class BulkEditLearningPathEnrollmentExpireDateUsecase {
  constructor({
    db,
    dbClient,
    logger,
    userService,
    roundService,
    preAssignContentService,
    userNotificationService,
    certificateService,
    organizationService,
    notificationService,
    learningPathService,
    learningPathVersionService,
    learningPathEnrollmentService,
    syncCourseLearningProgressService,
    learningPathEnrollmentCertificateService,
    organizationCertificateService,
    organizationStorageService,
    mediaService,
    buildAggregateCertificateAdaptorService,
    columnSettingService,
    organizationColumnSettingService,
    courseService,
    courseVersionService,
  }) {
    this.db = db;
    this.dbClient = dbClient;

    // Service
    this.userService = userService;
    this.roundService = roundService;
    this.preAssignContentService = preAssignContentService;
    this.userNotificationService = userNotificationService;
    this.certificateService = certificateService;
    this.organizationService = organizationService;
    this.notificationService = notificationService;
    this.learningPathService = learningPathService;
    this.learningPathVersionService = learningPathVersionService;
    this.learningPathEnrollmentService = learningPathEnrollmentService;
    this.learningPathEnrollmentCertificateService = learningPathEnrollmentCertificateService;
    this.syncCourseLearningProgressService = syncCourseLearningProgressService;
    this.organizationCertificateService = organizationCertificateService;
    this.organizationStorageService = organizationStorageService;
    this.mediaService = mediaService;
    this.buildAggregateCertificateAdaptorService = buildAggregateCertificateAdaptorService;
    this.columnSettingService = columnSettingService;
    this.organizationColumnSettingService = organizationColumnSettingService;
    this.courseService = courseService;
    this.courseVersionService = courseVersionService;

    // Config
    this.logger = logger;
  }

  async execute(msg, channel, { organization }) {
    const { domain, id: organizationId } = organization;
    const { jobId } = msg.properties.headers;

    const { payload, allPayload } = JSON.parse(msg.content.toString());

    this.logger.info(`Start processing the job ID ${jobId} of type EDIT_LEARNING_PATH_EXPIRE_DATE`);

    const { messageCount } = await channel.assertQueue(domain);
    const session = this.dbClient.startSession();

    session.startTransaction();
    try {
      await this.editLearningPathExpireDateTransaction(organizationId, jobId, payload, allPayload, session);
      await session.commitTransaction();

      channel.ack(msg);
    } catch (error) {
      this.logger.error(`| Something wrong, the job ID ${jobId} found error, errorMessage: ${error.message}`);
      await session.abortTransaction();

      channel.nack(msg, false, false);
    } finally {
      const isQueueFinished = messageCount === 0;
      if (isQueueFinished) {
        const { errorList } = await this.db.collection(DBCollection.jobs).findOne({
          guid: jobId,
        });
        await this.db.collection(DBCollection.jobs).updateOne(
          {
            guid: jobId,
          },
          {
            $set: {
              status: errorList && errorList.length ? BulkStatus.ERROR : BulkStatus.COMPLETED,
              updatedAt: date().toDate(),
            },
          },
        );
      }
      session.endSession();
    }
  }

  async editLearningPathExpireDateTransaction(organizationId, jobId, payload, allPayload, session) {
    const { username, learningPathCode, extendedDate: extendedDateText } = payload;
    let round = null;

    const organization = await this.organizationService.findOne({
      id: organizationId,
    });

    if (!organization) {
      throw new AppError(GenericStatusCodes.ORGANIZATION_NOT_FOUND, 'organization not found');
    }

    const userData = await this.userService.findOne({
      organizationId,
      username,
      active: true,
    });

    const learningPathData = await this.learningPathService.findOne({
      organizationId,
      code: learningPathCode,
      isEnabled: true,
    });

    const { guid: userId } = userData ?? {};
    const { id: learningPathId } = learningPathData ?? {};

    const learningPathEnrollmentData = await this.learningPathEnrollmentService.findLatestLearningPathEnrollment(
      organizationId,
      learningPathId,
      userId,
    );

    const {
      id: learningPathEnrollmentId,
      status: learningPathEnrollmentStatus,
      contentProgress: oldContentsProgress,
      startedAt,
      expiredAt,
      version,
      roundId,
    } = learningPathEnrollmentData ?? {};

    const learningPathVersionData = await this.learningPathVersionService.findOne({
      learningPathId,
      version,
    });

    if (roundId) {
      round = await this.roundService.findOne({ id: roundId });
    }

    const { status: learningPathVersionStatus, expiryDay } = learningPathVersionData ?? {};
    const currentDate = date().toDate();
    const isLearningPathEnrollment = !!learningPathEnrollmentData;
    const isLearningPath = !!learningPathData;
    const isLearningPathValid = isLearningPath && learningPathVersionStatus !== LearningPathVersionStatusEnum.DRAFT;
    const isUser = !!userData;
    const isExtendedDateValid = this.validateDateFormat(extendedDateText);
    const extendedDate = this.convertToDate(extendedDateText);

    const errorMessages = this.validateEditLearningPathEnrollmentExpireDate({
      isUser,
      isLearningPathValid,
      isLearningPathEnrollment,
      isExtendedDateValid,
      learningPathEnrollmentStatus,
      extendedDate,
      expiryDay,
      expiredAt,
      startedAt,
      allPayload,
      payload,
    });

    if (errorMessages.length) {
      await this.db.collection(DBCollection.jobs).updateOne(
        {
          guid: jobId,
        },
        {
          $push: {
            rawPayloads: [...payload.raw, errorMessages],
            errorList: {
              originalPayload: payload,
              message: errorMessages,
            },
          },
        },
      );
      return;
    }

    let learningPathEnrollmentCertificate = null;
    let generateLearningPathCertificate = null;
    let isLearningPathCompleted = false;
    const userNotificationBulkWriteOperations = [];
    const assignEnrollmentCompleteNotifications = [];
    const learningPathEnrollmentSuccessNotifications = [];
    const learningPathEnrollmentExpandExpiryDayNotifications = [];

    const courses = await this.syncCourseLearningProgressService.getCourseListByContents({
      contents: learningPathVersionData.contents,
      organizationId,
    });

    if (!isEqual(LearningPathEnrollmentStatusEnum.EXPIRED, learningPathEnrollmentStatus)) {
      learningPathEnrollmentData.expiredAt = extendedDate;

      await this.learningPathEnrollmentService.save(learningPathEnrollmentData, {
        session,
      });
    } else {
      const courseIds = courses.map((course) => course.id);
      const enrollments = await this.syncCourseLearningProgressService.getEnrollmentsByContents({
        userId: userData.guid,
        courseIds,
      });

      const preEnrollments = await this.syncCourseLearningProgressService.getPreEnrollmentsByContents({
        organizationId,
        userId: userData.guid,
        courseIds,
      });

      const learningPathContentProgress = this.syncCourseLearningProgressService.prepareContentProgress({
        contents: learningPathVersionData.contents,
        courses,
        enrollments,
        preEnrollments,
        oldContentsProgress,
      });

      const enrollmentGroupById = keyBy(enrollments, 'id');
      const enrollmentSyncProgress = learningPathContentProgress
        .map((content) => {
          if (!content?.enrollmentId) return null;
          return enrollmentGroupById[content.enrollmentId];
        })
        .filter((enrollment) => !isNil(enrollment));

      const { status: newLearningPathEnrollmentStatus, totalCompleted: summaryCompleteCourse } =
        this.syncCourseLearningProgressService.getStatusAndCountCompleteCourseByContentProgress(
          learningPathContentProgress,
          learningPathVersionData.contents.length,
        );

      const isLearningPathEnrollmentStatusComplete =
        newLearningPathEnrollmentStatus === LearningPathEnrollmentStatusEnum.COMPLETED;

      if (learningPathVersionData.isCertificateEnabled) {
        learningPathEnrollmentCertificate = await this.db
          .collection(DBCollection.learning_path_enrollment_certificates)
          .findOne({
            learningPathEnrollmentId,
          });
      }

      const isOldPassCriteriaCertificate = !!learningPathEnrollmentCertificate?.certificateUrl;
      let isNewPassCriteriaCertificate = false;

      if (!isOldPassCriteriaCertificate) {
        isNewPassCriteriaCertificate = this.learningPathEnrollmentCertificateService.validateIsPassCriteriaCertificate({
          courses,
          enrollments: enrollmentSyncProgress,
          learningPathContent: learningPathVersionData.contents,
        });
      }

      const isPassCriteriaCertificate = isOldPassCriteriaCertificate || isNewPassCriteriaCertificate;
      const isPassLearningPath =
        (learningPathVersionData.isCertificateEnabled && isPassCriteriaCertificate) ||
        (!learningPathVersionData.isCertificateEnabled && isLearningPathEnrollmentStatusComplete);

      // update learning path enrolment
      learningPathEnrollmentData.contentProgress = learningPathContentProgress;
      learningPathEnrollmentData.status = newLearningPathEnrollmentStatus;
      learningPathEnrollmentData.completedContentItem = summaryCompleteCourse;
      learningPathEnrollmentData.expiredAt = extendedDate;
      learningPathEnrollmentData.finishedAt = isLearningPathEnrollmentStatusComplete ? currentDate : null;
      learningPathEnrollmentData.passedAt = isPassLearningPath ? currentDate : null;

      await this.learningPathEnrollmentService.save(learningPathEnrollmentData, {
        session,
      });

      if (isLearningPathEnrollmentStatusComplete) {
        const preAssignContent = await this.preAssignContentService.findOne({
          id: learningPathEnrollmentData.preAssignContentId,
          organizationId: learningPathEnrollmentData.organizationId,
        });

        const assignee = isNull(preAssignContent)
          ? null
          : await this.userService.findOne({ guid: preAssignContent.createdByUserId, organizationId });

        // send email and in-app learning path enrollment completed to supervisor
        if (assignee) {
          const assignEnrollmentCompleteMailPayloadParams = {
            learnerUserId: userData.guid,
            contentName: learningPathVersionData.name,
            contentType: PreAssignContentTypeEnum.LEARNING_PATH,
            fullName: `${assignee.profile.firstname ?? ''} ${assignee.profile.lastname ?? ''}`.trim(),
            learnerFullName: `${userData.profile.firstname ?? ''} ${userData.profile.lastname ?? ''}`.trim(),
            round: {
              roundDate: round?.roundDate,
              expiredDate: learningPathEnrollmentData.expiredAt,
            },
          };

          const assignEnrollmentCompleteMailPayload = this.notificationService.assignEnrollmentCompleteEmail(
            assignee.email,
            assignEnrollmentCompleteMailPayloadParams,
            organization,
          );

          const assignEnrollmentCompleteUserNotification =
            await this.buildAssignLearningPathEnrollmentCompleteMessageAndInAppUserNotificationModel({
              learningPath: learningPathData,
              learningPathVersion: learningPathVersionData,
              learningPathEnrollment: learningPathEnrollmentData,
              round,
              assignee,
              fullName: `${userData.profile.firstname ?? ''} ${userData.profile.lastname ?? ''}`.trim(),
            });

          assignEnrollmentCompleteNotifications.push({
            emailPayload: assignEnrollmentCompleteMailPayload,
            inAppPayload: assignEnrollmentCompleteUserNotification.userNotification,
            organization,
          });

          if (assignEnrollmentCompleteUserNotification.userNotification) {
            userNotificationBulkWriteOperations.push({
              insertOne: {
                document: {
                  ...assignEnrollmentCompleteUserNotification.userNotification,
                },
              },
            });
          }
        }
      }

      const isGenerateCertificate = learningPathVersionData.isCertificateEnabled && isNewPassCriteriaCertificate;
      isLearningPathCompleted = learningPathEnrollmentData.status === LearningPathEnrollmentStatusEnum.COMPLETED;

      if (isGenerateCertificate && learningPathEnrollmentCertificate) {
        const { organizationCertificateId } = learningPathVersionData.certificate;

        const organizationCertificate =
          await this.organizationCertificateService.findWithCertificateDetail(organizationCertificateId);

        const { certificateDetail } = organizationCertificate;
        const certificateProperties = certificateDetail.properties;
        const organizationCertificateProperties = organizationCertificate.properties;
        const resultProperties = this.certificateService.mergeProperties(
          certificateProperties,
          organizationCertificateProperties,
        );
        const { slugName } = certificateDetail;

        const dynamicValue = await this.getCertificateDynamicValue({
          resultProperties,
          organizationId: organization.id,
          userId: userData.guid,
          courseId: null,
          courseVersionId: null,
          learningPathId: learningPathVersionData.learningPathId,
          learningPathVersionId: learningPathVersionData.id,
        });

        const certificatePayload = {
          dynamic_value: dynamicValue,
          slug_name: slugName,
          metadata_url: this.certificateService.genMetaDataUrl(
            learningPathEnrollmentCertificate.enrollmentId,
            DomainMetaDataEnum.LEARNING_PATH_ENROLLMENT,
          ),
        };

        generateLearningPathCertificate = await this.certificateService.create(certificatePayload);

        const { certificateUrl, certificateCode } = generateLearningPathCertificate;

        learningPathEnrollmentCertificate.certificateUrl = certificateUrl;
        learningPathEnrollmentCertificate.certificateCode = certificateCode;
        learningPathEnrollmentCertificate.isSentEmail = true;

        await this.learningPathEnrollmentCertificateService.save(learningPathEnrollmentCertificate, {
          session,
        });
      }
    }

    // send email and in-app learning path completed with certificate
    const certificateRefName = learningPathEnrollmentCertificate?.payload?.refName || '';

    const learningPathCompleteMailPayload = await this.notificationService.learningPathCompleteWithCertificateEmail(
      userData.email,
      {
        learningPathName: learningPathVersionData.name,
        learningPathCode: learningPathData.code,
        fullName: `${userData?.profile?.firstname} ${userData?.profile?.lastname}`,
        certificateUrl: generateLearningPathCertificate?.certificateUrl,
        certificatePDFUrl: generateLearningPathCertificate?.certificatePDFUrl,
        certificateCode: generateLearningPathCertificate?.certificateCode,
        refName: certificateRefName,
        isCertificate: !!generateLearningPathCertificate,
        isComplete: isLearningPathCompleted,
      },
      organization,
    );

    const learningPathEnrollmentSuccessUserNotification =
      this.buildLearningPathEnrollmentSuccessInAppNotificationMessageAndModel({
        userId,
        organizationId,
        learningPathCode,
        learningPathName: learningPathVersionData.name,
        learningPathThumbnailId: learningPathData.thumbnailMediaId,
        learningPathEnrollmentId,
        isCertificate: !!generateLearningPathCertificate,
        isComplete: isLearningPathCompleted,
        certificateUrl: learningPathEnrollmentCertificate?.certificateUrl,
      });

    learningPathEnrollmentSuccessNotifications.push({
      emailPayload: learningPathCompleteMailPayload,
      inAppPayload: learningPathEnrollmentSuccessUserNotification.userNotification,
      organization,
    });

    if (learningPathEnrollmentSuccessUserNotification.userNotification) {
      userNotificationBulkWriteOperations.push({
        insertOne: {
          document: {
            ...learningPathEnrollmentSuccessUserNotification.userNotification,
          },
        },
      });
    }

    // notification email and in-app learning path enrollment expand expiry day
    const courseNames = courses.map((item) => item.courseVersion.name);
    const extendLearningPathMailPayload = await this.notificationService.learningPathExtendExpireDateEmail(
      userData.email,
      {
        fullName: `${userData.profile?.firstname} ${userData.profile?.lastname}`,
        learningPathName: learningPathVersionData.name,
        courseNames,
        expiredDate: extendedDate,
        learningPathCode,
      },
      organization,
    );

    const learningPathEnrollmentExpandExpiryDayUserNotification =
      this.buildLearningPathExpandExpiryDayInAppNotificationMessageAndModel({
        userId,
        organizationId,
        learningPathCode: learningPathData.code,
        learningPathName: learningPathVersionData.name,
        learningPathThumbnailId: learningPathData.thumbnailMediaId,
        learningPathEnrollmentId: learningPathEnrollmentData.id,
        expiredAt: extendedDate,
      });

    learningPathEnrollmentExpandExpiryDayNotifications.push({
      emailPayload: extendLearningPathMailPayload,
      inAppPayload: learningPathEnrollmentExpandExpiryDayUserNotification.userNotification,
      organizationId,
    });

    if (learningPathEnrollmentExpandExpiryDayUserNotification.userNotification) {
      userNotificationBulkWriteOperations.push({
        insertOne: {
          document: {
            ...learningPathEnrollmentExpandExpiryDayUserNotification.userNotification,
          },
        },
      });
    }

    // save in-app user notification
    if (userNotificationBulkWriteOperations.length) {
      await this.db
        .collection(DBCollection.user_notifications)
        .bulkWrite(userNotificationBulkWriteOperations, { session });
    }

    this.sendEmailAndMessageByNotificationList(assignEnrollmentCompleteNotifications);
    this.sendEmailAndMessageByNotificationList(learningPathEnrollmentSuccessNotifications);
    this.sendEmailAndMessageByNotificationList(learningPathEnrollmentExpandExpiryDayNotifications);
  }

  validateEditLearningPathEnrollmentExpireDate(params) {
    const {
      isUser,
      isLearningPathValid,
      isLearningPathEnrollment,
      isExtendedDateValid,
      extendedDate,
      learningPathEnrollmentStatus,
      expiryDay,
      expiredAt,
      startedAt,
      allPayload,
      payload,
    } = params;
    const currentDate = date().toDate();
    const errorMessages = [];

    if (!isUser) {
      errorMessages.push('บัญชีผู้ใช้งานไม่ถูกต้อง');
    }

    if (!isLearningPathValid) {
      errorMessages.push('รหัสแผนการเรียนรู้ไม่ถูกต้อง');
    }

    if (!isExtendedDateValid) {
      errorMessages.push('รูปแบบวันที่ไม่ถูกต้อง');
    } else if (!this.validateMinExtendedDate(extendedDate, currentDate)) {
      errorMessages.push('วันที่สิ้นสุดการเรียนต้องมากกว่าหรือเท่าวันที่ในปัจจุบัน');
    }

    if (isUser && isLearningPathValid) {
      if (!isLearningPathEnrollment) {
        errorMessages.push('ไม่พบการลงทะเบียนในแผนการเรียนรู้นี้');
      } else {
        if (!expiryDay > 0) {
          errorMessages.push('ไม่สามารถขยายเวลาเรียนได้ เนื่องจากแผนการเรียนรู้นี้ไม่ได้ตั้งค่าวันที่สิ้นสุด');
        }
        if (learningPathEnrollmentStatus === LearningPathEnrollmentStatusEnum.COMPLETED) {
          errorMessages.push('ไม่สามารถขยายเวลาเรียนได้ เนื่องจากจบแผนการเรียนรู้');
        }
        if (learningPathEnrollmentStatus === LearningPathEnrollmentStatusEnum.CANCELED) {
          errorMessages.push('ไม่สามารถขยายเวลาเรียนได้ เนื่องจากแผนการเรียนรู้ถูกยกเลิกการเรียน');
        }
        if (learningPathEnrollmentStatus === LearningPathEnrollmentStatusEnum.EXPIRED) {
          if (!this.validateExpireDateWithinMaxDay(expiredAt, currentDate)) {
            errorMessages.push('วันที่ทำรายการเกิน 30 วันนับจากวันที่สิ้นสุดการเรียนเดิม');
          }
        }
        if (learningPathEnrollmentStatus === LearningPathEnrollmentStatusEnum.PRE_ASSIGN) {
          if (isExtendedDateValid && extendedDate <= startedAt) {
            errorMessages.push('วันที่สิ้นสุดการเรียนต้องมากกว่าวันที่เริ่มเรียน');
          }
        }
      }
    }

    if (!this.validateEditDuplicateExpireDate(allPayload, payload)) {
      errorMessages.push('ตรวจพบรายการขยายเวลาเรียนซ้ำภายในไฟล์');
    }

    return errorMessages;
  }

  convertToDate(dateText) {
    const buddhistDate = date(dateText, DateFormat.dayMonthYearWithLeadingZero);
    const newDate = new Date();
    newDate.setFullYear(buddhistDate.year() - 543, buddhistDate.month(), buddhistDate.date());
    return date(newDate).endOf('day').toDate();
  }

  validateDateFormat(date) {
    return dayjs(date, DateFormat.dayMonthYearWithLeadingZero, true).isValid();
  }

  validateMinExtendedDate(extendedDate, currentDate) {
    return date(extendedDate).startOf('day').toDate() >= date(currentDate).startOf('day').toDate();
  }

  validateExpireDateWithinMaxDay(expiredAt, currentDate) {
    const differenceInDays = date(currentDate).diff(expiredAt, 'day');
    return differenceInDays <= MAX_EXPIRE_LEARNING_PATH_ENROLLMENT_DAY;
  }

  validateEditDuplicateExpireDate(expireDateAllData, expireDateData) {
    const { username, learningPathCode } = expireDateData;
    const groupedExpireDates = groupBy(expireDateAllData, (data) => [data.username, data.learningPathCode]);
    const duplicateDataGroups = Object.values(groupedExpireDates).filter((group) => group.length > 1);
    return !duplicateDataGroups
      .flat()
      .some((item) => item.username === username && item.learningPathCode === learningPathCode);
  }

  sendEmailAndMessageByNotificationList(notificationList) {
    for (const item of notificationList) {
      const { emailPayload, inAppPayload, organizationId } = item;
      if (emailPayload) {
        this.notificationService.sendEmail(emailPayload);
      }
      if (inAppPayload) {
        this.notificationService.sendUserNotificationInApplication(inAppPayload.payload.message || '', organizationId);
      }
    }
  }

  buildLearningPathEnrollmentSuccessInAppNotificationMessageAndModel({
    userId,
    organizationId,
    learningPathCode,
    learningPathName,
    learningPathThumbnailId,
    learningPathEnrollmentId,
    isCertificate,
    isComplete,
    certificateUrl,
  }) {
    let userNotification = null;
    let message = '';
    let type;

    if (!isCertificate && !isComplete) {
      return {
        message,
        userNotification,
      };
    }

    if (isCertificate && isComplete) {
      type = UserNotificationTypeEnum.LEARNING_PATH_ENROLLMENT_COMPLETED_WITH_CERTIFICATE;
    } else if (isCertificate) {
      type = UserNotificationTypeEnum.LEARNING_PATH_ENROLLMENT_WITH_CERTIFICATE;
    } else {
      type = UserNotificationTypeEnum.LEARNING_PATH_ENROLLMENT_COMPLETED;
    }

    message = buildLearningPathCompleteWithCertificateNotificationMessage({
      contentName: learningPathName,
      isComplete,
      isCertificate,
    });

    userNotification = buildUserNotificationModel(userId, organizationId, type, {
      mediaId: learningPathThumbnailId,
      message,
      url: {
        code: learningPathCode,
        learningPathEnrollmentId,
        certificateUrl,
      },
    });

    return {
      message,
      userNotification,
    };
  }

  async buildAssignLearningPathEnrollmentCompleteMessageAndInAppUserNotificationModel({
    learningPath,
    learningPathVersion,
    learningPathEnrollment,
    round,
    assignee,
    fullName,
  }) {
    const message = buildAssignEnrollmentCompletedNotificationMessage({
      fullName: fullName || '',
      contentName: learningPathVersion.name,
      roundDate: round?.roundDate,
      expiryDay: learningPathVersion?.expiryDay || 0,
    });

    const userNotification = buildUserNotificationModel(
      assignee.guid, //supervisor
      learningPathEnrollment.organizationId,
      UserNotificationTypeEnum.ASSIGN_LEARNING_PATH_ENROLLMENT_COMPLETED,
      {
        mediaId: learningPath.thumbnailMediaId,
        message,
        url: {
          code: learningPath.code,
          userId: learningPathEnrollment.userId,
          learningPathEnrollmentId: learningPathEnrollment.id,
        },
      },
    );

    return { message, userNotification };
  }
  buildLearningPathExpandExpiryDayInAppNotificationMessageAndModel({
    userId,
    organizationId,
    learningPathCode,
    learningPathName,
    learningPathThumbnailId,
    learningPathEnrollmentId,
    expiredAt,
  }) {
    let userNotification = null;
    let message = '';
    const type = UserNotificationTypeEnum.LEARNING_PATH_EXPAND_EXPIRY_DAY;

    message = buildLearningPathExpandExpiryDayNotificationMessage({
      contentName: learningPathName,
      expiredAt,
    });

    userNotification = buildUserNotificationModel(userId, organizationId, type, {
      mediaId: learningPathThumbnailId,
      message,
      url: {
        code: learningPathCode,
        learningPathEnrollmentId,
      },
    });

    return {
      message,
      userNotification,
    };
  }

  async mainRepositoryFactory(collection, pipeline) {
    switch (collection) {
      case DBCollectionEnum.USERS: {
        return this.userService.aggregate(pipeline);
      }
      case DBCollectionEnum.COURSES: {
        return this.courseService.aggregate(pipeline);
      }
      case DBCollectionEnum.LEARNING_PATHS: {
        return this.learningPathService.aggregate(pipeline);
      }
      default: {
        return [];
      }
    }
  }

  async repositoryFactory(collection, pipeline) {
    switch (collection) {
      case DBCollectionEnum.USERS: {
        return this.userService.aggregate(pipeline);
      }
      case DBCollectionEnum.DEPARTMENTS: {
        return this.departmentService.aggregate(pipeline);
      }
      case DBCollectionEnum.USER_DIRECT_REPORTS: {
        return this.userDirectReportsService.aggregate(pipeline);
      }
      case DBCollectionEnum.LICENSES: {
        return this.licenseService.aggregate(pipeline);
      }
      case DBCollectionEnum.LEARNING_PATH_VERSIONS: {
        return this.learningPathVersionService.aggregate(pipeline);
      }
      case DBCollectionEnum.COURSE_VERSIONS: {
        return this.courseVersionService.aggregate(pipeline);
      }
      default: {
        return [];
      }
    }
  }

  async getCertificateDynamicValue(params) {
    const {
      resultProperties,
      organizationId,
      userId,
      courseId,
      courseVersionId,
      learningPathId,
      learningPathVersionId,
    } = params;

    const currentDate = date().toDate();
    const transformedCertificatePropertiesData = resultProperties.map((item) => {
      const key = ['t', 'i'].some((prefix) => item.key.startsWith(prefix)) ? item.key.slice(1) : item.key;
      const columnSettingParts = item.columnSettingKey ? item.columnSettingKey.split('.') : [null, null];

      return {
        key,
        columnSettingModule: columnSettingParts[0],
        columnSettingKey: item.columnSettingKey,
        type: item.type,
        value: item.value,
        mediaId: item.mediaId ? item.mediaId : null,
        name: item.name,
      };
    });

    const dynamicValue = {};
    for (const item of transformedCertificatePropertiesData) {
      if (item.type === CertificatePropertyTypeEnum.CURRENT_DATE) {
        dynamicValue[item.key] = getDateLocale(item.value, currentDate);
      } else if (item.type === CertificatePropertyTypeEnum.COLUMN_SETTING) {
        const columnSettings = await this.columnSettingService.findColumnSettingWithTemplate(item.columnSettingKey);

        const organizationColumnSettings = await this.organizationColumnSettingService.findColumnSettingWithTemplate(
          organizationId,
          item.columnSettingKey,
        );

        const columnSettingData = columnSettings.length > 0 ? columnSettings : organizationColumnSettings;

        if (item.columnSettingModule === CertificatePropertyModuleEnum.USER) {
          const userData = await this.buildAggregateCertificateAdaptorService.getUserColumnSettingData(
            organizationId,
            userId,
            columnSettings,
            (collection, pipeline) => this.mainRepositoryFactory(collection, pipeline),
            (collection, pipeline) => this.repositoryFactory(collection, pipeline),
          );
          const userValueByKey = this.columnSettingService.getValueByColumnSettingKey(
            item.columnSettingKey,
            userData[0],
            columnSettingData,
          );
          dynamicValue[item.key] = userValueByKey[0];
        } else if (item.columnSettingModule === CertificatePropertyModuleEnum.LEARNING_PATH) {
          const learningPathData = await this.buildAggregateCertificateAdaptorService.getLearningPathColumnSettingData(
            organizationId,
            learningPathId,
            learningPathVersionId,
            columnSettings,
            (collection, pipeline) => this.mainRepositoryFactory(collection, pipeline),
            (collection, pipeline) => this.repositoryFactory(collection, pipeline),
          );
          const getCourseValueByKey = this.columnSettingService.getValueByColumnSettingKey(
            item.columnSettingKey,
            learningPathData[0],
            columnSettingData,
          );
          dynamicValue[item.key] = getCourseValueByKey[0];
        } else if (item.columnSettingModule === CertificatePropertyModuleEnum.COURSE) {
          const courseData = await this.buildAggregateCertificateAdaptorService.getCourseColumnSettingData(
            organizationId,
            courseId,
            courseVersionId,
            columnSettings,
            (collection, pipeline) => this.mainRepositoryFactory(collection, pipeline),
            (collection, pipeline) => this.repositoryFactory(collection, pipeline),
          );

          const getCourseValueByKey = this.columnSettingService.getValueByColumnSettingKey(
            item.columnSettingKey,
            courseData[0],
            columnSettingData,
          );
          dynamicValue[item.key] = getCourseValueByKey[0];
        }
      } else if (item.type === CertificatePropertyTypeEnum.TEXT) {
        dynamicValue[item.key] = item.value;
      } else if (item.type === CertificatePropertyTypeEnum.IMAGE_URL) {
        const media = await this.mediaService.findOne({ id: item.mediaId });
        const organizationStorage = await this.organizationStorageService.findOne({
          organizationId: media.organizationId,
          storageType: OrganizationStorageTypeEnum.RESOURCE,
        });
        const mediaPath = this.mediaService.getMediaURL(media, organizationStorage);
        dynamicValue[item.key] = mediaPath;
      }
    }
    return dynamicValue;
  }
}

export default BulkEditLearningPathEnrollmentExpireDateUsecase;
