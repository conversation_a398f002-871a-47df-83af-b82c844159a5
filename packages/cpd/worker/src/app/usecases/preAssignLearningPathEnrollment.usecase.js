import { Exception } from '@core/exception';
import { EnrollByEnum } from '@iso/constants/enrollment';
import { UserNotificationTypeEnum } from '@iso/constants/userNotification';
import {
  buildAssignLearningPathCompleteNotificationMessage,
  buildAssignLearningPathEnrollmentCompletedNotificationMessage,
  buildLearningPathCompleteWithCertificateNotificationMessage,
  buildLearningPathEnrollmentSuccessNotificationMessage,
  buildLearningPathPreEnrollmentSuccessNotificationMessage,
} from '@iso/helpers/userNotification';
import { CertificatePropertyModuleEnum, CertificatePropertyTypeEnum } from '@iso/lms/enums/certificate.enum';
import { DBCollectionEnum } from '@iso/lms/enums/dbCollection.enum';
import * as _ from 'lodash';
import * as uuid from 'uuid';

import { DBCollection } from '@constants/dbCollection';
import { BulkStatus, DomainMetaDataEnum, GenericStatusCodes } from '@constants/generic';
import { JobTransactionStatusEnum } from '@constants/jobTransaction';
import { LearningPatEnrollTypeEnum } from '@constants/learningPath';
import { LearningPathEnrollmentStatusEnum } from '@constants/learningPathEnrollment';
import { LearningPathVersionStatusEnum } from '@constants/learningPathVersion';
import { OrganizationStorageTypeEnum } from '@constants/organizationStorage';
import { PreAssignContentEnrollByEnum, PreAssignContentTypeEnum } from '@constants/preAssignContent';
import { ColumnSettingTemplateEnum } from '@constants/templateColumnSetting';
import { buildJobTransactionModel } from '@domains/jobTransaction.domain';
import { buildLearningPathEnrollmentModel } from '@domains/learningPathEnrollment.domain';
import { buildLearningPathEnrollmentCertificateModel } from '@domains/learningPathEnrollmentCertificate.domain';
import { buildUserNotificationModel } from '@domains/userNotification.domain';
import { addDays, date, endOfDay, getDateLocale } from '@infrastructure/dateUtils';
import { AppError } from '@infrastructure/utilities/AppError';
import { getFullName } from '@infrastructure/utilities/helper';

const delay = (ms) => new Promise((res) => setTimeout(res, ms));

class PreAssignLearningPathEnrollmentUseCase {
  constructor({
    config,
    db,
    dbClient,
    notificationService,
    scheduleService,
    jobService,
    roundService,
    userService,
    preAssignContentService,
    userNotificationService,
    slackNotificationService,
    organizationService,
    learningPathService,
    learningPathVersionService,
    learningPathEnrollmentService,
    learningPathEnrollmentCertificateService,
    syncCourseLearningProgressService,
    jobTransactionService,
    templateColumnSettingService,
    userDirectReportsService,
    licenseService,
    certificateService,
    organizationCertificateService,
    courseService,
    courseVersionService,
    amqp,
    logger,
    mediaService,
    organizationStorageService,
    buildAggregateCertificateAdaptorService,
    columnSettingService,
    organizationColumnSettingService,
  }) {
    this.db = db;
    this.dbClient = dbClient;

    // Service
    this.scheduleService = scheduleService;
    this.slackNotificationService = slackNotificationService;
    this.notificationService = notificationService;
    this.jobService = jobService;
    this.queueService = amqp;
    this.roundService = roundService;
    this.userService = userService;
    this.preAssignContentService = preAssignContentService;
    this.userNotificationService = userNotificationService;
    this.organizationService = organizationService;
    this.learningPathService = learningPathService;
    this.learningPathVersionService = learningPathVersionService;
    this.learningPathEnrollmentService = learningPathEnrollmentService;
    this.learningPathEnrollmentCertificateService = learningPathEnrollmentCertificateService;
    this.syncCourseLearningProgressService = syncCourseLearningProgressService;
    this.jobTransactionService = jobTransactionService;
    this.templateColumnSettingService = templateColumnSettingService;
    this.userDirectReportsService = userDirectReportsService;
    this.licenseService = licenseService;
    this.certificateService = certificateService;
    this.organizationCertificateService = organizationCertificateService;
    this.courseService = courseService;
    this.courseVersionService = courseVersionService;
    this.mediaService = mediaService;
    this.organizationStorageService = organizationStorageService;
    this.buildAggregateCertificateAdaptorService = buildAggregateCertificateAdaptorService;
    this.columnSettingService = columnSettingService;
    this.organizationColumnSettingService = organizationColumnSettingService;

    // Config
    this.config = config;
    this.logger = logger;
  }

  async execute(msg, channel, { organization }) {
    const { domain, id: organizationId } = organization;
    const { jobId } = msg.properties.headers;
    const { payload, originalPayload, raw = [] } = JSON.parse(msg.content.toString());
    await delay(300);

    this.logger.info(`Start processing the job ID ${jobId} of type PRE_ASSIGN_LEARNING_PATH_ENROLLMENT`);

    const queueName = `${domain}:user_validate`;
    const { messageCount } = await channel.assertQueue(queueName);
    const createBy = await this.findCreateBy(payload.enrollBy, payload.preAssignContentId);
    const session = this.dbClient.startSession();

    session.startTransaction();

    try {
      await this.preAssignLearningPathEnrollmentTransaction(
        payload,
        session,
        organizationId,
        jobId,
        originalPayload,
        createBy,
        raw,
      );
      await session.commitTransaction();

      channel.ack(msg);
    } catch (error) {
      this.logger.error(`| Something wrong, the job ID ${jobId} found error, errorMessage: ${error.message}`);
      await session.abortTransaction();

      channel.nack(msg, false, false);
    } finally {
      const isQueueFinished = messageCount === 0;
      if (isQueueFinished) {
        const job = await this.jobService.findOne({
          guid: jobId,
        });
        if (job) {
          const { errorList } = job;
          await this.db.collection(DBCollection.jobs).updateOne(
            {
              guid: jobId,
            },
            {
              $set: {
                status: errorList && errorList.length ? BulkStatus.ERROR : BulkStatus.COMPLETED,
                totalError: errorList.length,
                updatedAt: date().toDate(),
              },
            },
          );

          await this.db.collection(DBCollection.pre_assign_contents).updateOne(
            {
              id: payload.preAssignContentId,
            },
            {
              $set: {
                status: errorList && errorList.length ? BulkStatus.ERROR : BulkStatus.COMPLETED,
                updatedAt: date().toDate(),
              },
            },
          );

          const { enrollBy } = payload;
          if (enrollBy === PreAssignContentEnrollByEnum.SUPERVISOR) {
            try {
              await this.sendPreAssignContentCompleteNotification(jobId);
              await this.sendAssignLearningPathEnrollmentCompleteNotification(jobId);
            } catch (error) {
              this.logger.error(`| Something wrong, notification found error, errorMessage: ${error.message}`);
            }
          }

          const message = 'Bulk pre assign learning path';
          this.notificationService.sendUserNotificationInApplication(message, organizationId);
        }
      }
      session.endSession();
      this.logger.info('End Session');
    }
  }

  async preAssignLearningPathEnrollmentTransaction(
    payload,
    session,
    organizationId,
    jobId,
    originalPayload,
    createBy,
    raw,
  ) {
    const { username, learningPathCode, roundDate, enrollBy, preAssignContentId = null } = payload;

    const errorMessageList = [];

    const organization = await this.organizationService.findOne({
      id: organizationId,
    });

    if (!organization) {
      throw new AppError(GenericStatusCodes.ORGANIZATION_NOT_FOUND, 'organization not found');
    }

    const user = await this.db.collection(DBCollection.users).findOne({
      username,
      organizationId,
    });

    if (!user) {
      errorMessageList.push('บัญชีผู้ใช้งานไม่ถูกต้อง');
    }

    let isLearningPathValid = true;
    const learningPath = await this.learningPathService.findOne({
      organizationId,
      code: learningPathCode,
      isEnabled: true,
    });

    if (!learningPath) {
      // not enabled or invalid learning path code
      isLearningPathValid = false;
    }

    const learningPathVersion = await this.learningPathVersionService.findOne(
      {
        learningPathId: learningPath?.id,
        status: LearningPathVersionStatusEnum.PUBLISHED,
      },
      { sort: { publishedAt: -1 } },
    );

    if (!learningPathVersion) {
      // Not published version
      isLearningPathValid = false;
    }

    if (!isLearningPathValid) {
      errorMessageList.push('รหัสแผนการเรียนรู้ไม่ถูกต้อง');
    }

    let learningPathEnrollmentStatus = LearningPathEnrollmentStatusEnum.PRE_ASSIGN;
    let startDate;
    let expiredDate;
    let round = null;

    if (user && learningPath && learningPathVersion) {
      // Check learning path enroll type IMMEDIATE
      if (learningPath.enrollType === LearningPatEnrollTypeEnum.IMMEDIATE) {
        if (!roundDate) {
          learningPathEnrollmentStatus = LearningPathEnrollmentStatusEnum.ASSIGNED;
          startDate = date().toDate();
          expiredDate =
            learningPathVersion.expiryDay > 0
              ? endOfDay(addDays(date().toDate(), learningPathVersion.expiryDay))
              : null;
        } else {
          errorMessageList.push('แผนการเรียนรู้ไม่มีรอบการเรียน');
        }
      } else {
        // Check learning path enroll type PRE_ENROLL
        if (roundDate && !this.isDateFormatValid(roundDate)) {
          errorMessageList.push('รูปแบบวันที่เริ่มรอบไม่ถูกต้อง');
        } else {
          const dateYearMonthDayFormat = this.convertFormatDateToYearMonthDay(roundDate);
          const roundDateToChrist = this.convertFormatDateBuddistToChrist(dateYearMonthDayFormat);
          const formatRoundDate = date(roundDateToChrist).startOf('day').toDate();
          round = await this.roundService.getRoundByLearningPathIdAndRoundDate(learningPath.id, formatRoundDate);

          if (!round) {
            errorMessageList.push('แผนการเรียนรู้ไม่มีรอบการเรียน');
          } else {
            startDate = round.roundDate;
            expiredDate =
              learningPathVersion.expiryDay > 0
                ? endOfDay(addDays(date(formatRoundDate).toDate(), learningPathVersion.expiryDay))
                : null;

            const isOverToday = expiredDate > date().toDate();

            // Check expiry date is over today
            if (!isOverToday && expiredDate) {
              errorMessageList.push('รอบการเรียนหมดอายุ');
            }
            const { firstRegistrationDate, lastRegistrationDate } = round;
            const today = date().toDate();

            const isValidFirstRegistration = date(today).isSameOrAfter(date(firstRegistrationDate));
            const isValidLastRegistration = date(today).isSameOrBefore(date(lastRegistrationDate));

            if (!isValidFirstRegistration || !isValidLastRegistration) {
              errorMessageList.push('วันที่ส่งรายชื่ออยู่นอกเหนือจากวันที่กำหนด');
            }
          }
        }
      }

      const learningPathEnrollmentData = await this.learningPathEnrollmentService.findOne({
        organizationId,
        userId: user?.guid,
        learningPathId: learningPath.id,
        version: learningPathVersion.version,
        status: { $ne: LearningPathEnrollmentStatusEnum.CANCELED },
      });

      // Check duplicate learning path enrollment
      if (learningPathEnrollmentData) {
        errorMessageList.push('ตรวจพบรายการลงแผนการเรียนรู้ซ้ำภายในไฟล์หรือผู้เรียนได้ลงทะเบียนแผนการเรียนรู้ไปแล้ว');
      }
    }

    if (errorMessageList.length > 0) {
      await this.db.collection(DBCollection.jobs).updateOne(
        {
          guid: jobId,
        },
        {
          $push: {
            rawPayloads: [...raw, errorMessageList],
            errorList: {
              originalPayload,
              message: errorMessageList,
            },
          },
          $set: {
            updatedAt: date().toDate(),
          },
        },
      );

      // Create job transaction
      const columnSettingDatas = await this.templateColumnSettingService.getColumnSettingByTemplate(
        organizationId,
        ColumnSettingTemplateEnum.assignContentManagement,
      );

      let supervisorName = '';
      let departmentName = '';
      let supervisor = null;
      if (user) {
        const departmentData = await this.db
          .collection(DBCollection.departments)
          .findOne({ userIds: { $in: [user.guid] } });
        departmentName = departmentData?.name ?? '';

        const userDirectReportData = await this.userDirectReportsService.findOne({ userId: user.guid });
        if (userDirectReportData) {
          supervisor = await this.db.collection(DBCollection.users).findOne({
            guid: userDirectReportData.directReportUserId,
            organizationId,
          });
          supervisorName = getFullName(supervisor.profile);
        }
      }

      const userTransaction = this.jobTransactionService.getUserData(columnSettingDatas, user);
      const userLicenseData = await this.licenseService.find({
        userId: user?.guid,
      });
      const licenseTransactions = this.jobTransactionService.getUserLicenseDatas(userLicenseData);

      const jobTransaction = {
        jobId,
        preEnrollmentTransactionId: null,
        learningPathEnrollmentId: null,
        status: JobTransactionStatusEnum.ERROR,
        payload: {
          user: userTransaction,
          userLicenses: licenseTransactions,
          departmentName,
          supervisorName,
          originalPayload,
        },
        errorMessages: errorMessageList,
        warningMessages: [],
      };

      const jobTrasactionModel = buildJobTransactionModel(jobTransaction);

      await this.jobTransactionService.save(jobTrasactionModel, {
        session,
      });
      this.logger.error('Erorr learning path enrollment inserted');
    } else {
      this.logger.info(`start create learning path enrollment`);

      const courses = await this.syncCourseLearningProgressService.getCourseListByContents({
        contents: learningPathVersion.contents,
        organizationId,
      });

      const courseIds = courses.map((course) => course.id);
      const courseCodes = courses.map((course) => course.code);
      const enrollments = await this.syncCourseLearningProgressService.getEnrollmentsByContents({
        userId: user?.guid,
        courseIds,
      });

      const preEnrollments = await this.syncCourseLearningProgressService.getPreEnrollmentsByContents({
        organizationId,
        userId: user?.guid,
        courseIds,
      });

      let learningPathContentProgress = [];
      let completedContentItem = 0;

      if (learningPathEnrollmentStatus === LearningPathEnrollmentStatusEnum.ASSIGNED) {
        learningPathContentProgress = this.syncCourseLearningProgressService.prepareContentProgress({
          contents: learningPathVersion.contents,
          courses,
          enrollments,
          preEnrollments,
        });

        const { status, totalCompleted } =
          this.syncCourseLearningProgressService.getStatusAndCountCompleteCourseByContentProgress(
            learningPathContentProgress,
            learningPathVersion.contents.length,
          );
        completedContentItem = totalCompleted;
        learningPathEnrollmentStatus = status;
      }

      const enrollmentGroupById = _.keyBy(enrollments, 'id');
      const enrollmentSyncProgress = learningPathContentProgress
        .map((content) => {
          if (!content?.enrollmentId) return null;
          return enrollmentGroupById[content.enrollmentId];
        })
        .filter((enrollment) => !_.isNil(enrollment));

      const currentDate = date().toDate();

      const isLearningPathEnrollmentStatusComplete =
        learningPathEnrollmentStatus === LearningPathEnrollmentStatusEnum.COMPLETED;

      const isPassCriteriaCertificate = this.learningPathEnrollmentCertificateService.validateIsPassCriteriaCertificate(
        {
          courses,
          enrollments: enrollmentSyncProgress,
          learningPathContent: learningPathVersion.contents,
        },
      );

      const isPassLearningPath =
        (learningPathVersion.isCertificateEnabled && isPassCriteriaCertificate) ||
        (!learningPathVersion.isCertificateEnabled && isLearningPathEnrollmentStatusComplete);

      const learningPathEnrollment = {
        id: uuid.v4(),
        organizationId,
        learningPathId: learningPath.id,
        userId: user?.guid,
        version: learningPathVersion.version,
        status: learningPathEnrollmentStatus,
        contentProgress: learningPathContentProgress,
        enrollBy,
        completedContentItem,
        startedAt: this.learningPathEnrollmentService.getStartDate(learningPathEnrollmentStatus, startDate),
        expiredAt: expiredDate,
        passedAt: isPassLearningPath ? currentDate : null,
        finishedAt: learningPathEnrollmentStatus === LearningPathEnrollmentStatusEnum.COMPLETED ? currentDate : null,
        roundId: round?.id,
        preAssignContentId,
      };

      const learningPathEnrollmentModel = buildLearningPathEnrollmentModel(learningPathEnrollment);
      await this.learningPathEnrollmentService.save(learningPathEnrollmentModel, {
        session,
      });

      this.logger.info('Successfully learning path enrollment inserted');

      // Create job transaction
      const columnSettingDatas = await this.templateColumnSettingService.getColumnSettingByTemplate(
        organizationId,
        ColumnSettingTemplateEnum.assignContentManagement,
      );

      let supervisorName = '';
      let departmentName = '';
      let supervisor = null;

      if (user) {
        const departmentData = await this.db
          .collection(DBCollection.departments)
          .findOne({ userIds: { $in: [user.guid] } });
        departmentName = departmentData?.name ?? '';

        const userDirectReportData = await this.userDirectReportsService.findOne({ userId: user?.guid });
        if (userDirectReportData) {
          supervisor = await this.db.collection(DBCollection.users).findOne({
            guid: userDirectReportData.directReportUserId,
            organizationId,
          });
          supervisorName = getFullName(supervisor.profile);
        }
      }

      const userTransaction = this.jobTransactionService.getUserData(columnSettingDatas, user);
      const userLicenseData = await this.licenseService.find({
        userId: user?.guid,
      });
      const licenseTransactions = this.jobTransactionService.getUserLicenseDatas(userLicenseData);

      const jobTransaction = {
        id: uuid.v4(),
        jobId,
        preEnrollmentTransactionId: null,
        learningPathEnrollmentId: learningPathEnrollmentModel.id,
        status: JobTransactionStatusEnum.PASSED,
        payload: {
          user: userTransaction,
          userLicenses: licenseTransactions,
          departmentName,
          supervisorName,
          originalPayload,
        },
        errorMessages: [],
        warningMessages: [],
      };

      const jobTrasactionModel = buildJobTransactionModel(jobTransaction);

      await this.jobTransactionService.save(jobTrasactionModel, {
        session,
      });

      let certificateRefName;
      let certificateUrl;
      let certificateCode;
      let certificatePDFUrl;

      if (learningPathVersion.isCertificateEnabled) {
        const { organizationCertificateId } = learningPathVersion.certificate;

        const certificatePayload = {
          refCode: learningPathVersion?.certificate?.refCode ?? '',
          refName: learningPathVersion?.certificate?.refName ?? '',
        };
        const newLearningPathCertificateEnrollment = buildLearningPathEnrollmentCertificateModel({
          learningPathEnrollmentId: learningPathEnrollmentModel.id,
          slugName: learningPathVersion?.certificate?.mandatoryFields?.slugName,
          payload: certificatePayload,
          logoImageUrl: organization.certificateConfig?.logoImageUrl,
          issuedBy: organization.certificateConfig?.textDynamicCertificate,
          isSentEmail: false,
        });

        if (isPassCriteriaCertificate) {
          certificateRefName = learningPathVersion?.certificate?.refName || '';

          const organizationCertificate =
            await this.organizationCertificateService.findWithCertificateDetail(organizationCertificateId);

          const { certificateDetail } = organizationCertificate;
          const certificateProperties = certificateDetail.properties;
          const organizationCertificateProperties = organizationCertificate.properties;
          const resultProperties = this.certificateService.mergeProperties(
            certificateProperties,
            organizationCertificateProperties,
          );
          const { slugName } = certificateDetail;

          // get dynamic here
          const dynamicValue = await this.getCertificateDynamicValue({
            resultProperties,
            organizationId: organization.id,
            userId: user.guid,
            courseId: null,
            courseVersionId: null,
            learningPathId: learningPath.id,
            learningPathVersionId: learningPathVersion.id,
          });

          const certificatePayload = {
            dynamic_value: dynamicValue,
            slug_name: slugName,
            metadata_url: this.certificateService.genMetaDataUrl(
              learningPathEnrollmentModel.id,
              DomainMetaDataEnum.LEARNING_PATH_ENROLLMENT,
            ),
          };

          ({ certificateCode, certificateUrl, certificatePDFUrl } =
            await this.certificateService.create(certificatePayload));

          if (certificateCode) {
            newLearningPathCertificateEnrollment.certificateUrl = certificateUrl;
            newLearningPathCertificateEnrollment.certificateCode = certificateCode;
            newLearningPathCertificateEnrollment.isSentEmail = true;
          }
        }

        await this.learningPathEnrollmentCertificateService.save(newLearningPathCertificateEnrollment, {
          session,
        });
        this.logger.info('Successfully learning path enrollment certificate inserted');
      }

      const courseNames = courses.map((course) => course.courseVersion.name);
      if (learningPathEnrollmentStatus === LearningPathEnrollmentStatusEnum.PRE_ASSIGN) {
        const emailData = {
          fullName: `คุณ${user.profile.firstname} ${user.profile.lastname ?? ''}`.trim(),
          learningPathName: learningPathVersion.name,
          courseNames,
          startDate: this.learningPathEnrollmentService.getStartDate(learningPathEnrollmentStatus, startDate),
          expiredDate,
          thumbnailUrl: learningPath.thumbnailUrl,
          learningPathCode: learningPath.code,
        };
        const mailPayload = await this.notificationService.preAssignLearningPathEmail(
          user.email,
          emailData,
          organization,
        );
        this.notificationService.sendEmail(mailPayload);

        const { userNotification } = this.buildLearningPathPreEnrollmentSuccessInAppNotificationMessageAndModel({
          userId: user.guid,
          organizationId,
          learningPath,
          learningPathVersion,
          learningPathEnrollment,
          roundDate,
          createBy,
        });

        await this.db.collection(DBCollection.user_notifications).insertOne(userNotification, {
          session,
        });
      } else {
        const emailData = {
          fullName: `คุณ${user.profile.firstname} ${user.profile.lastname ?? ''}`.trim(),
          isComplete: learningPathEnrollmentStatus === LearningPathEnrollmentStatusEnum.COMPLETED ? true : false,
          isCertificate: learningPathVersion.isCertificateEnabled && isPassCriteriaCertificate,
          learningPathName: learningPathVersion.name,
          learningPathCode: learningPath.code,
          courseNames,
          refName: certificateRefName,
          isRound: round ? true : false,
          startDate: this.learningPathEnrollmentService.getStartDate(learningPathEnrollmentStatus, startDate),
          expiredDate,
          thumbnailUrl: learningPath.thumbnailUrl,
          certificateCode,
          certificatePDFUrl,
        };
        const mailPayload = await this.notificationService.assignLearningPathEmail(user.email, emailData, organization);
        this.notificationService.sendEmail(mailPayload);

        const { userNotification } = this.buildLearningPathEnrollmentSuccessInAppNotificationMessageAndModel({
          userId: user.guid,
          organizationId,
          learningPath,
          learningPathVersion,
          learningPathEnrollment,
          roundDate,
          createBy,
          isComplete: learningPathEnrollmentStatus === LearningPathEnrollmentStatusEnum.COMPLETED ? true : false,
          isCertificate: learningPathVersion.isCertificateEnabled && isPassCriteriaCertificate,
          certificateUrl,
        });

        await this.db.collection(DBCollection.user_notifications).insertOne(userNotification, {
          session,
        });
      }
    }
  }

  buildLearningPathPreEnrollmentSuccessInAppNotificationMessageAndModel({
    userId,
    organizationId,
    learningPath,
    learningPathVersion,
    learningPathEnrollment,
    roundDate,
    createBy,
  }) {
    const message = buildLearningPathPreEnrollmentSuccessNotificationMessage({
      createBy,
      contentName: learningPathVersion.name,
      roundDate: this.convertFormatRoundDate(roundDate),
      expiryDay: learningPathVersion?.expiryDay || 0,
      expiredAt: learningPathEnrollment?.expiredAt,
    });

    const userNotification = buildUserNotificationModel(
      userId,
      organizationId,
      UserNotificationTypeEnum.LEARNING_PATH_PRE_ENROLLMENT_SUCCESS,
      {
        mediaId: learningPath.thumbnailMediaId,
        message,
        url: {
          code: learningPath.code,
          learningPathEnrollmentId: learningPathEnrollment.id,
        },
      },
    );

    return {
      message,
      userNotification,
    };
  }

  buildLearningPathEnrollmentSuccessInAppNotificationMessageAndModel({
    userId,
    organizationId,
    learningPath,
    learningPathVersion,
    learningPathEnrollment,
    roundDate,
    createBy,
    isComplete,
    isCertificate,
    certificateUrl,
  }) {
    let userNotification = null;
    let message = '';
    let type;

    if (isCertificate && isComplete) {
      type = UserNotificationTypeEnum.LEARNING_PATH_ENROLLMENT_COMPLETED_WITH_CERTIFICATE;
    } else if (isCertificate && !isComplete) {
      type = UserNotificationTypeEnum.LEARNING_PATH_ENROLLMENT_WITH_CERTIFICATE;
    } else if (!isCertificate && isComplete) {
      type = UserNotificationTypeEnum.LEARNING_PATH_ENROLLMENT_COMPLETED;
    } else if (roundDate) {
      type = UserNotificationTypeEnum.LEARNING_PATH_ENROLLMENT_WITH_ROUND_SUCCESS;
    } else {
      type = UserNotificationTypeEnum.LEARNING_PATH_ENROLLMENT_SUCCESS;
    }

    if (isCertificate || isComplete) {
      message = buildLearningPathCompleteWithCertificateNotificationMessage({
        contentName: learningPathVersion.name,
        isComplete,
        isCertificate,
      });

      userNotification = buildUserNotificationModel(userId, organizationId, type, {
        mediaId: learningPath.thumbnailMediaId,
        message,
        url: {
          code: learningPath.code,
          learningPathEnrollmentId: learningPathEnrollment.id,
          certificateUrl,
        },
      });
    } else {
      message = buildLearningPathEnrollmentSuccessNotificationMessage({
        createBy,
        contentName: learningPathVersion.name,
        roundDate: this.convertFormatRoundDate(roundDate),
        expiryDay: learningPathVersion?.expiryDay || 0,
        expiredAt: learningPathEnrollment?.expiredAt,
      });

      userNotification = buildUserNotificationModel(userId, organizationId, type, {
        mediaId: learningPath.thumbnailMediaId,
        message,
        url: {
          code: learningPath.code,
          learningPathEnrollmentId: learningPathEnrollment.id,
        },
      });
    }

    return {
      message,
      userNotification,
    };
  }

  async findCreateBy(enrollBy, preAssignContentId) {
    let createBy = '';
    if (enrollBy === EnrollByEnum.SUPERVISOR) {
      const preAssignContent = await this.db.collection(DBCollection.pre_assign_contents).findOne(
        {
          id: preAssignContentId,
        },
        { projection: { id: 1, createdByUserId: 1 } },
      );

      if (preAssignContent) {
        const createByUser = await this.db.collection(DBCollection.users).findOne(
          {
            guid: preAssignContent?.createdByUserId,
          },
          { projection: { guid: 1, profile: 1 } },
        );

        if (createByUser) {
          createBy = `คุณ${createByUser.profile.firstname} ${createByUser.profile.lastname}`;
        }
      }
    } else if (enrollBy === EnrollByEnum.ADMIN) {
      createBy = 'ผู้ดูแลระบบ';
    }

    return createBy;
  }

  convertFormatRoundDate(roundDate) {
    if (!roundDate) return null;
    const dateYearMonthDayFormat = this.convertFormatDateToYearMonthDay(roundDate);
    const roundDateToChrist = this.convertFormatDateBuddistToChrist(dateYearMonthDayFormat);
    return date(roundDateToChrist).startOf('day').toDate();
  }

  async sendPreAssignContentCompleteNotification(jobId) {
    const job = await this.db.collection(DBCollection.jobs).findOne({ guid: jobId });
    if (!job) {
      this.logger.error(`Can't sending email pre-assign content: Job id ${jobId} not found.`);
      throw Exception.new(`Can't sending email pre-assign content: Job id ${jobId} not found.`, { jobId });
    }

    const preAssignContentJob = await this.db
      .collection(DBCollection.pre_assign_content_jobs)
      .findOne({ jobId: job.guid });

    const preAssignContent = await this.db
      .collection(DBCollection.pre_assign_contents)
      .findOne({ id: preAssignContentJob.preAssignContentId });

    if (!preAssignContent) {
      this.logger.error(`Can't sending email pre-assign content not found.`);
      throw Exception.new(`Can't sending email pre-assign content not found.`);
    }

    const { organizationId, userId } = job;
    const [firstPreAssignContentItem] = preAssignContent.items;
    const { learningPathId, roundId } = firstPreAssignContentItem;

    const organization = await this.db.collection(DBCollection.organizations).findOne({ id: organizationId });
    if (!organization) {
      this.logger.error(`Can't sending email pre-assign content: Organization id ${organizationId} not found.`);
      throw Exception.new(`Can't sending email pre-assign content: Organization id ${organizationId} not found.`);
    }

    const user = await this.db.collection(DBCollection.users).findOne({ guid: userId });
    if (!user) {
      this.logger.error(`Can't sending email pre-assign content: User id ${userId} not found.`);
      throw Exception.new(`Can't sending email pre-assign content: User id ${userId} not found.`);
    }

    const { contentType } = preAssignContent;
    let contentName = '';
    let contentRound = null;
    let contentExpiryDay = null;

    const pipeline = [
      {
        $match: {
          id: learningPathId,
          isEnabled: true,
        },
      },
      {
        $lookup: {
          from: 'learning-path-versions',
          let: { id: '$id' },
          pipeline: [
            {
              $match: {
                $expr: {
                  $and: [
                    {
                      $eq: ['$learningPathId', '$$id'],
                    },
                    {
                      $eq: ['$status', LearningPathVersionStatusEnum.PUBLISHED],
                    },
                  ],
                },
              },
            },
          ],
          as: 'learningPathVersion',
        },
      },
      {
        $unwind: '$learningPathVersion',
      },
    ];

    const [learningPath] = await this.db.collection(DBCollection.learning_paths).aggregate(pipeline).toArray();
    if (!learningPath) {
      this.logger.error(`Can't sending email pre-assign content: Learning path id '${learningPathId}' not found.`);
      throw Exception.new(`Can't sending email pre-assign content: Learning path id '${learningPathId}' not found.`, {
        learningPathId,
      });
    }

    contentExpiryDay = learningPath.learningPathVersion.expiryDay;
    contentName = learningPath.learningPathVersion.name;
    contentRound = null;

    const round = await this.db.collection(DBCollection.rounds).findOne({ id: roundId });
    if (round?.roundDate) {
      contentRound = {
        roundDate: round.roundDate,
        expiredDate: contentExpiryDay
          ? date(round.roundDate).add(contentExpiryDay, 'days').endOf('day').toDate()
          : null,
      };
    }

    const payload = {
      contentName,
      contentType,
      fullName: `${user.profile?.firstname ?? ''} ${user.profile?.lastname ?? ''}`.trim(),
      userTotal: job.total,
      userTotalError: job.totalError,
      round: contentRound,
      redirectUrl: 'myTeam?tab=pre-assign-content',
    };

    const mailPayload = this.notificationService.preAssignContentCompleteEmail(user.email, payload, organization);
    this.notificationService.sendEmail(mailPayload);

    const { userNotification, message } = this.buildAssignLearningPathCompleteInAppNotificationMessageAndModel({
      userId,
      organizationId,
      preAssignContentId: preAssignContent.id,
      learningPath,
      round,
      userTotal: job.total,
      userTotalError: job.totalError,
    });

    await this.db.collection(DBCollection.user_notifications).insertOne(userNotification);
    this.notificationService.sendUserNotificationInApplication(message, organizationId);
  }

  async sendAssignLearningPathEnrollmentCompleteNotification(jobId) {
    const assignLearningPathEnrollmentCompleteNotifications = [];
    const userNotificationBulkWriteOperations = [];

    const job = await this.db.collection(DBCollection.jobs).findOne({ guid: jobId });
    if (!job) {
      this.logger.error(`Can't sending email pre-assign content: Job id ${jobId} not found.`);
      throw Exception.new(`Can't sending email pre-assign content: Job id ${jobId} not found.`, { jobId });
    }

    const { organizationId, userId } = job;

    const organization = await this.organizationService.findOne({
      id: organizationId,
    });

    if (!organization) {
      this.logger.error(`Can't sending email organization not found.`);
      throw Exception.new(`Can't sending email organization not found.`);
    }

    const preAssignContentJob = await this.db
      .collection(DBCollection.pre_assign_content_jobs)
      .findOne({ jobId: job.guid });

    const preAssignContent = await this.db
      .collection(DBCollection.pre_assign_contents)
      .findOne({ id: preAssignContentJob.preAssignContentId });

    if (!preAssignContent) {
      this.logger.error(`Can't sending email pre-assign content not found.`);
      throw Exception.new(`Can't sending email pre-assign content not found.`);
    }

    const assignee = await this.userService.findOne({ guid: userId, organizationId });
    if (!assignee) {
      this.logger.error(`Can't sending email pre-assign content: Supervisor user id ${userId} not found.`);
      return;
    }

    const { items } = preAssignContent;

    const learnerUserIds = _.chain(items).map('userId').value();
    const roundIds = _.chain(items).map('roundId').uniq().value();
    const learningPathIds = _.chain(items).map('learningPathId').uniq().value();

    const [learners, rounds, learningPaths, learningPathEnrollments] = await Promise.all([
      this.userService.find({ guid: { $in: learnerUserIds }, organizationId }),
      this.roundService.find({ id: { $in: roundIds }, organizationId }),
      this.learningPathService.findEnablePublishLearningPathByIds(learningPathIds),
      this.learningPathEnrollmentService.find({ preAssignContentId: preAssignContent.id }),
    ]);

    const learnerMapById = learners.reduce((acc, cur) => {
      const key = String(cur.guid);
      acc.set(key, cur);
      return acc;
    }, new Map());

    const roundMapById = rounds.reduce((acc, cur) => {
      const key = String(cur.id);
      acc.set(key, cur);
      return acc;
    }, new Map());

    const learningPathMapById = learningPaths.reduce((acc, cur) => {
      const key = String(cur.id);
      acc.set(key, cur);
      return acc;
    }, new Map());

    const learningPathEnrollmentByUserId = learningPathEnrollments.reduce((acc, cur) => {
      const key = String(cur.userId);
      acc.set(key, cur);
      return acc;
    }, new Map());

    for (const item of items) {
      const { learningPathId, roundId, userId } = item;

      const learner = learnerMapById.get(String(userId));
      if (!learner) {
        continue;
      }

      const learningPathData = learningPathMapById.get(String(learningPathId));
      if (!learningPathData) {
        continue;
      }

      const { learningPathVersion, ...learningPath } = learningPathData;

      if (!learningPathVersion) {
        continue;
      }

      const learningPathEnrollment = learningPathEnrollmentByUserId.get(String(userId));
      if (!learningPathEnrollment) {
        continue;
      }

      if (learningPathEnrollment.status !== LearningPathEnrollmentStatusEnum.COMPLETED) {
        continue;
      }

      const round = roundMapById.get(String(roundId));

      const assigneeFullName = `${assignee.profile.firstname ?? ''} ${assignee.profile.lastname ?? ''}`.trim();
      const learnerFullname = `${learner.profile.firstname ?? ''} ${learner.profile.lastname ?? ''}`.trim();
      const assignEnrollmentCompleteMailPayloadParams = {
        learnerUserId: learner.guid,
        contentName: learningPathVersion?.name || '',
        contentType: PreAssignContentTypeEnum.LEARNING_PATH,
        learnerFullName: learnerFullname,
        fullName: assigneeFullName,
        round: {
          roundDate: round?.roundDate,
          expiredDate: learningPathEnrollment.expiredAt,
        },
      };

      const { message, userNotification } =
        await this.buildAssignLearningPathEnrollmentCompleteMessageAndInAppUserNotificationModel({
          learningPath,
          learningPathVersion,
          learningPathEnrollment,
          round,
          assignee,
          fullName: learnerFullname,
        });

      assignLearningPathEnrollmentCompleteNotifications.push({
        email: assignee.email,
        mailPayload: assignEnrollmentCompleteMailPayloadParams,
        message,
        organization,
      });

      userNotificationBulkWriteOperations.push({
        insertOne: {
          document: {
            ...userNotification,
          },
        },
      });
    }

    if (userNotificationBulkWriteOperations.length > 0) {
      await this.db.collection(DBCollection.user_notifications).bulkWrite(userNotificationBulkWriteOperations);
    }

    for (const item of assignLearningPathEnrollmentCompleteNotifications) {
      const { email, mailPayload, message } = item;
      const assignEnrollmentCompletePayload = await this.notificationService.assignEnrollmentCompleteEmail(
        email,
        mailPayload,
        organization,
      );

      this.notificationService.sendEmail(assignEnrollmentCompletePayload);
      this.notificationService.sendUserNotificationInApplication(message || '', organizationId);
    }
  }

  buildAssignLearningPathCompleteInAppNotificationMessageAndModel({
    userId,
    organizationId,
    preAssignContentId,
    learningPath,
    round,
    userTotal,
    userTotalError,
  }) {
    const message = buildAssignLearningPathCompleteNotificationMessage({
      contentName: learningPath.learningPathVersion?.name,
      roundDate: round?.roundDate,
      expiryDay: learningPath.learningPathVersion?.expiryDay,
      userTotal,
      userTotalError,
    });
    const userNotification = buildUserNotificationModel(
      userId,
      organizationId,
      UserNotificationTypeEnum.PRE_ASSIGN_LEARNING_PATH_COMPLETED,
      {
        mediaId: learningPath.thumbnailMediaId,
        message,
        url: {
          code: learningPath.code,
          preAssignContentId: preAssignContentId || null,
        },
      },
    );

    return {
      userNotification,
      message,
    };
  }

  async buildAssignLearningPathEnrollmentCompleteMessageAndInAppUserNotificationModel({
    learningPath,
    learningPathVersion,
    learningPathEnrollment,
    round,
    assignee,
    fullName,
  }) {
    const message = buildAssignLearningPathEnrollmentCompletedNotificationMessage({
      fullName: fullName || '',
      contentName: learningPathVersion.name,
      roundDate: round?.roundDate,
      expiredAt: learningPathEnrollment?.expiredAt,
    });

    const userNotification = buildUserNotificationModel(
      assignee.guid, //supervisor
      learningPathEnrollment.organizationId,
      UserNotificationTypeEnum.ASSIGN_LEARNING_PATH_ENROLLMENT_COMPLETED,
      {
        mediaId: learningPath.thumbnailMediaId,
        message,
        url: {
          code: learningPath.code,
          userId: learningPathEnrollment.userId,
          learningPathEnrollmentId: learningPathEnrollment.id,
        },
      },
    );

    return { message, userNotification };
  }

  convertFormatDateToYearMonthDay(dateTimeStr) {
    const [dateStr, timeStr] = dateTimeStr.split(' ');

    let splitSyntax = '-';
    if (dateStr.includes('/')) {
      splitSyntax = '/';
    }
    const [day, month, year] = dateStr.split(splitSyntax);
    const newDate = [year, month, day].join(splitSyntax);

    return timeStr ? `${newDate} ${timeStr}` : newDate;
  }

  convertFormatDateBuddistToChrist(str) {
    const [dateStr, timeStr] = str.split(' ');

    let splitSyntax = '-';
    if (dateStr.includes('/')) {
      splitSyntax = '/';
    }
    const [year, month, day] = dateStr.split(splitSyntax);
    const newDate = [parseInt(year) - 543, month, day].join(splitSyntax);
    return timeStr ? `${newDate} ${timeStr}` : newDate;
  }

  isDateFormatValid(str) {
    return /^([0-9]{2})\/([0-9]{2})\/([0-9]{4})$/.test(str);
  }
  async mainRepositoryFactory(collection, pipeline) {
    switch (collection) {
      case DBCollectionEnum.USERS: {
        return this.userService.aggregate(pipeline);
      }
      case DBCollectionEnum.COURSES: {
        return this.courseService.aggregate(pipeline);
      }
      case DBCollectionEnum.LEARNING_PATHS: {
        return this.learningPathService.aggregate(pipeline);
      }
      default: {
        return [];
      }
    }
  }

  async repositoryFactory(collection, pipeline) {
    switch (collection) {
      case DBCollectionEnum.USERS: {
        return this.userService.aggregate(pipeline);
      }
      case DBCollectionEnum.DEPARTMENTS: {
        return this.departmentService.aggregate(pipeline);
      }
      case DBCollectionEnum.USER_DIRECT_REPORTS: {
        return this.userDirectReportsService.aggregate(pipeline);
      }
      case DBCollectionEnum.LICENSES: {
        return this.licenseService.aggregate(pipeline);
      }
      case DBCollectionEnum.LEARNING_PATH_VERSIONS: {
        return this.learningPathVersionService.aggregate(pipeline);
      }
      case DBCollectionEnum.COURSE_VERSIONS: {
        return this.courseVersionService.aggregate(pipeline);
      }
      default: {
        return [];
      }
    }
  }

  async getCertificateDynamicValue(params) {
    const {
      resultProperties,
      organizationId,
      userId,
      courseId,
      courseVersionId,
      learningPathId,
      learningPathVersionId,
    } = params;

    const currentDate = date().toDate();
    const transformedCertificatePropertiesData = resultProperties.map((item) => {
      const key = ['t', 'i'].some((prefix) => item.key.startsWith(prefix)) ? item.key.slice(1) : item.key;
      const columnSettingParts = item.columnSettingKey ? item.columnSettingKey.split('.') : [null, null];

      return {
        key,
        columnSettingModule: columnSettingParts[0],
        columnSettingKey: item.columnSettingKey,
        type: item.type,
        value: item.value,
        mediaId: item.mediaId ? item.mediaId : null,
        name: item.name,
      };
    });

    const dynamicValue = {};
    for (const item of transformedCertificatePropertiesData) {
      if (item.type === CertificatePropertyTypeEnum.CURRENT_DATE) {
        dynamicValue[item.key] = getDateLocale(item.value, currentDate);
      } else if (item.type === CertificatePropertyTypeEnum.COLUMN_SETTING) {
        const columnSettings = await this.columnSettingService.findColumnSettingWithTemplate(item.columnSettingKey);

        const organizationColumnSettings = await this.organizationColumnSettingService.findColumnSettingWithTemplate(
          organizationId,
          item.columnSettingKey,
        );

        const columnSettingData = columnSettings.length > 0 ? columnSettings : organizationColumnSettings;

        if (item.columnSettingModule === CertificatePropertyModuleEnum.USER) {
          const userData = await this.buildAggregateCertificateAdaptorService.getUserColumnSettingData(
            organizationId,
            userId,
            columnSettings,
            (collection, pipeline) => this.mainRepositoryFactory(collection, pipeline),
            (collection, pipeline) => this.repositoryFactory(collection, pipeline),
          );
          const userValueByKey = this.columnSettingService.getValueByColumnSettingKey(
            item.columnSettingKey,
            userData[0],
            columnSettingData,
          );
          dynamicValue[item.key] = userValueByKey[0];
        } else if (item.columnSettingModule === CertificatePropertyModuleEnum.LEARNING_PATH) {
          const learningPathData = await this.buildAggregateCertificateAdaptorService.getLearningPathColumnSettingData(
            organizationId,
            learningPathId,
            learningPathVersionId,
            columnSettings,
            (collection, pipeline) => this.mainRepositoryFactory(collection, pipeline),
            (collection, pipeline) => this.repositoryFactory(collection, pipeline),
          );
          const getCourseValueByKey = this.columnSettingService.getValueByColumnSettingKey(
            item.columnSettingKey,
            learningPathData[0],
            columnSettingData,
          );
          dynamicValue[item.key] = getCourseValueByKey[0];
        } else if (item.columnSettingModule === CertificatePropertyModuleEnum.COURSE) {
          const courseData = await this.buildAggregateCertificateAdaptorService.getCourseColumnSettingData(
            organizationId,
            courseId,
            courseVersionId,
            columnSettings,
            (collection, pipeline) => this.mainRepositoryFactory(collection, pipeline),
            (collection, pipeline) => this.repositoryFactory(collection, pipeline),
          );

          const getCourseValueByKey = this.columnSettingService.getValueByColumnSettingKey(
            item.columnSettingKey,
            courseData[0],
            columnSettingData,
          );
          dynamicValue[item.key] = getCourseValueByKey[0];
        }
      } else if (item.type === CertificatePropertyTypeEnum.TEXT) {
        dynamicValue[item.key] = item.value;
      } else if (item.type === CertificatePropertyTypeEnum.IMAGE_URL) {
        const media = await this.mediaService.findOne({ id: item.mediaId });
        const organizationStorage = await this.organizationStorageService.findOne({
          organizationId: media.organizationId,
          storageType: OrganizationStorageTypeEnum.RESOURCE,
        });
        const mediaPath = this.mediaService.getMediaURL(media, organizationStorage);
        dynamicValue[item.key] = mediaPath;
      }
    }
    return dynamicValue;
  }
}

export default PreAssignLearningPathEnrollmentUseCase;
