import { Exception } from '@core/exception';
import { UserNotificationTypeEnum } from '@iso/constants/userNotification';
import {
  buildApprovedRegularNotificationMessage,
  buildApprovedWithCertificateNotificationMessage,
  buildAssignEnrollmentCompletedNotificationMessage,
  buildAssignLearningPathEnrollmentCompletedNotificationMessage,
  buildCancelCourseApprovalNotificationMessage,
  buildCancelCoursePassResultNotificationMessage,
  buildCancelLearningPathCertificateNotificationMessage,
  buildEnrollmentRejectOrExpireNotificationMessage,
  buildLearningPathCompleteWithCertificateNotificationMessage,
} from '@iso/helpers/userNotification';
import { CertificatePropertyModuleEnum, CertificatePropertyTypeEnum } from '@iso/lms/enums/certificate.enum';
import { CourseObjectiveTypeEnum, RegulatorEnum } from '@iso/lms/enums/course.enum';
import { CourseItemStatusCodeEnum } from '@iso/lms/enums/courseItemProgress.enum';
import {
  CourseItemProgressHistoryActionByEnum,
  CourseItemProgressHistoryEventTypeEnum,
} from '@iso/lms/enums/courseItemProgressHistory.enum';
import { CourseVersionStatusEnum } from '@iso/lms/enums/courseVersion.enum';
import { DBCollectionEnum } from '@iso/lms/enums/dbCollection.enum';
import { EnrollmentStatusEnum } from '@iso/lms/enums/enrollment.enum';
import { BulkOpTypeEnum } from '@iso/lms/enums/job.enum';
import { MaterialMediaTypeEnum } from '@iso/lms/enums/materialMedia.enum';
import { OrganizationStorageTypeEnum } from '@iso/lms/enums/organizationStorage.enum';
import { QuizTestTypeEnum } from '@iso/lms/enums/quiz.enum';
import { CourseItemProgressHistory } from '@iso/lms/models/courseItemProgressHistory.model';
import {
  isPassedAllLearningCriteria,
  isPassedCriteriaLearningProgress,
  isPassedCriteriaPostTestQuiz,
  isPassedCriteriaTimeSpent,
} from '@iso/lms/services/learningProgress.service';
import { chain, cloneDeep, compact, groupBy, isEmpty, isNil, isNull, map, set } from 'lodash';

import {
  calculateEnrollmentLearningProgress,
  checkIsAllClassroomMarkResult,
  checkIsAllowUpdateLearningPathEnrollmentExpired,
  checkIsCancelCertificateLearningPathEditClassroomMarkResult,
  checkIsEditMarkResult,
  checkIsLatestEnrollment,
  getEnrollmentStatusEditClassroomMarkResult,
  getLearningPathContentProgressStatus,
  getLearningPathEnrollmentStatusEditClassroomMarkResult,
  getNotifyCourseEditClassroomMarkResult,
  validateRequiredFields,
} from '@app/domains/classroomLocationEnrollment.domain';
import { buildJobTransactionModel } from '@app/domains/jobTransaction.domain';
import { ClassroomLocationEnrollmentStatusEnum, ClassroomRoundStatusEnum } from '@constants/classroom';
import { CourseItemTypeEnum } from '@constants/course';
import { DBCollection } from '@constants/dbCollection';
import { BulkStatus, DomainMetaDataEnum } from '@constants/generic';
import { JobTransactionStatusEnum } from '@constants/jobTransaction';
import {
  LearningPathContentProgressStatusEnum,
  LearningPathEnrollmentStatusEnum,
} from '@constants/learningPathEnrollment';
import { PreAssignContentTypeEnum } from '@constants/preAssignContent';
import { isFinishedLearningProgress } from '@domains/learningProgress.domain';
import { buildUserNotificationModel } from '@domains/userNotification.domain';
import { date, getDateLocale } from '@infrastructure/dateUtils';
import { getFullName } from '@infrastructure/utilities/helper';

const delay = (ms) => new Promise((res) => setTimeout(res, ms));

class ClassroomMarkResultUseCase {
  constructor({
    config,
    db,
    dbClient,
    notificationService,
    jobService,
    organizationService,
    learningPathService,
    learningPathVersionService,
    learningPathEnrollmentService,
    learningPathEnrollmentCertificateService,
    preAssignContentService,
    roundService,
    syncCourseLearningProgressService,
    userService,
    partService,
    certificateService,
    jobTransactionService,
    courseItemProgressHistoryService,
    amqp,
    logger,
    enrollmentCertificateService,
    organizationCertificateService,
    organizationStorageService,
    mediaService,
    columnSettingService,
    organizationColumnSettingService,
    buildAggregateCertificateAdaptorService,
    courseService,
    courseVersionService,
  }) {
    // Config
    this.config = config;
    this.logger = logger;
    this.db = db;
    this.dbClient = dbClient;

    // Service
    this.notificationService = notificationService;
    this.jobService = jobService;
    this.queueService = amqp;
    this.organizationService = organizationService;
    this.learningPathService = learningPathService;
    this.learningPathVersionService = learningPathVersionService;
    this.learningPathEnrollmentService = learningPathEnrollmentService;
    this.learningPathEnrollmentCertificateService = learningPathEnrollmentCertificateService;
    this.preAssignContentService = preAssignContentService;
    this.roundService = roundService;
    this.syncCourseLearningProgressService = syncCourseLearningProgressService;
    this.userService = userService;
    this.partService = partService;
    this.certificateService = certificateService;
    this.jobTransactionService = jobTransactionService;
    this.courseItemProgressHistoryService = courseItemProgressHistoryService;
    this.enrollmentCertificateService = enrollmentCertificateService;
    this.organizationCertificateService = organizationCertificateService;
    this.organizationStorageService = organizationStorageService;
    this.mediaService = mediaService;
    this.columnSettingService = columnSettingService;
    this.organizationColumnSettingService = organizationColumnSettingService;
    this.buildAggregateCertificateAdaptorService = buildAggregateCertificateAdaptorService;
    this.courseService = courseService;
    this.courseVersionService = courseVersionService;
  }

  async execute(msg, channel, { organization }) {
    const currentDate = date().toDate();
    const { domain, id: organizationId } = organization;
    const { jobId } = msg.properties.headers;
    const data = JSON.parse(msg.content.toString());
    const { payload, originalPayload, raw } = data;
    await delay(300);

    this.logger.info(`Start processing the job ID ${jobId} of type CLASSROOM_MARK_RESULT`);

    const { messageCount } = await channel.assertQueue(domain);

    const session = this.dbClient.startSession();
    session.startTransaction();

    try {
      await this.classroomMarkResultTransaction(payload, session, organizationId, jobId, domain);
      await session.commitTransaction();

      channel.ack(msg);
    } catch (error) {
      this.logger.error(`| Something wrong, the job ID ${jobId} found error, errorMessage: ${error.message}`);
      await session.abortTransaction();

      await this.db.collection(DBCollection.jobs).updateOne(
        {
          guid: jobId,
        },
        {
          $push: {
            rawPayloads: [...raw, error.message],
            errorList: {
              originalPayload,
              message: error.message,
            },
          },
          $set: {
            updatedAt: currentDate,
          },
        },
      );

      const jobTransaction = {
        jobId,
        enrollmentId: error.data?.enrollmentId,
        classroomLocationEnrollmentId: error.data?.classroomLocationEnrollmentId,
        status: JobTransactionStatusEnum.ERROR,
        payload,
        errorMessages: error.data?.errorMessages ?? error.message,
      };

      const jobTransactionModel = buildJobTransactionModel(jobTransaction);
      await this.jobTransactionService.save(jobTransactionModel, { session });

      channel.nack(msg, false, false);
    } finally {
      if (messageCount === 0) {
        const job = await this.jobService.findOne({
          guid: jobId,
        });
        const { errorList } = job;
        await this.db.collection(DBCollection.jobs).updateOne(
          {
            guid: jobId,
          },
          {
            $set: {
              status: errorList && errorList.length ? BulkStatus.ERROR : BulkStatus.COMPLETED,
              updatedAt: currentDate,
            },
          },
        );
      }
      session.endSession();
      this.logger.info('End Session');
    }
  }

  async classroomMarkResultTransaction(payload, session, organizationId, jobId, domain) {
    const currentDate = date().toDate();
    const { classroomRoundId, classroomLocationId, username, scoreAttend, scoreHomework, remark } = payload;

    const userNotificationBulkWriteOperations = [];
    const enrollmentCertificateBulkWriteOperations = [];
    const learningPathEnrollmentCertificateBulkWriteOperations = [];
    const syncCourseProgressBulkWriteOperations = [];

    const assignEnrollmentCompleteNotifications = [];
    const assignLearningPathEnrollmentCompleteNotifications = [];
    const enrollmentCompleteWithCertificateNotifications = [];
    const completeLearningPathNotifications = [];
    const completeLearningPathAndCertificateNotifications = [];
    const completeLearningPathCertificateNotifications = [];
    const cancelRegularCourseCertificateNotifications = [];
    const cancelTrainingCourseCertificateNotifications = [];
    const cancelLearningPathCertificateNotifications = [];
    const rejectEnrollmentNotifications = [];

    let roundOfEnrollment = null;

    const organization = await this.organizationService.findOne({
      id: organizationId,
    });

    if (!organization) {
      throw Exception.new('organization not found');
    }

    const user = await this.userService.findOne({
      username,
      organizationId,
    });

    const userId = user ? user.guid : '';

    this.logger.info(`User found with the ID of ${userId}`);

    const classroomRound = await this.db.collection(DBCollection.classroom_rounds).findOne({
      id: classroomRoundId,
      organizationId: organization.id,
    });

    const classroomRoundMaterialMediaId = classroomRound.materialMediaId;

    const classroomLocation = await this.db.collection(DBCollection.classroom_locations).findOne({
      id: classroomLocationId,
      deletedAt: null,
    });

    const classroomLocationEnrollment = await this.db.collection(DBCollection.classroom_location_enrollments).findOne({
      classroomRoundId: classroomRound.id,
      classroomLocationId: classroomLocation.id,
      userId,
      status: { $ne: ClassroomLocationEnrollmentStatusEnum.CANCELED },
      deletedAt: null,
    });

    if (!user || !classroomLocationEnrollment) {
      throw Exception.new('ไม่พบชื่อผู้ใช้งานบนระบบ หรือไม่ได้ลงเรียนในรอบและสถานที่นี้');
    }

    const courseItem = await this.db
      .collection(DBCollection.course_items)
      .find({
        materialMediaId: classroomRoundMaterialMediaId,
      })
      .toArray();
    if (courseItem.length === 0) {
      throw Exception.new('course item not found', {
        enrollmentId: classroomLocationEnrollment.enrollmentId,
        classroomLocationEnrollmentId: classroomLocationEnrollment.id,
      });
    }

    const courseItemIds = courseItem.map((val) => val.id);

    const courseItemCriteriaConfigs = await this.db
      .collection(DBCollection.course_item_criteria_configs)
      .find({
        courseItemType: CourseItemTypeEnum.Classroom,
        isEnabled: true,
        courseItemId: { $in: courseItemIds },
      })
      .toArray();

    const enrollment = await this.db.collection(DBCollection.enrollments).findOne({
      id: classroomLocationEnrollment?.enrollmentId,
    });

    if (!enrollment) {
      throw Exception.new('enrollment not found', {
        enrollmentId: classroomLocationEnrollment.enrollmentId,
        classroomLocationEnrollmentId: classroomLocationEnrollment.id,
      });
    }

    if (enrollment.roundId) {
      roundOfEnrollment = await this.roundService.findOne({
        id: enrollment.roundId,
      });
    }

    this.logger.info(`Enrollment found with the ID of ${enrollment?.id}`);

    const enrollmentCourseId = enrollment.courseId;
    const enrollmentCourseVersionId = enrollment.courseVersionId;

    const courseItemCriteriaConfig = courseItemCriteriaConfigs.find(
      (val) => val.courseVersionId === enrollmentCourseVersionId,
    );

    const passAttendanceCriteria = courseItemCriteriaConfig?.config?.passAttendance ?? 0;
    const maxAttendanceCriteria = courseItemCriteriaConfig?.config?.maxAttendance ?? 0;
    const passScoreHomeworkCriteria = courseItemCriteriaConfig?.config?.passScoreHomework ?? 0;
    const maxScoreHomeworkCriteria = courseItemCriteriaConfig?.config?.maxScoreHomework ?? 0;

    const passScoreHomework = scoreHomework ? Number(scoreHomework) : null;
    const passAttendance = scoreAttend ? Number(scoreAttend) : null;

    const isEditMarkResult = checkIsEditMarkResult(
      classroomLocationEnrollment.status,
      {
        totalAttended: classroomLocationEnrollment.totalAttended,
        scoreHomework: classroomLocationEnrollment.scoreHomework,
      },
      {
        totalAttended: passAttendance,
        scoreHomework: passScoreHomework,
      },
    );

    const errorMessageList = validateRequiredFields(
      isEditMarkResult,
      passAttendanceCriteria,
      maxAttendanceCriteria,
      passScoreHomeworkCriteria,
      maxScoreHomeworkCriteria,
      { scoreAttend, scoreHomework, remark },
    );

    if (errorMessageList.length > 0) {
      throw Exception.new(errorMessageList, {
        enrollmentId: classroomLocationEnrollment.enrollmentId,
        classroomLocationEnrollmentId: classroomLocationEnrollment.id,
        errorMessages: errorMessageList,
      });
    }

    const isUpdateRemarkOnly =
      classroomLocationEnrollment.totalAttended === passAttendance &&
      classroomLocationEnrollment.scoreHomework === passScoreHomework &&
      classroomLocationEnrollment.remark !== remark;

    if (isUpdateRemarkOnly) {
      await this.db.collection(DBCollection.classroom_location_enrollments).updateOne(
        {
          id: classroomLocationEnrollment.id,
        },
        {
          $set: {
            remark,
            updatedAt: currentDate,
          },
        },
        {
          session,
        },
      );

      this.logger.info(`Successfully update classroom location enrollment ${classroomLocationEnrollment.id}`);

      const jobTransaction = {
        jobId,
        enrollmentId: classroomLocationEnrollment.enrollmentId,
        classroomLocationEnrollmentId: classroomLocationEnrollment.id,
        status: JobTransactionStatusEnum.PASSED,
        payload,
      };

      const jobTransactionModel = buildJobTransactionModel(jobTransaction);
      await this.jobTransactionService.save(jobTransactionModel, { session });
      return;
    }

    const course = await this.db.collection(DBCollection.courses).findOne({
      id: enrollmentCourseId,
    });

    this.logger.info(`Course found with the ID of ${course?.id}`);

    const courseVersion = await this.db.collection(DBCollection.course_versions).findOne({
      id: enrollmentCourseVersionId,
    });

    this.logger.info(`Course Version found with the ID of ${courseVersion?.id}`);

    const courseName = courseVersion.name;

    const partList = await this.partService.getPartByCourseId(enrollmentCourseVersionId);

    const partCourseItemList = partList.flatMap((part) => part.courseItems);
    const classroomCourseItem = partCourseItemList.find(
      (item) => item.materialMediaId === classroomRoundMaterialMediaId,
    );

    const courseItemId = classroomCourseItem?.id ?? '';
    const courseItemName = classroomCourseItem?.name ?? '';

    let newClassroomLocationEnrollmentStatus = classroomLocationEnrollment.status;
    if (passAttendance >= passAttendanceCriteria && passScoreHomework >= passScoreHomeworkCriteria) {
      newClassroomLocationEnrollmentStatus = ClassroomLocationEnrollmentStatusEnum.PASSED;
    } else {
      newClassroomLocationEnrollmentStatus = ClassroomLocationEnrollmentStatusEnum.NOT_PASS;
    }

    // Update classroom location enrollment
    await this.db.collection(DBCollection.classroom_location_enrollments).updateOne(
      {
        id: classroomLocationEnrollment.id,
      },
      {
        $set: {
          status: newClassroomLocationEnrollmentStatus,
          totalAttended: passAttendance,
          scoreHomework: passScoreHomework,
          remark,
          updatedAt: currentDate,
        },
      },
      {
        session,
      },
    );

    this.logger.info(`Successfully update classroom location enrollment ${classroomLocationEnrollment.id}`);

    // Insert Logger course item progress history
    if (newClassroomLocationEnrollmentStatus === ClassroomLocationEnrollmentStatusEnum.PASSED) {
      const courseItemProgressHistoryData = await this.buildCourseItemProgressHistoryClassroomMarkResultPassed(
        enrollment.id,
        courseItemId,
        courseItemName,
        classroomLocation.location,
        classroomLocation.type,
        classroomLocation.code,
        {
          ...classroomLocationEnrollment,
          status: newClassroomLocationEnrollmentStatus,
          totalAttended: passAttendance,
          scoreHomework: passScoreHomework,
        },
        courseItemCriteriaConfig?.config,
      );

      await this.courseItemProgressHistoryService.save(courseItemProgressHistoryData, { session });
      this.logger.info(`Successfully insert courseItemProgressHistory type classroom`);
    }

    if (newClassroomLocationEnrollmentStatus === ClassroomLocationEnrollmentStatusEnum.NOT_PASS) {
      const courseItemProgressHistoryData = await this.buildCourseItemProgressHistoryClassroomMarkResultNotPass(
        enrollment.id,
        courseItemId,
        courseItemName,
        classroomLocation.location,
        classroomLocation.type,
        classroomLocation.code,
        {
          ...classroomLocationEnrollment,
          status: newClassroomLocationEnrollmentStatus,
          totalAttended: passAttendance,
          scoreHomework: passScoreHomework,
        },
        courseItemCriteriaConfig?.config,
      );

      await this.courseItemProgressHistoryService.save(courseItemProgressHistoryData, { session });
      this.logger.info(`Successfully insert courseItemProgressHistory type classroom`);
    }

    // Update classroom round
    const classroomLocationEnrollmentInprogress = await this.db
      .collection(DBCollection.classroom_location_enrollments)
      .findOne(
        {
          classroomRoundId: classroomRound.id,
          status: ClassroomLocationEnrollmentStatusEnum.IN_PROGRESS,
          deletedAt: null,
        },
        {
          session,
          readPreference: 'primary',
        },
      );

    if (!classroomLocationEnrollmentInprogress) {
      const newClassroomRoundStatus = ClassroomRoundStatusEnum.COMPLETED;
      await this.db.collection(DBCollection.classroom_rounds).updateOne(
        {
          id: classroomRound.id,
          status: { $ne: ClassroomRoundStatusEnum.COMPLETED },
        },
        {
          $set: {
            status: newClassroomRoundStatus,
            updatedAt: currentDate,
          },
        },
        {
          session,
        },
      );

      this.logger.info(`Successfully update classroom round ${classroomRound.id}`);
    }

    // Update enrollment
    const enrollmentId = enrollment.id;
    const currentEnrollmentStatus = enrollment.status;
    const learningProgress = enrollment.learningProgress ?? [];
    const courseItems = partList.flatMap((part) => part.courseItems);

    const existingCourseItem = courseItems.find((item) => item.id === courseItemId);
    if (!existingCourseItem) {
      throw Exception.new('enrollment course item not found', {
        enrollmentId: classroomLocationEnrollment.enrollmentId,
        classroomLocationEnrollmentId: classroomLocationEnrollment.id,
      });
    }

    const calculateNewProgress = calculateEnrollmentLearningProgress(
      newClassroomLocationEnrollmentStatus,
      learningProgress,
      courseItemId,
    );

    const { learningProgress: newLearningProgress = [], completedCourseItem = 0 } = calculateNewProgress;
    enrollment.learningProgress = newLearningProgress;
    enrollment.completedCourseItem = completedCourseItem;
    enrollment.updatedAt = currentDate;

    const isFinishedLearning = isFinishedLearningProgress(enrollment.learningProgress, courseVersion.totalCourseItems);
    if (
      isFinishedLearning &&
      [EnrollmentStatusEnum.IN_PROGRESS, EnrollmentStatusEnum.PENDING_RESULT, EnrollmentStatusEnum.EXPIRED].includes(
        currentEnrollmentStatus,
      )
    ) {
      enrollment.finishedAt = currentDate;
      enrollment.snapshot = null;

      await this.db.collection(DBCollection.logs_snapshot_enrollments).deleteMany(
        { enrollmentId, eventType: 'SNAPSHOT' },
        {
          session,
        },
      );
    } else {
      enrollment.finishedAt = null;
    }

    const courseItemDurations = partCourseItemList.map((item) => {
      return { id: item.id, duration: item.duration, type: item.type };
    });

    const allCourseItemCriteriaConfigsWithCourseVersion = await this.db
      .collection(DBCollection.course_item_criteria_configs)
      .find({
        isEnabled: true,
        courseVersionId: courseVersion.id,
        courseItemId: { $in: courseItemIds },
      })
      .toArray();

    const courseItemQuizPostTestList = courseItems.filter(
      (item) => item.type === MaterialMediaTypeEnum.QUIZ && item.testType === QuizTestTypeEnum.POST_TEST,
    );

    const quizAnswerPostTestIds = courseItemQuizPostTestList.map((item) => {
      const quizPostTestInLearningProgress = newLearningProgress.find((lp) => lp.courseItemId === item.id);
      if (quizPostTestInLearningProgress) {
        return quizPostTestInLearningProgress.quizAnswer?.id;
      }

      return null;
    });

    const postTestQuizAnswers = await this.db
      .collection(DBCollection.quiz_answers)
      .find({
        id: { $in: quizAnswerPostTestIds },
        testType: QuizTestTypeEnum.POST_TEST,
      })
      .toArray();

    const isCourseDurationSec =
      courseVersion.totalDurationSec > 0 ||
      (courseVersion.isCountdownArticle && courseVersion.totalDurationArticleSec > 0);
    const isCourseQuizPostTest = courseItemQuizPostTestList.length > 0;

    const enrollmentAchievements = await this.db
      .collection(DBCollection.enrollment_achievements)
      .find({
        enrollmentId,
      })
      .toArray();

    const enrolledAchievementIds = enrollmentAchievements.map((item) => item.achievementId);

    const achievements = await this.db
      .collection(DBCollection.achievements)
      .aggregate([
        {
          $match: {
            id: { $nin: enrolledAchievementIds },
            organizationId,
            isEnabled: true,
            publishedAt: { $ne: null },
            type: enrollment.enrollType,
            $or: [{ courseIds: enrollment.courseId }, { courseIds: { $size: 0 } }],
            $expr: {
              $and: [
                {
                  $or: [{ $eq: ['$startedAt', null] }, { $lte: ['$startedAt', date().toDate()] }],
                },
                {
                  $or: [{ $eq: ['$endedAt', null] }, { $gte: ['$endedAt', date().toDate()] }],
                },
              ],
            },
            ...(!isCourseDurationSec && { 'criteria.timeSpent.isEnabled': false }),
            ...(!isCourseQuizPostTest && { 'criteria.quiz.isEnabled': false }),
          },
        },
      ])
      .toArray();

    const isPassedAchievementCriteria = achievements.some((item) => {
      const isPassedAchievementLearningProgress = isPassedCriteriaLearningProgress(
        newLearningProgress,
        item.criteria.learningProgress,
        courseVersion.totalCourseItems,
      );

      const isPassedAchievementTimeSpent = isPassedCriteriaTimeSpent(
        newLearningProgress,
        courseItemDurations,
        item.criteria.timeSpent,
        courseVersion.isCountdownArticle,
        courseVersion.totalDurationSec + (courseVersion.isCountdownArticle ? courseVersion.totalDurationArticleSec : 0),
      );

      const isPassedAchievementPostTestQuiz = isPassedCriteriaPostTestQuiz(
        postTestQuizAnswers,
        courseItems,
        item.criteria.quiz,
      );

      return isPassedAchievementLearningProgress && isPassedAchievementTimeSpent && isPassedAchievementPostTestQuiz;
    });

    const isPassedLearning = isPassedAllLearningCriteria({
      learningProgress: enrollment.learningProgress,
      courseItemDurations,
      completionCriteria: courseVersion.completionCriteria,
      courseItemCriteriaConfigs: allCourseItemCriteriaConfigsWithCourseVersion,
      isCountdownArticle: courseVersion.isCountdownArticle,
      totalDurationSec: courseVersion.totalDurationSec,
      totalDurationArticleSec: courseVersion.totalDurationArticleSec,
      totalCourseItems: courseVersion.totalCourseItems,
    });

    const isCourseObjectiveTypeRegular = course.objectiveType === CourseObjectiveTypeEnum.REGULAR;
    const isCourseObjectiveTypeNotRegular = course.objectiveType === CourseObjectiveTypeEnum.TRAINING;
    const isEnrollmentExpired = date().isAfter(date(enrollment.expiredAt));

    if (
      isPassedLearning &&
      [EnrollmentStatusEnum.IN_PROGRESS, EnrollmentStatusEnum.PENDING_RESULT, EnrollmentStatusEnum.EXPIRED].includes(
        currentEnrollmentStatus,
      )
    ) {
      enrollment.passedAt = currentDate;
      if (isCourseObjectiveTypeRegular && isEnrollmentExpired) {
        enrollment.status = EnrollmentStatusEnum.COMPLETED;
      } else {
        enrollment.status = EnrollmentStatusEnum.PASSED;
      }
    } else {
      enrollment.passedAt = null;
    }

    if (!isPassedLearning && isEnrollmentExpired) {
      enrollment.status = EnrollmentStatusEnum.EXPIRED;
    }

    if (isEditMarkResult) {
      const allEnrollments = await this.db
        .collection(DBCollection.enrollments)
        .find({ userId: enrollment.userId, courseId: enrollment.courseId }, { session, readPreference: 'primary' })
        .toArray();

      const allPreEnrollments = await this.db
        .collection(DBCollection.pre_enrollment_transactions)
        .find({ userId: enrollment.userId, 'payload.courseCode': course.code }, { session, readPreference: 'primary' })
        .toArray();

      const isLastEnrollment = checkIsLatestEnrollment(enrollment.id, allEnrollments, allPreEnrollments);
      if (!isLastEnrollment) {
        enrollment.expiredAt = currentDate;
      }

      const classroomLocationEnrollmentIds = compact(
        enrollment.learningProgress.map((item) => item.classroomLocationEnrollmentId),
      );

      const classroomLocationEnrollments = await this.db
        .collection(DBCollection.classroom_location_enrollments)
        .find({ id: { $in: classroomLocationEnrollmentIds }, deletedAt: null })
        .toArray();

      const isAllClassroomMarkResult = checkIsAllClassroomMarkResult(
        courseVersion.totalClassrooms,
        classroomLocationEnrollments,
      );

      const newEnrollmentStatus = getEnrollmentStatusEditClassroomMarkResult(
        course.objectiveType,
        currentEnrollmentStatus,
        classroomLocationEnrollment.status,
        newClassroomLocationEnrollmentStatus,
        isPassedLearning,
        isEnrollmentExpired || !isLastEnrollment,
        isAllClassroomMarkResult,
      );

      enrollment.status = newEnrollmentStatus;
    }

    await this.db.collection(DBCollection.enrollments).updateOne(
      {
        id: enrollmentId,
      },
      {
        $set: {
          ...enrollment,
        },
      },
      {
        session,
      },
    );

    this.logger.info(`Successfully update enrollment ${enrollmentId}`);

    const enrollmentCertificates =
      await this.enrollmentCertificateService.findWithOrganizationCertificateDetail(enrollmentId);

    const notifyCourse = getNotifyCourseEditClassroomMarkResult(
      course.objectiveType,
      currentEnrollmentStatus,
      enrollment.status,
      !!enrollmentCertificates.length,
    );

    if (isCourseObjectiveTypeRegular) {
      if (notifyCourse === UserNotificationTypeEnum.ENROLLMENT_PASSED_WITH_CERTIFICATE) {
        const sendNotifyEnrollmentCertificates = enrollmentCertificates.filter((item) => !item.isSentEmail);
        await Promise.all(
          sendNotifyEnrollmentCertificates.map(async (enrollmentCertificate) => {
            const { courseVersionCertificate, organizationCertificate, certificateDetail } = enrollmentCertificate;

            const certificateProperties = certificateDetail.properties;
            const organizationCertificateProperties = organizationCertificate.properties;
            const resultProperties = this.certificateService.mergeProperties(
              certificateProperties,
              organizationCertificateProperties,
            );
            const { slugName } = certificateDetail;

            const dynamicValue = await this.getCertificateDynamicValue({
              resultProperties,
              organizationId: organization.id,
              userId: user.guid,
              courseId: course.id,
              courseVersionId: courseVersion.id,
              learningPathId: null,
              learningPathVersionId: null,
            });

            const certificatePayload = {
              dynamic_value: dynamicValue,
              slug_name: slugName,
              metadata_url: this.certificateService.genMetaDataUrl(
                enrollmentCertificate.enrollmentId,
                DomainMetaDataEnum.ENROLLMENT,
              ),
            };

            await this.certificateService.pretestGenerateCertificate(certificatePayload);

            const { certificateCode, certificateUrl, certificatePDFUrl } =
              await this.certificateService.create(certificatePayload);

            const mailPayload = {
              fullName: getFullName(user.profile),
              courseName,
              certificateUrl,
              certificatePDFUrl,
              certificateCode,
              refName: courseVersionCertificate.refName,
              objectiveType: CourseObjectiveTypeEnum.REGULAR,
              regulator: RegulatorEnum.NONE,
            };

            let message;

            if (enrollmentCertificates.length > 1) {
              message = buildApprovedWithCertificateNotificationMessage(
                courseName,
                false,
                false,
                courseVersionCertificate.refName,
              );
            } else {
              message = buildApprovedWithCertificateNotificationMessage(courseName, false, false);
            }

            const inAppPayload = buildUserNotificationModel(
              userId,
              organizationId,
              UserNotificationTypeEnum.ENROLLMENT_PASSED_WITH_CERTIFICATE,
              {
                mediaId: course.thumbnailMediaId,
                message,
                url: {
                  code: course.url,
                  enrollmentId,
                  certificateUrl,
                },
              },
            );

            enrollmentCompleteWithCertificateNotifications.push({
              email: user.email,
              mailPayload,
              inAppPayload,
              organization,
            });

            userNotificationBulkWriteOperations.push({
              insertOne: {
                document: inAppPayload,
              },
            });

            enrollmentCertificate.isSentEmail = true;
            enrollmentCertificate.certificateCode = certificateCode;
            enrollmentCertificate.certificateUrl = certificateUrl;
            enrollmentCertificate.updatedAt = currentDate;

            enrollmentCertificateBulkWriteOperations.push({
              updateOne: {
                filter: { id: enrollmentCertificate.id },
                update: {
                  $set: enrollmentCertificate,
                },
              },
            });
          }),
        );
      }

      if (notifyCourse === UserNotificationTypeEnum.ENROLLMENT_PASSED) {
        const message = buildApprovedRegularNotificationMessage(courseName);

        const inAppPayload = buildUserNotificationModel(
          userId,
          organizationId,
          UserNotificationTypeEnum.ENROLLMENT_PASSED,
          {
            mediaId: course.thumbnailMediaId,
            message,
            url: {
              code: course.url,
              enrollmentId,
            },
          },
        );

        enrollmentCompleteWithCertificateNotifications.push({
          email: user.email,
          mailPayload: null,
          inAppPayload,
          organization,
        });

        userNotificationBulkWriteOperations.push({
          insertOne: {
            document: inAppPayload,
          },
        });
      }

      if (notifyCourse === UserNotificationTypeEnum.CANCEL_COURSE_PASS_RESULT) {
        const sendNotifyEnrollmentCertificates = enrollmentCertificates.filter((item) => item.isSentEmail);
        if (sendNotifyEnrollmentCertificates.length > 0) {
          for (const enrollmentCertificate of sendNotifyEnrollmentCertificates) {
            const message = buildCancelCoursePassResultNotificationMessage({
              courseName: courseVersion.name,
              certificateCode: enrollmentCertificate.certificateCode || '',
              expiredAt: enrollment.expiredAt,
            });

            const mailPayload = {
              firstName: user.profile?.firstname || '',
              lastName: user.profile?.lastname || '',
              courseName: courseVersion.name,
              thumbnailUrl: course.thumbnailUrl,
              url: course.url,
              certificateCode: enrollmentCertificate.certificateCode || '',
              expiredAt: enrollment.expiredAt,
            };

            const inAppPayload = buildUserNotificationModel(
              userId,
              organizationId,
              UserNotificationTypeEnum.CANCEL_COURSE_PASS_RESULT,
              {
                mediaId: course.thumbnailMediaId,
                message,
                url: {
                  code: course.url,
                },
              },
            );

            cancelRegularCourseCertificateNotifications.push({
              email: user.email,
              mailPayload,
              inAppPayload,
              organization,
            });

            userNotificationBulkWriteOperations.push({
              insertOne: {
                document: inAppPayload,
              },
            });

            enrollmentCertificate.isSentEmail = false;
            enrollmentCertificate.certificateCode = '';
            enrollmentCertificate.certificateUrl = '';
            enrollmentCertificate.updatedAt = currentDate;
            enrollmentCertificateBulkWriteOperations.push({
              updateOne: {
                filter: { id: enrollmentCertificate.id },
                update: {
                  $set: enrollmentCertificate,
                },
              },
            });
          }
        } else {
          const message = buildCancelCoursePassResultNotificationMessage({
            courseName: courseVersion.name,
            expiredAt: enrollment.expiredAt,
            isRound: !!enrollment.roundId,
          });

          const mailPayload = {
            firstName: user.profile?.firstname || '',
            lastName: user.profile?.lastname || '',
            courseName: courseVersion.name,
            thumbnailUrl: course.thumbnailUrl,
            url: course.url,
            expiredAt: enrollment.expiredAt,
            isRound: !!enrollment.roundId,
          };

          const inAppPayload = buildUserNotificationModel(
            userId,
            organizationId,
            UserNotificationTypeEnum.CANCEL_COURSE_PASS_RESULT,
            {
              mediaId: course.thumbnailMediaId,
              message,
              url: {
                code: course.url,
              },
            },
          );

          cancelRegularCourseCertificateNotifications.push({
            email: user.email,
            mailPayload,
            inAppPayload,
            organization,
          });

          userNotificationBulkWriteOperations.push({
            insertOne: {
              document: inAppPayload,
            },
          });
        }
      }

      if (enrollment.preAssignContentId && isPassedLearning) {
        const preAssignContent = await this.preAssignContentService.findOne({
          id: enrollment.preAssignContentId,
          organizationId,
        });

        const assignee = isNull(preAssignContent)
          ? null
          : await this.userService.findOne({ guid: preAssignContent.createdByUserId, organizationId });

        if (assignee) {
          const learnerFullName = `${user.profile.firstname ?? ''} ${user.profile.lastname ?? ''}`.trim();
          const assigneeFullName = `${assignee.profile.firstname ?? ''} ${assignee.profile.lastname ?? ''}`.trim();
          const mailPayload = {
            learnerUserId: enrollment.userId,
            contentName: courseVersion.name,
            contentType: PreAssignContentTypeEnum.COURSE,
            learnerFullName,
            fullName: assigneeFullName,
            round: {
              roundDate: roundOfEnrollment?.roundDate,
              expiredDate: enrollment?.expiredAt,
            },
          };

          const message = buildAssignEnrollmentCompletedNotificationMessage({
            fullName: learnerFullName || '',
            contentName: courseVersion.name,
            roundDate: roundOfEnrollment?.roundDate,
            expiryDay: courseVersion?.expiryDay || 0,
          });

          const inAppPayload = buildUserNotificationModel(
            assignee.guid,
            organizationId,
            UserNotificationTypeEnum.ASSIGN_ENROLLMENT_COMPLETED,
            {
              mediaId: course.thumbnailMediaId,
              message,
              url: {
                code: course.code,
                userId: enrollment.userId,
                enrollmentId: enrollment.id,
              },
            },
          );

          assignEnrollmentCompleteNotifications.push({
            email: user.email,
            mailPayload,
            inAppPayload,
            organization,
          });

          userNotificationBulkWriteOperations.push({
            insertOne: {
              document: inAppPayload,
            },
          });
        }
      }
    }

    if (isCourseObjectiveTypeNotRegular) {
      if (notifyCourse === UserNotificationTypeEnum.ENROLLMENT_REJECT) {
        const message = buildEnrollmentRejectOrExpireNotificationMessage(courseVersion.name);

        const mailPayload = { courseName: courseVersion.name, fullName: getFullName(user.profile) };

        const inAppPayload = buildUserNotificationModel(
          userId,
          organizationId,
          UserNotificationTypeEnum.ENROLLMENT_REJECT,
          {
            mediaId: course.thumbnailMediaId,
            message,
            url: {
              code: course.url,
              enrollmentId: enrollment.id,
            },
          },
        );

        rejectEnrollmentNotifications.push({
          email: user.email,
          mailPayload,
          inAppPayload,
          organization,
        });

        userNotificationBulkWriteOperations.push({
          insertOne: {
            document: inAppPayload,
          },
        });
      }

      if (notifyCourse === UserNotificationTypeEnum.CANCEL_COURSE_APPROVAL) {
        const sendNotifyEnrollmentCertificates = enrollmentCertificates.filter((item) => item.isSentEmail);
        if (sendNotifyEnrollmentCertificates.length > 0) {
          for (const enrollmentCertificate of sendNotifyEnrollmentCertificates) {
            const message = buildCancelCourseApprovalNotificationMessage({
              courseName: courseVersion.name,
              certificateCode: enrollmentCertificate.certificateCode || '',
              expiredAt: enrollment.expiredAt,
            });

            const mailPayload = {
              firstName: user.profile?.firstname || '',
              lastName: user.profile?.lastname || '',
              courseName: courseVersion.name,
              thumbnailUrl: course.thumbnailUrl,
              url: course.url,
              certificateCode: enrollmentCertificate.certificateCode || '',
              expiredAt: enrollment.expiredAt,
            };

            const inAppPayload = buildUserNotificationModel(
              userId,
              organizationId,
              UserNotificationTypeEnum.CANCEL_COURSE_APPROVAL,
              {
                mediaId: course.thumbnailMediaId,
                message,
                url: {
                  code: course.url,
                },
              },
            );

            cancelTrainingCourseCertificateNotifications.push({
              email: user.email,
              mailPayload,
              inAppPayload,
              organization,
            });

            userNotificationBulkWriteOperations.push({
              insertOne: {
                document: inAppPayload,
              },
            });

            enrollmentCertificate.isSentEmail = false;
            enrollmentCertificate.certificateCode = '';
            enrollmentCertificate.certificateUrl = '';
            enrollmentCertificate.updatedAt = currentDate;
            enrollmentCertificateBulkWriteOperations.push({
              updateOne: {
                filter: { id: enrollmentCertificate.id },
                update: {
                  $set: enrollmentCertificate,
                },
              },
            });
          }
        } else {
          const message = buildCancelCourseApprovalNotificationMessage({
            courseName: courseVersion.name,
            expiredAt: enrollment.expiredAt,
          });

          const mailPayload = {
            firstName: user.profile?.firstname || '',
            lastName: user.profile?.lastname || '',
            courseName: courseVersion.name,
            thumbnailUrl: course.thumbnailUrl,
            url: course.url,
            expiredAt: enrollment.expiredAt,
          };

          const inAppPayload = buildUserNotificationModel(
            userId,
            organizationId,
            UserNotificationTypeEnum.CANCEL_COURSE_APPROVAL,
            {
              mediaId: course.thumbnailMediaId,
              message,
              url: {
                code: course.url,
              },
            },
          );

          cancelTrainingCourseCertificateNotifications.push({
            email: user.email,
            mailPayload,
            inAppPayload,
            organization,
          });

          userNotificationBulkWriteOperations.push({
            insertOne: {
              document: inAppPayload,
            },
          });
        }
      }

      if (isPassedAchievementCriteria) {
        const queueName = `${organization.domain}:enrollment_achievement`;
        const operationType = BulkOpTypeEnum.ENROLLMENT_ACHIEVEMENT_COMPLETED;
        const queuePayload = { enrollmentId };

        this.queueService.publish(queueName, JSON.stringify(queuePayload), {
          persistent: true,
          headers: {
            operationType,
          },
        });
      }

      if (!notifyCourse && isPassedLearning) {
        // Send to Auto approve
        const queueName = `${domain}:auto-approve-enrollment`;
        const queuePayload = { enrollmentId: enrollment.id, organizationId: enrollment.organizationId };

        this.queueService.publish(queueName, JSON.stringify(queuePayload), {
          persistent: true,
        });
      }
    }

    const isUpdateLearningPathEnrollmentAndNotify =
      isCourseObjectiveTypeRegular ||
      (isCourseObjectiveTypeNotRegular &&
        currentEnrollmentStatus === EnrollmentStatusEnum.APPROVED &&
        [EnrollmentStatusEnum.IN_PROGRESS, EnrollmentStatusEnum.EXPIRED].includes(enrollment.status));

    if (isUpdateLearningPathEnrollmentAndNotify) {
      const resLearningPathEnrollments = await this.learningPathEnrollmentService.find({
        userId,
        status: {
          $in: [
            LearningPathEnrollmentStatusEnum.ASSIGNED,
            LearningPathEnrollmentStatusEnum.IN_PROGRESS,
            LearningPathEnrollmentStatusEnum.COMPLETED,
            LearningPathEnrollmentStatusEnum.EXPIRED,
          ],
        },
        contentProgress: {
          $elemMatch: { enrollmentId },
        },
      });

      const learningPathEnrollments = resLearningPathEnrollments.filter((lpe) =>
        checkIsAllowUpdateLearningPathEnrollmentExpired(
          currentDate,
          lpe.expiredAt,
          isPassedLearning || isFinishedLearning,
          enrollment.learningProgress,
        ),
      );

      if (learningPathEnrollments.length > 0) {
        const roundIds = chain(learningPathEnrollments).map('roundId').compact().uniq().value();
        const preAssignContentIds = chain(learningPathEnrollments).map('preAssignContentId').compact().uniq().value();

        const [rounds, preAssignContents] = await Promise.all([
          this.roundService.find({ id: { $in: roundIds } }),
          this.preAssignContentService.find({
            id: { $in: preAssignContentIds },
            organizationId: enrollment.organizationId,
          }),
        ]);

        const assigneeUserIds = chain(preAssignContents).map('createdByUserId').compact().uniq().value();

        const [assignee, learningPathVersions] = await Promise.all([
          this.userService.find({ guid: { $in: assigneeUserIds } }),
          this.learningPathVersionService.find({
            $or: learningPathEnrollments.map((learningPathEnrollment) => ({
              learningPathId: learningPathEnrollment.learningPathId,
              version: learningPathEnrollment.version,
            })),
          }),
        ]);

        const learningPathIds = learningPathVersions.map((learningPathVersion) => learningPathVersion.learningPathId);
        const learningPaths = await this.learningPathService.find({ id: { $in: learningPathIds } });

        const learningPathMapWithId = learningPaths.reduce((acc, cur) => {
          const key = String(cur.id);
          acc.set(key, cur);
          return acc;
        }, new Map());

        const learningPathVersionsMapper = learningPathVersions.reduce((acc, val) => {
          const key = `${val.learningPathId}_${val.version}`;
          acc.set(key, val);
          return acc;
        }, new Map());

        const assigneeMapWithId = assignee.reduce((acc, cur) => {
          const key = String(cur.guid);
          acc.set(key, cur);
          return acc;
        }, new Map());

        const roundMapWithId = rounds.reduce((acc, cur) => {
          const key = String(cur.id);
          acc.set(key, cur);
          return acc;
        }, new Map());

        const preAssignContentMapWithId = preAssignContents.reduce((acc, cur) => {
          const key = String(cur.id);
          acc.set(key, cur);
          return acc;
        }, new Map());

        const changeLearningPathEnrollmentStatusMapper = {};

        learningPathVersions.splice(0, learningPathVersions.length);

        const [certificationLearningPathEnrollments, nonCertificationLearningPathEnrollments] = chain(
          learningPathEnrollments,
        )
          .map((learningPathEnrollment) => {
            const learningPathContentStatus = getLearningPathContentProgressStatus(enrollment.status);
            this.updateContentProgressStatus(learningPathEnrollment, enrollmentId, learningPathContentStatus);

            const completedContentItems = learningPathEnrollment.contentProgress.filter(
              (contentProgressItem) => contentProgressItem.status === LearningPathContentProgressStatusEnum.COMPLETED,
            );

            learningPathEnrollment.completedContentItem = completedContentItems.length;

            const key = `${learningPathEnrollment.learningPathId}_${learningPathEnrollment.version}`;
            const learningPathOfLPEnrollment = learningPathMapWithId.get(String(learningPathEnrollment.learningPathId));
            const learningPathVersionOfLPEnrollment = learningPathVersionsMapper.get(key);
            const roundOfLPEnrollment = roundMapWithId.get(String(learningPathEnrollment?.roundId));
            const preAssignContentOfLPEnrollment = preAssignContentMapWithId.get(
              String(learningPathEnrollment?.preAssignContentId),
            );
            const assigneeOfLPEnrollment = assigneeMapWithId.get(
              String(preAssignContentOfLPEnrollment?.createdByUserId),
            );

            if (!learningPathVersionOfLPEnrollment) {
              return {
                isVerifyCriteriaCertificate: false,
                learningPathEnrollment,
              };
            }

            const isLearningPathEnrollmentExpired = date().isAfter(date(learningPathEnrollment.expiredAt));

            // Check is complete all content in learning path
            const isCompleteLearningProgress =
              learningPathEnrollment.completedContentItem === learningPathVersionOfLPEnrollment.contents.length;

            const currentLearningPathEnrollmentStatus = learningPathEnrollment.status;
            const newLearningPathEnrollmentStatus = getLearningPathEnrollmentStatusEditClassroomMarkResult(
              currentLearningPathEnrollmentStatus,
              isCompleteLearningProgress,
              isLearningPathEnrollmentExpired,
            );

            set(changeLearningPathEnrollmentStatusMapper, [learningPathEnrollment.id], {
              beforeStatus: currentLearningPathEnrollmentStatus,
              afterStatus: newLearningPathEnrollmentStatus,
              isPassedCriteria: null,
            });

            if (isCompleteLearningProgress) {
              learningPathEnrollment.finishedAt = currentDate;
              learningPathEnrollment.status = newLearningPathEnrollmentStatus;
              learningPathEnrollment.updatedAt = currentDate;

              if (assigneeOfLPEnrollment) {
                const learnerFullName = `${user.profile.firstname ?? ''} ${user.profile.lastname ?? ''}`.trim();
                const assigneeFullName = `${assigneeOfLPEnrollment.profile.firstname ?? ''} ${
                  assigneeOfLPEnrollment.profile.lastname ?? ''
                }`.trim();
                const mailPayload = {
                  learnerUserId: learningPathEnrollment.userId,
                  contentName: learningPathVersionOfLPEnrollment.name,
                  contentType: PreAssignContentTypeEnum.LEARNING_PATH,
                  learnerFullName,
                  fullName: assigneeFullName,
                  round: {
                    roundDate: roundOfLPEnrollment?.roundDate,
                    expiredDate: learningPathEnrollment?.expiredAt,
                  },
                };

                const message = buildAssignLearningPathEnrollmentCompletedNotificationMessage({
                  fullName: learnerFullName || '',
                  contentName: learningPathVersionOfLPEnrollment.name,
                  roundDate: roundOfLPEnrollment?.roundDate,
                  expiredAt: learningPathEnrollment?.expiredAt,
                });

                const inAppPayload = buildUserNotificationModel(
                  assigneeOfLPEnrollment.guid,
                  organizationId,
                  UserNotificationTypeEnum.ASSIGN_LEARNING_PATH_ENROLLMENT_COMPLETED,
                  {
                    mediaId: learningPathOfLPEnrollment.thumbnailMediaId,
                    message,
                    url: {
                      code: learningPathOfLPEnrollment.code,
                      userId: learningPathEnrollment.userId,
                      learningPathEnrollmentId: learningPathEnrollment.id,
                    },
                  },
                );

                assignLearningPathEnrollmentCompleteNotifications.push({
                  email: assigneeOfLPEnrollment.email,
                  mailPayload,
                  inAppPayload,
                });

                userNotificationBulkWriteOperations.push({
                  insertOne: {
                    document: inAppPayload,
                  },
                });
              }
            } else {
              learningPathEnrollment.finishedAt = null;
              learningPathEnrollment.status = newLearningPathEnrollmentStatus;
              learningPathEnrollment.updatedAt = currentDate;
            }

            if (isCompleteLearningProgress && !learningPathVersionOfLPEnrollment.isCertificateEnabled) {
              learningPathEnrollment.passedAt = currentDate;
              learningPathEnrollment.updatedAt = currentDate;
            } else {
              learningPathEnrollment.passedAt = null;
            }

            // passed criteria learning path
            const isVerifyCriteriaCertificate =
              this.syncCourseLearningProgressService.validateIsLearningPathEnrollmentNeedToVerifyCertificateCriteria(
                learningPathVersionOfLPEnrollment,
                learningPathEnrollment,
              );

            return {
              isVerifyCriteriaCertificate,
              learningPathVersion: learningPathVersionOfLPEnrollment,
              learningPathEnrollment,
            };
          })
          .partition(({ isVerifyCriteriaCertificate }) => isVerifyCriteriaCertificate)
          .value();

        const learningEnrollmentForSendCertifications = [];

        if (certificationLearningPathEnrollments.length > 0) {
          const allCriteriaEnableContentIds = chain(certificationLearningPathEnrollments)
            .map('learningPathVersion')
            .map('contents')
            .flatten()
            .filter((content) => content.isCriteriaEnabled)
            .map('courseId')
            .value();

          const allEnrollmentContentIds = chain(certificationLearningPathEnrollments)
            .map('learningPathEnrollment')
            .map('contentProgress')
            .flatten()
            .map('enrollmentId')
            .filter((id) => !isNil(id))
            .value();

          const [courses, enrollments] = await Promise.all([
            this.db
              .collection(DBCollection.courses)
              .find({ id: { $in: allCriteriaEnableContentIds } }, { projection: { id: 1, objectiveType: 1 } })
              .toArray(),
            //find enrollment in session
            this.db
              .collection(DBCollection.enrollments)
              .find({ id: { $in: allEnrollmentContentIds } }, { session, readPreference: 'primary' })
              .toArray(),
          ]);

          const courseMappedById = courses.reduce((acc, val) => {
            const key = String(val.id);
            acc.set(key, val);
            return acc;
          }, new Map());

          const enrollmentMappedById = enrollments.reduce((acc, val) => {
            const key = String(val.id);
            acc.set(key, val);
            return acc;
          }, new Map());

          const courseIds = courses.map((val) => val.id);
          const enrollmentCourseIds = enrollments.map((val) => val.courseId);
          const enrollmentCourseVersionIds = enrollments.map((val) => val.courseVersionId);
          const notEnrollCourseIds = courseIds.filter((val) => {
            return enrollmentCourseIds.indexOf(val) === -1;
          });
          const enrollmentCourseVersions = await this.db
            .collection(DBCollection.course_versions)
            .find({
              id: { $in: enrollmentCourseVersionIds },
            })
            .toArray();
          const notEnrollCourseVersions = await this.db
            .collection(DBCollection.course_versions)
            .find({
              courseId: { $in: notEnrollCourseIds },
              status: CourseVersionStatusEnum.PUBLISHED,
            })
            .toArray();
          const courseVersions = [...enrollmentCourseVersions, ...notEnrollCourseVersions];

          const courseVersionMappedByCourseId = courseVersions.reduce((acc, val) => {
            const key = String(val.courseId);
            acc.set(key, val);
            return acc;
          }, new Map());

          for (const [index, certificationLearningPathEnrollment] of certificationLearningPathEnrollments.entries()) {
            const { learningPathEnrollment } = certificationLearningPathEnrollment;

            const isPassedCriteria =
              this.learningPathEnrollmentCertificateService.validateIsLearningPathEnrollmentPassedCriteriaCertificate(
                certificationLearningPathEnrollment.learningPathEnrollment,
                certificationLearningPathEnrollment.learningPathVersion?.contents || [],
                courseMappedById,
                courseVersionMappedByCourseId,
                enrollmentMappedById,
              );

            set(
              changeLearningPathEnrollmentStatusMapper,
              [learningPathEnrollment.id, 'isPassedCriteria'],
              isPassedCriteria,
            );

            if (!isPassedCriteria) continue;

            const isNotPassedAtExisting = isNil(learningPathEnrollment.passedAt);
            if (isNotPassedAtExisting) {
              certificationLearningPathEnrollments[index].learningPathEnrollment.passedAt = currentDate;
              learningEnrollmentForSendCertifications.push(
                certificationLearningPathEnrollments[index].learningPathEnrollment,
              );
            }
          }

          // Clean up memory
          courseMappedById.clear();
          enrollmentMappedById.clear();
        }

        const updatedLearningPathEnrollmentsContentProgress = [
          ...nonCertificationLearningPathEnrollments.map((val) => val.learningPathEnrollment),
          ...certificationLearningPathEnrollments.map((val) => val.learningPathEnrollment),
        ];

        for (const item of updatedLearningPathEnrollmentsContentProgress) {
          syncCourseProgressBulkWriteOperations.push({
            updateOne: {
              filter: { id: item.id },
              update: {
                $set: {
                  ...item,
                },
              },
            },
          });
        }

        // Prepare Send notification LearningPath
        const updatedLearningPathEnrollmentIds = map(updatedLearningPathEnrollmentsContentProgress, 'id');
        const allLearningPathEnrollmentCertificates = await this.db
          .collection(DBCollection.learning_path_enrollment_certificates)
          .find({
            learningPathEnrollmentId: { $in: updatedLearningPathEnrollmentIds },
          })
          .toArray();

        const learningPathEnrollmentForSendCertificateIds = map(learningEnrollmentForSendCertifications, (v) => v.id);

        const groupLearningPathEnrollmentForSendCertificateById = groupBy(
          learningEnrollmentForSendCertifications,
          'id',
        );

        if (!isEmpty(learningPathEnrollmentForSendCertificateIds)) {
          const cloneAllLearningPathEnrollmentCertificates = cloneDeep(allLearningPathEnrollmentCertificates);
          const learningPathEnrollmentCertificates = cloneAllLearningPathEnrollmentCertificates.filter(
            (item) =>
              learningPathEnrollmentForSendCertificateIds.includes(item.learningPathEnrollmentId) && !item.isSentEmail,
          );

          const learningPathIds = learningEnrollmentForSendCertifications.map((val) => val.learningPathId);
          const learningPaths = await this.db
            .collection(DBCollection.learning_paths)
            .find({ id: { $in: learningPathIds } })
            .toArray();
          const learningPathMapWithId = learningPaths.reduce((acc, cur) => {
            const key = String(cur.id);
            acc.set(key, cur);
            return acc;
          }, new Map());

          const certificatePayloadWithLearningPathEnrollmentId = new Map();
          for (const learningEnrollmentForSendCertification of learningEnrollmentForSendCertifications) {
            const enrollmentCertificate = learningPathEnrollmentCertificates.find(
              (val) => val.learningPathEnrollmentId === learningEnrollmentForSendCertification.id,
            );

            if (!enrollmentCertificate) continue;

            const key = `${learningEnrollmentForSendCertification.learningPathId}_${learningEnrollmentForSendCertification.version}`;
            const learningPathVersion = learningPathVersionsMapper.get(key);

            const { certificate } = learningPathVersion;
            const { organizationCertificateId } = certificate;
            const organizationCertificate =
              await this.organizationCertificateService.findWithCertificateDetail(organizationCertificateId);

            const { certificateDetail } = organizationCertificate;
            const certificateProperties = certificateDetail.properties;
            const organizationCertificateProperties = organizationCertificate.properties;
            const resultProperties = this.certificateService.mergeProperties(
              certificateProperties,
              organizationCertificateProperties,
            );
            const { slugName } = certificateDetail;

            const dynamicValue = await this.getCertificateDynamicValue({
              resultProperties,
              organizationId: organization.id,
              userId: user.guid,
              courseId: null,
              courseVersionId: null,
              learningPathId: learningPathVersion.learningPathId,
              learningPathVersionId: learningPathVersion.id,
            });

            const certificatePayload = {
              dynamic_value: dynamicValue,
              slug_name: slugName,
              metadata_url: this.certificateService.genMetaDataUrl(
                learningEnrollmentForSendCertification.id,
                DomainMetaDataEnum.LEARNING_PATH_ENROLLMENT,
              ),
            };

            certificatePayloadWithLearningPathEnrollmentId.set(
              String(learningEnrollmentForSendCertification.id),
              certificatePayload,
            );
          }

          // Generate Certificate
          const certificateResults = await Promise.all(
            learningPathEnrollmentCertificates.map(async (learningPathEnrollmentCertificate) => {
              const key = String(learningPathEnrollmentCertificate.learningPathEnrollmentId);
              const payloadCertificate = certificatePayloadWithLearningPathEnrollmentId.get(key);

              if (isNil(payloadCertificate)) return null;

              await this.certificateService.pretestGenerateCertificate(payloadCertificate);

              const certificateResult = await this.certificateService.create(payloadCertificate);
              return { id: learningPathEnrollmentCertificate.id, result: certificateResult };
            }),
          );

          const certificateResultMapWithLearningPathEnrollmentCertificateId = certificateResults
            .filter((v) => !isNil(v))
            .reduce((acc, val) => {
              if (!val) return acc;
              acc.set(String(val.id), val.result);
              return acc;
            }, new Map());

          for (const learningPathEnrollmentCertificate of learningPathEnrollmentCertificates) {
            const id = String(learningPathEnrollmentCertificate.id);
            const certificateResult = certificateResultMapWithLearningPathEnrollmentCertificateId.get(id);

            if (isNil(certificateResult)) continue;

            const { certificateCode, certificateUrl, certificatePDFUrl } = certificateResult;

            learningPathEnrollmentCertificate.certificateCode = certificateCode;
            learningPathEnrollmentCertificate.certificateUrl = certificateUrl;
            learningPathEnrollmentCertificate.isSentEmail = true;
            learningPathEnrollmentCertificate.updatedAt = currentDate;
            learningPathEnrollmentCertificateBulkWriteOperations.push({
              updateOne: {
                filter: { id: learningPathEnrollmentCertificate.id },
                update: {
                  $set: {
                    ...learningPathEnrollmentCertificate,
                  },
                },
              },
            });

            const [learningPathEnrollment] =
              groupLearningPathEnrollmentForSendCertificateById[
                String(learningPathEnrollmentCertificate.learningPathEnrollmentId)
              ] ?? [];
            if (!learningPathEnrollment) continue;

            const key = `${learningPathEnrollment.learningPathId}_${learningPathEnrollment.version}`;
            const learningPathVersion = learningPathVersionsMapper.get(key);
            if (!learningPathVersion) continue;

            const learningPath = learningPathMapWithId.get(`${learningPathVersion.learningPathId}`);
            if (!learningPath) continue;

            const isCompleteLearningPath = learningPathEnrollment.status === LearningPathEnrollmentStatusEnum.COMPLETED;
            const { refName } = learningPathEnrollmentCertificate.payload;
            if (isCompleteLearningPath) {
              const mailPayload = {
                learningPathName: learningPathVersion.name,
                learningPathCode: learningPathVersion.code,
                fullName: getFullName(user.profile),
                certificateCode,
                certificateUrl,
                certificatePDFUrl,
                refName: refName || '',
                isCertificate: true,
                isComplete: true,
              };

              const inAppMessage = buildLearningPathCompleteWithCertificateNotificationMessage({
                contentName: learningPathVersion.name,
                isCertificate: true,
                isComplete: true,
              });

              const inAppPayload = buildUserNotificationModel(
                userId,
                organizationId,
                UserNotificationTypeEnum.LEARNING_PATH_ENROLLMENT_COMPLETED_WITH_CERTIFICATE,
                {
                  mediaId: learningPath.thumbnailMediaId,
                  message: inAppMessage,
                  url: {
                    code: learningPath.code,
                    learningPathEnrollmentId: learningPathEnrollment.id,
                    certificateUrl,
                  },
                },
              );

              completeLearningPathAndCertificateNotifications.push({
                email: user.email,
                mailPayload,
                inAppPayload,
                organization,
              });

              userNotificationBulkWriteOperations.push({
                insertOne: {
                  document: inAppPayload,
                },
              });
            } else {
              const mailPayload = {
                learningPathName: learningPathVersion.name,
                learningPathCode: learningPathVersion.code,
                fullName: getFullName(user.profile),
                certificateCode,
                certificateUrl,
                certificatePDFUrl,
                refName: refName || '',
                isCertificate: true,
                isComplete: false,
              };

              const inAppMessage = buildLearningPathCompleteWithCertificateNotificationMessage({
                contentName: learningPathVersion.name,
                isCertificate: true,
                isComplete: false,
              });

              const inAppPayload = buildUserNotificationModel(
                userId,
                organizationId,
                UserNotificationTypeEnum.LEARNING_PATH_ENROLLMENT_WITH_CERTIFICATE,
                {
                  mediaId: learningPath.thumbnailMediaId,
                  message: inAppMessage,
                  url: {
                    code: learningPath.code,
                    learningPathEnrollmentId: learningPathEnrollment.id,
                    certificateUrl,
                  },
                },
              );

              completeLearningPathCertificateNotifications.push({
                email: user.email,
                mailPayload,
                inAppPayload,
                organization,
              });

              userNotificationBulkWriteOperations.push({
                insertOne: {
                  document: inAppPayload,
                },
              });
            }
          }

          certificatePayloadWithLearningPathEnrollmentId.clear();
          certificateResultMapWithLearningPathEnrollmentCertificateId.clear();
        }

        const completeNonCertificationLearningPathEnrollments = nonCertificationLearningPathEnrollments.filter(
          (nonCertificationLearningPathEnrollment) =>
            !isNil(nonCertificationLearningPathEnrollment.learningPathEnrollment.finishedAt),
        );

        if (!isEmpty(completeNonCertificationLearningPathEnrollments)) {
          const learningPathIds = completeNonCertificationLearningPathEnrollments.map(
            ({ learningPathVersion }) => learningPathVersion?.learningPathId,
          );

          const learningPaths = await this.db
            .collection(DBCollection.learning_paths)
            .find({ id: { $in: learningPathIds } })
            .toArray();

          const learningPathMapWithId = learningPaths.reduce((acc, val) => {
            const key = String(val.id);
            acc.set(key, val);
            return acc;
          }, new Map());

          for (const completeNonCertificationLearningPathEnrollment of completeNonCertificationLearningPathEnrollments) {
            const { learningPathVersion, learningPathEnrollment } = completeNonCertificationLearningPathEnrollment;
            const learningPath = learningPathMapWithId.get(String(learningPathVersion?.learningPathId));
            if (!learningPath) continue;
            if (!learningPathEnrollment) continue;
            const mailPayload = {
              learningPathName: learningPathVersion.name,
              learningPathCode: learningPathVersion.code,
              fullName: getFullName(user.profile),
              certificateUrl: '',
              certificatePDFUrl: '',
              certificateCode: '',
              refName: '',
              isCertificate: false,
              isComplete: true,
            };

            const inAppMessage = buildLearningPathCompleteWithCertificateNotificationMessage({
              contentName: learningPathVersion.name,
              isCertificate: false,
              isComplete: true,
            });

            const inAppPayload = buildUserNotificationModel(
              userId,
              organizationId,
              UserNotificationTypeEnum.LEARNING_PATH_ENROLLMENT_COMPLETED,
              {
                mediaId: learningPath.thumbnailMediaId,
                message: inAppMessage,
                url: {
                  code: learningPath.code,
                  learningPathEnrollmentId: learningPathEnrollment.id,
                },
              },
            );

            userNotificationBulkWriteOperations.push({
              insertOne: {
                document: inAppPayload,
              },
            });

            completeLearningPathNotifications.push({ email: user.email, mailPayload, inAppPayload, organization });
          }
        }

        const cancelLearningPathCertificates = updatedLearningPathEnrollmentsContentProgress;
        for (const item of cancelLearningPathCertificates) {
          const changeStatus = changeLearningPathEnrollmentStatusMapper[item.id];

          if (!changeStatus) continue;

          const learningPathEnrollmentCertificates = allLearningPathEnrollmentCertificates.filter(
            (lpCertItem) => item.id === lpCertItem.learningPathEnrollmentId,
          );

          const learningPath = learningPathMapWithId.get(item.learningPathId);
          const learningPathVersionKey = `${item.learningPathId}_${item.version}`;
          const learningPathVersion = learningPathVersionsMapper.get(learningPathVersionKey);

          for (const learningPathEnrollmentCertificate of learningPathEnrollmentCertificates) {
            const isCancelCertLearningPath = checkIsCancelCertificateLearningPathEditClassroomMarkResult(
              changeStatus.beforeStatus,
              changeStatus.afterStatus,
              changeStatus.isPassedCriteria,
              learningPathEnrollmentCertificate.isSentEmail,
            );

            if (isCancelCertLearningPath) {
              const message = buildCancelLearningPathCertificateNotificationMessage({
                learningPathName: learningPathVersion.name,
                certificateCode: learningPathEnrollmentCertificate.certificateCode,
              });

              const mailPayload = {
                firstName: user.profile?.firstname || '',
                lastName: user.profile?.lastname || '',
                learningPathName: learningPathVersion.name,
                certificateCode: learningPathEnrollmentCertificate.certificateCode,
              };

              const inAppPayload = buildUserNotificationModel(
                userId,
                organizationId,
                UserNotificationTypeEnum.CANCEL_LEARNING_PATH_CERTIFICATE,
                {
                  mediaId: learningPath.thumbnailMediaId,
                  message,
                  url: {
                    code: learningPath.code,
                  },
                },
              );

              cancelLearningPathCertificateNotifications.push({
                email: user.email,
                mailPayload,
                inAppPayload,
                organization,
              });

              userNotificationBulkWriteOperations.push({
                insertOne: {
                  document: inAppPayload,
                },
              });

              learningPathEnrollmentCertificate.certificateCode = '';
              learningPathEnrollmentCertificate.certificateUrl = '';
              learningPathEnrollmentCertificate.isSentEmail = false;
              learningPathEnrollmentCertificate.updatedAt = currentDate;
              learningPathEnrollmentCertificateBulkWriteOperations.push({
                updateOne: {
                  filter: { id: learningPathEnrollmentCertificate.id },
                  update: {
                    $set: {
                      ...learningPathEnrollmentCertificate,
                    },
                  },
                },
              });
            }
          }
        }
      }
    }

    const jobTransaction = {
      jobId,
      enrollmentId: classroomLocationEnrollment.enrollmentId,
      classroomLocationEnrollmentId: classroomLocationEnrollment.id,
      status: JobTransactionStatusEnum.PASSED,
      payload,
    };

    const jobTransactionModel = buildJobTransactionModel(jobTransaction);
    await this.jobTransactionService.save(jobTransactionModel, { session });

    //#region bulk update to database
    if (syncCourseProgressBulkWriteOperations.length > 0) {
      await this.db
        .collection(DBCollection.learning_path_enrollments)
        .bulkWrite(syncCourseProgressBulkWriteOperations, { session });

      this.logger.info(
        `Successfully update learning_path_enrollments ${syncCourseProgressBulkWriteOperations.length} items`,
      );
    }

    if (enrollmentCertificateBulkWriteOperations.length > 0) {
      await this.db
        .collection(DBCollection.enrollment_certificates)
        .bulkWrite(enrollmentCertificateBulkWriteOperations, { session });

      this.logger.info(
        `Successfully update enrollment_certificates ${enrollmentCertificateBulkWriteOperations.length} items`,
      );
    }

    if (learningPathEnrollmentCertificateBulkWriteOperations.length > 0) {
      await this.db
        .collection(DBCollection.learning_path_enrollment_certificates)
        .bulkWrite(learningPathEnrollmentCertificateBulkWriteOperations, { session });

      this.logger.info(
        `Successfully update learning_path_enrollment_certificates ${learningPathEnrollmentCertificateBulkWriteOperations.length} items`,
      );
    }

    if (userNotificationBulkWriteOperations.length > 0) {
      await this.db
        .collection(DBCollection.user_notifications)
        .bulkWrite(userNotificationBulkWriteOperations, { session });

      this.logger.info(`Successfully update user_notifications ${userNotificationBulkWriteOperations.length} items`);
    }
    //#endregion

    //#region send notifications
    if (enrollmentCompleteWithCertificateNotifications.length > 0) {
      for (const item of enrollmentCompleteWithCertificateNotifications) {
        const { email, mailPayload, inAppPayload } = item;
        if (mailPayload) {
          const enrollmentMailPayload = await this.notificationService.notifyEnrollmentCompleteWithCertificate(
            email,
            mailPayload,
            organization,
          );
          this.notificationService.sendEmail(enrollmentMailPayload);
        }

        this.notificationService.sendUserNotificationInApplication(inAppPayload.payload.message || '', organizationId);
      }
    }

    if (completeLearningPathNotifications.length > 0) {
      for (const item of completeLearningPathNotifications) {
        const { email, mailPayload, inAppPayload } = item;
        const learningPathEnrollmentMailPayload =
          await this.notificationService.learningPathCompleteWithCertificateEmail(email, mailPayload, organization);

        this.notificationService.sendEmail(learningPathEnrollmentMailPayload);
        this.notificationService.sendUserNotificationInApplication(inAppPayload.payload.message || '', organizationId);
      }
    }

    if (completeLearningPathCertificateNotifications.length > 0) {
      for (const item of completeLearningPathCertificateNotifications) {
        const { email, mailPayload, inAppPayload } = item;
        const learningPathEnrollmentMailPayload =
          await this.notificationService.learningPathCompleteWithCertificateEmail(email, mailPayload, organization);

        this.notificationService.sendEmail(learningPathEnrollmentMailPayload);
        this.notificationService.sendUserNotificationInApplication(inAppPayload.payload.message || '', organizationId);
      }
    }

    if (completeLearningPathAndCertificateNotifications.length > 0) {
      for (const item of completeLearningPathAndCertificateNotifications) {
        const { email, mailPayload, inAppPayload } = item;
        const learningPathEnrollmentMailPayload =
          await this.notificationService.learningPathCompleteWithCertificateEmail(email, mailPayload, organization);

        this.notificationService.sendEmail(learningPathEnrollmentMailPayload);
        this.notificationService.sendUserNotificationInApplication(inAppPayload.payload.message || '', organizationId);
      }
    }

    if (assignEnrollmentCompleteNotifications.length > 0) {
      for (const item of assignEnrollmentCompleteNotifications) {
        const { email, mailPayload, inAppPayload } = item;
        const assignEnrollmentCompletePayload = await this.notificationService.assignEnrollmentCompleteEmail(
          email,
          mailPayload,
          organization,
        );

        this.notificationService.sendEmail(assignEnrollmentCompletePayload);
        this.notificationService.sendUserNotificationInApplication(inAppPayload.payload.message || '', organizationId);
      }
    }

    if (assignLearningPathEnrollmentCompleteNotifications.length > 0) {
      for (const item of assignLearningPathEnrollmentCompleteNotifications) {
        const { email, mailPayload, inAppPayload } = item;
        const assignLearningPathEnrollmentCompletePayload =
          await this.notificationService.assignEnrollmentCompleteEmail(email, mailPayload, organization);

        this.notificationService.sendEmail(assignLearningPathEnrollmentCompletePayload);
        this.notificationService.sendUserNotificationInApplication(inAppPayload.payload.message || '', organizationId);
      }
    }

    if (cancelRegularCourseCertificateNotifications.length > 0) {
      for (const item of cancelRegularCourseCertificateNotifications) {
        const { email, mailPayload, inAppPayload } = item;

        const cancelRegularCourseCertificateMailPayload = await this.notificationService.cancelCoursePassResultEmail(
          email,
          mailPayload,
          organization,
        );

        this.notificationService.sendEmail(cancelRegularCourseCertificateMailPayload);
        this.notificationService.sendUserNotificationInApplication(inAppPayload.payload.message || '', organizationId);
      }
    }

    if (cancelTrainingCourseCertificateNotifications.length > 0) {
      for (const item of cancelTrainingCourseCertificateNotifications) {
        const { email, mailPayload, inAppPayload } = item;

        const cancelTrainingCourseCertificateMailPayload = await this.notificationService.cancelCourseApprovalEmail(
          email,
          mailPayload,
          organization,
        );

        this.notificationService.sendEmail(cancelTrainingCourseCertificateMailPayload);
        this.notificationService.sendUserNotificationInApplication(inAppPayload.payload.message || '', organizationId);
      }
    }

    if (rejectEnrollmentNotifications.length > 0) {
      for (const item of rejectEnrollmentNotifications) {
        const { email, mailPayload, inAppPayload } = item;

        const rejectEnrollmentMailPayload = await this.notificationService.enrollmentReject(
          email,
          mailPayload,
          organization,
        );

        this.notificationService.sendEmail(rejectEnrollmentMailPayload);
        this.notificationService.sendUserNotificationInApplication(inAppPayload.payload.message || '', organizationId);
      }
    }

    if (cancelLearningPathCertificateNotifications.length > 0) {
      for (const item of cancelLearningPathCertificateNotifications) {
        const { email, mailPayload, inAppPayload } = item;

        const cancelLearningPathCertificateMailPayload =
          await this.notificationService.cancelLearningPathCertificateEmail(email, mailPayload, organization);

        this.notificationService.sendEmail(cancelLearningPathCertificateMailPayload);
        this.notificationService.sendUserNotificationInApplication(inAppPayload.payload.message || '', organizationId);
      }
    }
    //#endregion
  }

  updateContentProgressStatus(learningPathEnrollment, enrollmentId, status) {
    const currentDate = date().toDate();
    const { contentProgress } = learningPathEnrollment;
    const index = contentProgress.findIndex((contentProgressItem) => contentProgressItem.enrollmentId === enrollmentId);
    if (index < 0) return;

    const contentProgressItem = contentProgress[index];

    contentProgressItem.status = status;
    contentProgressItem.updatedAt = currentDate;

    contentProgress[index] = contentProgressItem;
  }

  async mainRepositoryFactory(collection, pipeline) {
    switch (collection) {
      case DBCollectionEnum.USERS: {
        return this.userService.aggregate(pipeline);
      }
      case DBCollectionEnum.COURSES: {
        return this.courseService.aggregate(pipeline);
      }
      case DBCollectionEnum.LEARNING_PATHS: {
        return this.learningPathService.aggregate(pipeline);
      }
      default: {
        return [];
      }
    }
  }

  async repositoryFactory(collection, pipeline) {
    switch (collection) {
      case DBCollectionEnum.USERS: {
        return this.userService.aggregate(pipeline);
      }
      case DBCollectionEnum.DEPARTMENTS: {
        return this.departmentService.aggregate(pipeline);
      }
      case DBCollectionEnum.USER_DIRECT_REPORTS: {
        return this.userDirectReportsService.aggregate(pipeline);
      }
      case DBCollectionEnum.LICENSES: {
        return this.licenseService.aggregate(pipeline);
      }
      case DBCollectionEnum.LEARNING_PATH_VERSIONS: {
        return this.learningPathVersionService.aggregate(pipeline);
      }
      case DBCollectionEnum.COURSE_VERSIONS: {
        return this.courseVersionService.aggregate(pipeline);
      }
      default: {
        return [];
      }
    }
  }

  async getCertificateDynamicValue(params) {
    const {
      resultProperties,
      organizationId,
      userId,
      courseId,
      courseVersionId,
      learningPathId,
      learningPathVersionId,
    } = params;

    const currentDate = date().toDate();
    const transformedCertificatePropertiesData = resultProperties.map((item) => {
      const key = ['t', 'i'].some((prefix) => item.key.startsWith(prefix)) ? item.key.slice(1) : item.key;
      const columnSettingParts = item.columnSettingKey ? item.columnSettingKey.split('.') : [null, null];

      return {
        key,
        columnSettingModule: columnSettingParts[0],
        columnSettingKey: item.columnSettingKey,
        type: item.type,
        value: item.value,
        mediaId: item.mediaId ? item.mediaId : null,
        name: item.name,
      };
    });

    const dynamicValue = {};
    for (const item of transformedCertificatePropertiesData) {
      if (item.type === CertificatePropertyTypeEnum.CURRENT_DATE) {
        dynamicValue[item.key] = getDateLocale(item.value, currentDate);
      } else if (item.type === CertificatePropertyTypeEnum.COLUMN_SETTING) {
        const columnSettings = await this.columnSettingService.findColumnSettingWithTemplate(item.columnSettingKey);

        const organizationColumnSettings = await this.organizationColumnSettingService.findColumnSettingWithTemplate(
          organizationId,
          item.columnSettingKey,
        );

        const columnSettingData = columnSettings.length > 0 ? columnSettings : organizationColumnSettings;

        if (item.columnSettingModule === CertificatePropertyModuleEnum.USER) {
          const userData = await this.buildAggregateCertificateAdaptorService.getUserColumnSettingData(
            organizationId,
            userId,
            columnSettings,
            (collection, pipeline) => this.mainRepositoryFactory(collection, pipeline),
            (collection, pipeline) => this.repositoryFactory(collection, pipeline),
          );
          const userValueByKey = this.columnSettingService.getValueByColumnSettingKey(
            item.columnSettingKey,
            userData[0],
            columnSettingData,
          );
          dynamicValue[item.key] = userValueByKey[0];
        } else if (item.columnSettingModule === CertificatePropertyModuleEnum.LEARNING_PATH) {
          const learningPathData = await this.buildAggregateCertificateAdaptorService.getLearningPathColumnSettingData(
            organizationId,
            learningPathId,
            learningPathVersionId,
            columnSettings,
            (collection, pipeline) => this.mainRepositoryFactory(collection, pipeline),
            (collection, pipeline) => this.repositoryFactory(collection, pipeline),
          );
          const getCourseValueByKey = this.columnSettingService.getValueByColumnSettingKey(
            item.columnSettingKey,
            learningPathData[0],
            columnSettingData,
          );
          dynamicValue[item.key] = getCourseValueByKey[0];
        } else if (item.columnSettingModule === CertificatePropertyModuleEnum.COURSE) {
          const courseData = await this.buildAggregateCertificateAdaptorService.getCourseColumnSettingData(
            organizationId,
            courseId,
            courseVersionId,
            columnSettings,
            (collection, pipeline) => this.mainRepositoryFactory(collection, pipeline),
            (collection, pipeline) => this.repositoryFactory(collection, pipeline),
          );

          const getCourseValueByKey = this.columnSettingService.getValueByColumnSettingKey(
            item.columnSettingKey,
            courseData[0],
            columnSettingData,
          );
          dynamicValue[item.key] = getCourseValueByKey[0];
        }
      } else if (item.type === CertificatePropertyTypeEnum.TEXT) {
        dynamicValue[item.key] = item.value;
      } else if (item.type === CertificatePropertyTypeEnum.IMAGE_URL) {
        const media = await this.mediaService.findOne({ id: item.mediaId });
        const organizationStorage = await this.organizationStorageService.findOne({
          organizationId: media.organizationId,
          storageType: OrganizationStorageTypeEnum.RESOURCE,
        });
        const mediaPath = this.mediaService.getMediaURL(media, organizationStorage);
        dynamicValue[item.key] = mediaPath;
      }
    }
    return dynamicValue;
  }

  async buildCourseItemProgressHistoryClassroomMarkResultNotPass(
    enrollmentId,
    courseItemId,
    courseItemName,
    location,
    locationType,
    locationCode,
    classroomLocationEnrollment,
    classroomCriteriaConfig,
  ) {
    const entity = await CourseItemProgressHistory.new({
      enrollmentId,
      courseItemId,
      type: CourseItemTypeEnum.Classroom,
      statusCode: CourseItemStatusCodeEnum.IN_PROGRESS,
      timeSpent: null,
      currentTime: 0,
      maxSeekTime: 0,
      payload: {
        courseItemName,
        status: ClassroomLocationEnrollmentStatusEnum.NOT_PASS,
        location,
        locationType,
        locationCode,
        classroomLocationEnrollmentId: classroomLocationEnrollment.id,
        totalAttended: classroomLocationEnrollment.totalAttended ?? null,
        scoreHomework: classroomLocationEnrollment.scoreHomework ?? null,
        maxAttendance: classroomCriteriaConfig?.maxAttendance ?? null,
        maxScoreHomework: classroomCriteriaConfig?.maxScoreHomework ?? null,
      },
      eventType: CourseItemProgressHistoryEventTypeEnum.MARK_RESULT_CLASSROOM,
      actionBy: CourseItemProgressHistoryActionByEnum.ADMIN,
    });

    return {
      id: entity.id,
      createdAt: entity.createdAt,
      updatedAt: entity.updatedAt,
      deletedAt: entity.deletedAt,
      enrollmentId: entity.enrollmentId,
      courseItemId: entity.courseItemId,
      type: entity.type,
      statusCode: entity.statusCode,
      timeSpent: entity.timeSpent,
      currentTime: entity.currentTime,
      maxSeekTime: entity.maxSeekTime,
      payload: entity.payload,
      eventType: entity.eventType,
      actionBy: entity.actionBy,
    };
  }

  async buildCourseItemProgressHistoryClassroomMarkResultPassed(
    enrollmentId,
    courseItemId,
    courseItemName,
    location,
    locationType,
    locationCode,
    classroomLocationEnrollment,
    classroomCriteriaConfig,
  ) {
    const entity = await CourseItemProgressHistory.new({
      enrollmentId,
      courseItemId,
      type: CourseItemTypeEnum.Classroom,
      statusCode: CourseItemStatusCodeEnum.COMPLETE,
      timeSpent: null,
      currentTime: 0,
      maxSeekTime: 0,
      payload: {
        courseItemName,
        status: ClassroomLocationEnrollmentStatusEnum.PASSED,
        location,
        locationType,
        locationCode,
        classroomLocationEnrollmentId: classroomLocationEnrollment.id,
        totalAttended: classroomLocationEnrollment.totalAttended ?? null,
        scoreHomework: classroomLocationEnrollment.scoreHomework ?? null,
        maxAttendance: classroomCriteriaConfig?.maxAttendance ?? null,
        maxScoreHomework: classroomCriteriaConfig?.maxScoreHomework ?? null,
      },
      eventType: CourseItemProgressHistoryEventTypeEnum.MARK_RESULT_CLASSROOM,
      actionBy: CourseItemProgressHistoryActionByEnum.ADMIN,
    });

    return {
      id: entity.id,
      createdAt: entity.createdAt,
      updatedAt: entity.updatedAt,
      deletedAt: entity.deletedAt,
      enrollmentId: entity.enrollmentId,
      courseItemId: entity.courseItemId,
      type: entity.type,
      statusCode: entity.statusCode,
      timeSpent: entity.timeSpent,
      currentTime: entity.currentTime,
      maxSeekTime: entity.maxSeekTime,
      payload: entity.payload,
      eventType: entity.eventType,
      actionBy: entity.actionBy,
    };
  }
}

export default ClassroomMarkResultUseCase;
