import { MinusOutlined, PlusOutlined } from '@ant-design/icons';
import FontAwesomeIcon from '@iso/components/fontAwesomeIcon';
import React, { useCallback, useEffect, useState } from 'react';

import { farCirclePlay, farFile, farFileLines } from '@iso/components/fontAwesomeIcon/regular';
import { fasFileCircleQuestion, fasSquarePollVertical, fasUsers } from '@iso/components/fontAwesomeIcon/solid';
import { Space } from 'antd';

import { formatDurationShort } from '@utils/format';

import styles from './style.module.css';

const courseItemType = {
  video: 'video',
  quiz: 'quiz',
  article: 'article',
  survey: 'survey',
  classroom: 'classroom',
};

// Private Component
const Lesson = (props) => {
  const { chapter } = props;
  const { name, duration, type, totalScore, totalAttachment, criteriaEnable } = chapter;

  let TypeItemComponent;
  let DetailItemComponent;
  let AttachmentComponent;
  let NameItemComponent;

  NameItemComponent = name;

  if (type === courseItemType.video) {
    TypeItemComponent = <FontAwesomeIcon icon={farCirclePlay} />;
    DetailItemComponent = formatDurationShort(duration);
  } else if (type === courseItemType.quiz) {
    NameItemComponent = criteriaEnable ? `${name} (วัดผล)` : name;
    TypeItemComponent = <FontAwesomeIcon icon={fasFileCircleQuestion} />;
    DetailItemComponent = `${totalScore} ข้อ`;
  } else if (type === courseItemType.article) {
    TypeItemComponent = <FontAwesomeIcon icon={farFileLines} />;
    DetailItemComponent = formatDurationShort(chapter.duration);
  } else if (type === courseItemType.survey) {
    TypeItemComponent = <FontAwesomeIcon icon={fasSquarePollVertical} />;
    DetailItemComponent = 'แบบสำรวจ';
  } else if (type === courseItemType.classroom) {
    NameItemComponent = criteriaEnable ? `${name} วัดผล` : name;
    TypeItemComponent = <FontAwesomeIcon icon={fasUsers} />;
  }

  if (totalAttachment.length > 0) {
    AttachmentComponent = <FontAwesomeIcon icon={farFile} />;
  }

  return (
    <div className={styles.lessonContainer}>
      <div className={styles.lessonIcon}>{TypeItemComponent}</div>

      <div className={styles.lessonContent}>
        {NameItemComponent}

        <Space size={24}>
          <span>{AttachmentComponent}</span>

          <span className={styles.lessonInfo}>{DetailItemComponent}</span>
        </Space>
      </div>
    </div>
  );
};

// Private Component
const Part = (props) => {
  const { part, index, isExpandedAll, onExpandChange } = props;

  const [isExpanded, setIsExpanded] = useState(true);

  const onClickCollapse = () => {
    const value = !isExpanded;
    setIsExpanded(value);
    onExpandChange(index, value);
  };

  useEffect(() => {
    setIsExpanded(isExpandedAll);
  }, [isExpandedAll]);

  return (
    <div className={styles.partContainer}>
      <div className={styles.partTitle}>
        <span className={styles.partTitleIcon} onClick={onClickCollapse}>
          {isExpanded ? <MinusOutlined /> : <PlusOutlined />}
        </span>

        {part.name}
      </div>

      <div className={isExpanded ? styles.partContent : styles.partContentCollapsed}>
        {part.courseItems.map((chapter) => (
          <Lesson chapter={chapter} key={`curriculum-chapter-${chapter.id}`} />
        ))}
      </div>
    </div>
  );
};

const Curriculum = (props) => {
  const { parts = [] } = props;

  const [isExpandedAll, setIsExpandedAll] = useState(true);
  const [itemsExpanded, setItemsExpanded] = useState([]);

  const onClickCollapseAll = () => {
    setIsExpandedAll(!isExpandedAll);
  };

  const onExpandChange = useCallback((index, value) => {
    setItemsExpanded((prev) => {
      prev[index] = value;
      return [...prev];
    });
  }, []);

  useEffect(() => {
    if (itemsExpanded.every((item) => item === true)) {
      setIsExpandedAll(true);
    } else if (itemsExpanded.every((item) => item === false)) {
      setIsExpandedAll(false);
    }
  }, [itemsExpanded]);

  useEffect(() => {
    if (!parts.length) return;
    setItemsExpanded(Array.from({ length: parts.length }, (_) => true));
  }, [parts]);

  return (
    <div className={styles.curriculumContainer}>
      {parts?.map((part) => (
        <Part
          part={part}
          isExpandedAll={isExpandedAll}
          onExpandChange={onExpandChange}
          index={part.id}
          key={`curriculum-part-${part.id}`}
        />
      ))}
    </div>
  );
};

export default Curriculum;
