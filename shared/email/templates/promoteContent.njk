{% macro PromoteContentCard(imageUrl, title, subtitle1, subtitle2 = "", url = "") %}
  <a style="text-decoration: none;" href="{{ url }}">
    <div class="card" style="margin: auto;">
      <div class="card-img-wrapper">
        <img class="card-img" src="{{ imageUrl }}" alt="banner"/>
      </div>
      <div class="card-content">
        <p class="card-type">
          {% for subtitle in subtitle1 %}
            <span>{{ subtitle }}</span>
          {% endfor %}
        </p>
        <p class="card-title">{{ title }}</p>
        <p class="card-subtitle">{{ subtitle2 }}</p>
      </div>
    </div>
  </a>
{% endmacro %}
<div style="width: 687px; max-width: 687px; margin: auto;">
  <div class="title-text">{{ title }}</div>
  <div class="to-text">เรียน คุณ{{ fullName }}</div>
  <div class="description html-template" style="margin-top: 8px">
    {{ description | safe }}
  </div>
  <table class="fixed-table" style="margin-top: 40px">
    {% for packContents in contents %}
      {% if packContents|length === 2 %}
        <tr>
          {% for content in packContents %}
            <td colspan="1">
              {{ PromoteContentCard(content.imageUrl, content.title, content.subtitle1, content.subtitle2, content.url) }}
            </td>
          {% endfor %}
        </tr>
      {% elif packContents|length  === 1 %}
        <tr>
          <td colspan="2">
            {% set content = packContents[0] %}
            {{ PromoteContentCard(content.imageUrl, content.title, content.subtitle1, content.subtitle2, content.url) }}
          </td>
        </tr>
      {% endif %}
    {% endfor %}
  </table>
  <div class="button-container bg-primary" style="min-width: 116px; margin-top: 40px;">
    <a href="{{ url }}" class="button-text-link">{{ textButton }}</a>
  </div>
</div>